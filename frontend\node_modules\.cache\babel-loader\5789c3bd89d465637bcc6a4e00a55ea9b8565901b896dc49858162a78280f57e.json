{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\ai-hr-agent\\\\frontend\\\\src\\\\components\\\\FeatureSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Brain, Search, CheckCircle, Zap, ArrowRight, Shield, Sparkles, Target, TrendingUp, Award, Database, Globe, Cpu } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [isLoaded, setIsLoaded] = useState(true);\n  const handleGetStarted = () => {\n    // Navigation logic would go here\n    console.log('Navigate to analysis page');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"floating-particles\",\n      children: [...Array(12)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 8}s`,\n          animationDuration: `${8 + Math.random() * 4}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      className: \"relative z-10 min-h-screen flex items-center justify-center px-4 section-glow\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: isLoaded ? 1 : 0\n      },\n      transition: {\n        duration: 1.2\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            delay: 0.2\n          },\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200,\n              delay: 0.1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-cyan-300\",\n              children: \"Next-Generation AI Talent Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"TalentSphere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Revolutionary Multi-Agent AI Platform for Comprehensive Talent Discovery, Deep Analysis, and Strategic Hiring Intelligence with Unparalleled Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 40\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            delay: 0.6\n          },\n          className: \"flex justify-center items-center mb-16\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGetStarted,\n            className: \"btn-primary text-lg px-10 py-5 group hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(Brain, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), \"Discover Talent\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              className: \"w-6 h-6 group-hover:translate-x-1 transition-transform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 40\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            delay: 0.8\n          },\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass-card p-8 rounded-2xl text-center hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container mx-auto\",\n              children: /*#__PURE__*/_jsxDEV(Brain, {\n                className: \"w-8 h-8 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-3\",\n              children: \"AI-Powered Intelligence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm leading-relaxed\",\n              children: \"Advanced multi-agent system for comprehensive talent evaluation with unprecedented depth and accuracy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass-card p-8 rounded-2xl text-center hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container mx-auto\",\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"w-8 h-8 text-purple-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-3\",\n              children: \"Deep Research Engine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm leading-relaxed\",\n              children: \"Automated research across LinkedIn, GitHub, and professional platforms for complete talent profiles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass-card p-8 rounded-2xl text-center hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container mx-auto\",\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                className: \"w-8 h-8 text-cyan-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-3\",\n              children: \"Strategic Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm leading-relaxed\",\n              children: \"Comprehensive analysis with rankings, insights, and strategic hiring recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeatureSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4 section-glow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            whileInView: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(Cpu, {\n              className: \"w-4 h-4 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-purple-300\",\n              children: \"AI-Powered Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-6xl font-bold mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"How TalentSphere Works\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Experience our revolutionary multi-agent AI system as it processes talent through advanced intelligence pipelines, research networks, and validation protocols\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"glass-intense p-12 lg:p-16 rounded-3xl hover-glow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n              initial: {\n                scale: 0\n              },\n              whileInView: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 200\n              },\n              viewport: {\n                once: true\n              },\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                className: \"w-4 h-4 text-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-300\",\n                children: \"Performance Excellence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-4xl md:text-5xl font-bold mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"gradient-text\",\n                children: \"Unmatched Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-gray-300 max-width-3xl mx-auto\",\n              children: \"Built for speed, precision, and scalability with cutting-edge AI optimizations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n            children: [{\n              value: \"15x\",\n              label: \"Faster Analysis\",\n              icon: Zap,\n              color: \"text-yellow-400\"\n            }, {\n              value: \"98%\",\n              label: \"Accuracy Rate\",\n              icon: Target,\n              color: \"text-green-400\"\n            }, {\n              value: \"7\",\n              label: \"AI Agents\",\n              icon: Brain,\n              color: \"text-purple-400\"\n            }, {\n              value: \"100+\",\n              label: \"Data Points\",\n              icon: Database,\n              color: \"text-cyan-400\"\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.1,\n                duration: 0.8\n              },\n              viewport: {\n                once: true\n              },\n              className: \"stat-card hover-lift\",\n              children: [/*#__PURE__*/_jsxDEV(stat.icon, {\n                className: `w-8 h-8 ${stat.color} mx-auto mb-4`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-value\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-label\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            whileInView: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(Award, {\n              className: \"w-4 h-4 text-amber-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-amber-300\",\n              children: \"Advanced Capabilities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-6xl font-bold mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Revolutionary Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Comprehensive suite of AI-powered tools designed for the future of talent acquisition\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: enhancedFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 40\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"feature-card hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-8 h-8 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-4\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 leading-relaxed mb-6\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: feature.benefits.map((benefit, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-300\",\n                  children: benefit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center glass-intense p-12 lg:p-20 rounded-3xl hover-glow section-glow\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            whileInView: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-cyan-300\",\n              children: \"Ready to Transform?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-6xl font-bold mb-8\",\n            children: [\"Revolutionize Your \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Talent Strategy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 34\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed\",\n            children: \"Experience the future of talent discovery with our AI-powered platform. Unlock unprecedented insights and make data-driven hiring decisions with confidence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGetStarted,\n              className: \"btn-primary text-xl px-12 py-6 group hover-lift\",\n              children: [/*#__PURE__*/_jsxDEV(Brain, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), \"Start Discovery\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                className: \"w-6 h-6 group-hover:translate-x-1 transition-transform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-secondary text-xl px-12 py-6 hover-glow\",\n              children: [/*#__PURE__*/_jsxDEV(Globe, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), \"Schedule Demo\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"relative z-10 py-16 border-t border-white/10 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold gradient-text mb-3\",\n              children: \"TalentSphere AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-lg\",\n              children: \"Revolutionary AI-Powered Talent Analysis Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center gap-8 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"15x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"98%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Accurate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"AI Agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"100+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Data Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-500 text-sm\",\n            children: \"\\xA9 2024 TalentSphere AI. Powered by Advanced Multi-Agent Intelligence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"rghfZOAcWRMXS0vY2ff9+tyYTSY=\");\n_c = HomePage;\nconst FeatureSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative z-10 py-32 px-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        viewport: {\n          once: true\n        },\n        className: \"text-center mb-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n          initial: {\n            scale: 0\n          },\n          whileInView: {\n            scale: 1\n          },\n          transition: {\n            type: \"spring\",\n            stiffness: 200\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(Brain, {\n            className: \"w-4 h-4 text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-blue-300\",\n            children: \"AI Excellence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-6xl font-bold mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Advanced Intelligence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Experience cutting-edge AI technology with our comprehensive platform designed for precision and performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        viewport: {\n          once: true\n        },\n        className: \"glass-intense p-8 lg:p-12 rounded-3xl hover-glow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-3xl font-bold text-center mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text\",\n            children: \"Technical Excellence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: technicalSpecs.map((spec, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-center glass p-6 rounded-2xl hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-16 h-16 rounded-2xl ${spec.bgColor} flex items-center justify-center mx-auto mb-6`,\n              children: /*#__PURE__*/_jsxDEV(spec.icon, {\n                className: `w-8 h-8 ${spec.iconColor}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: spec.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-4xl font-bold gradient-text mb-3\",\n              children: spec.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 leading-relaxed\",\n              children: spec.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mt-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass p-6 rounded-2xl max-w-4xl mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-2xl font-bold mb-6 gradient-text\",\n              children: \"Advanced Capabilities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Real-time processing & analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Multi-platform integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Advanced security protocols\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Scalable architecture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Intelligent automation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Performance optimization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 358,\n    columnNumber: 5\n  }, this);\n};\n_c2 = FeatureSection;\nconst enhancedFeatures = [{\n  icon: Brain,\n  title: \"Multi-Agent AI System\",\n  description: \"Seven specialized AI agents working in parallel for comprehensive talent analysis with unprecedented depth and accuracy.\",\n  benefits: [\"GPT-4 powered intelligence\", \"Parallel processing architecture\", \"98% accuracy rate\", \"Semantic understanding\"]\n}, {\n  icon: Search,\n  title: \"Deep Research Engine\",\n  description: \"Automated research across LinkedIn, GitHub, and professional platforms to build complete talent profiles with strategic insights.\",\n  benefits: [\"Multi-platform intelligence\", \"Real-time data collection\", \"Professional verification\", \"Social proof analysis\"]\n}, {\n  icon: Shield,\n  title: \"Identity Validation\",\n  description: \"Advanced validation algorithms ensure talent authenticity through cross-platform verification and consistency analysis.\",\n  benefits: [\"Multi-factor verification\", \"Fraud detection AI\", \"Consistency analysis\", \"Confidence scoring\"]\n}, {\n  icon: Target,\n  title: \"Precision Matching\",\n  description: \"Intelligent scoring and ranking against job requirements with semantic analysis and cultural fit assessment.\",\n  benefits: [\"Semantic matching\", \"Cultural fit analysis\", \"Skills gap identification\", \"Growth potential scoring\"]\n}, {\n  icon: TrendingUp,\n  title: \"Predictive Analytics\",\n  description: \"Advanced analytics to predict candidate success, retention probability, and career trajectory patterns.\",\n  benefits: [\"Success prediction\", \"Retention modeling\", \"Career trajectory analysis\", \"Performance forecasting\"]\n}, {\n  icon: Globe,\n  title: \"Global Talent Pool\",\n  description: \"Access worldwide talent with localized insights, cultural considerations, and regional expertise mapping.\",\n  benefits: [\"Global reach\", \"Cultural intelligence\", \"Regional expertise\", \"Diversity insights\"]\n}];\nconst technicalSpecs = [{\n  icon: Zap,\n  title: \"Processing Speed\",\n  value: \"15x\",\n  description: \"Faster than traditional recruitment methods with parallel AI processing\",\n  bgColor: \"bg-yellow-500/20\",\n  iconColor: \"text-yellow-400\"\n}, {\n  icon: Database,\n  title: \"Data Points\",\n  value: \"100+\",\n  description: \"Comprehensive data extraction per candidate profile for complete analysis\",\n  bgColor: \"bg-cyan-500/20\",\n  iconColor: \"text-cyan-400\"\n}, {\n  icon: Brain,\n  title: \"AI Agents\",\n  value: \"7\",\n  description: \"Specialized multi-agent system for comprehensive talent evaluation\",\n  bgColor: \"bg-purple-500/20\",\n  iconColor: \"text-purple-400\"\n}, {\n  icon: Globe,\n  title: \"Platforms\",\n  value: \"10+\",\n  description: \"Research sources across professional networks and platforms\",\n  bgColor: \"bg-blue-500/20\",\n  iconColor: \"text-blue-400\"\n}];\nexport default HomePage;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c2, \"FeatureSection\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Brain", "Search", "CheckCircle", "Zap", "ArrowRight", "Shield", "<PERSON><PERSON><PERSON>", "Target", "TrendingUp", "Award", "Database", "Globe", "Cpu", "jsxDEV", "_jsxDEV", "HomePage", "_s", "isLoaded", "setIsLoaded", "handleGetStarted", "console", "log", "className", "children", "Array", "map", "_", "i", "style", "left", "Math", "random", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "section", "initial", "opacity", "animate", "transition", "duration", "div", "y", "delay", "scale", "type", "stiffness", "onClick", "FeatureSection", "whileInView", "viewport", "once", "value", "label", "icon", "color", "stat", "index", "enhancedFeatures", "feature", "title", "description", "benefits", "benefit", "_c", "technicalSpecs", "spec", "bgColor", "iconColor", "_c2", "$RefreshReg$"], "sources": ["D:/Projects/ai-hr-agent/frontend/src/components/FeatureSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { \r\n  Brain, \r\n  Search, \r\n  CheckCircle, \r\n  BarChart3, \r\n  Users, \r\n  Zap,\r\n  ArrowRight,\r\n  Star,\r\n  Shield,\r\n  Clock,\r\n  Sparkles,\r\n  Target,\r\n  TrendingUp,\r\n  Award,\r\n  Database,\r\n  Globe,\r\n  Cpu\r\n} from 'lucide-react';\r\n\r\nconst HomePage = () => {\r\n  const [isLoaded, setIsLoaded] = useState(true);\r\n\r\n  const handleGetStarted = () => {\r\n    // Navigation logic would go here\r\n    console.log('Navigate to analysis page');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen relative overflow-hidden\">\r\n      {/* Floating particles effect */}\r\n      <div className=\"floating-particles\">\r\n        {[...Array(12)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className=\"particle\"\r\n            style={{\r\n              left: `${Math.random() * 100}%`,\r\n              animationDelay: `${Math.random() * 8}s`,\r\n              animationDuration: `${8 + Math.random() * 4}s`\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n      \r\n      {/* Hero Section */}\r\n      <motion.section \r\n        className=\"relative z-10 min-h-screen flex items-center justify-center px-4 section-glow\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: isLoaded ? 1 : 0 }}\r\n        transition={{ duration: 1.2 }}\r\n      >\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1, delay: 0.2 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200, delay: 0.1 }}\r\n            >\r\n              <Sparkles className=\"w-4 h-4 text-cyan-400\" />\r\n              <span className=\"text-sm font-medium text-cyan-300\">Next-Generation AI Talent Analysis</span>\r\n            </motion.div>\r\n            \r\n            <h1 className=\"hero-title text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight\">\r\n              <span className=\"gradient-text\">TalentSphere</span>\r\n              <br />\r\n              <span className=\"text-white\">AI</span>\r\n            </h1>\r\n            <p className=\"hero-subtitle text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed\">\r\n              Revolutionary Multi-Agent AI Platform for Comprehensive Talent Discovery, \r\n              Deep Analysis, and Strategic Hiring Intelligence with Unparalleled Insights\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 40 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1, delay: 0.6 }}\r\n            className=\"flex justify-center items-center mb-16\"\r\n          >\r\n            <button\r\n              onClick={handleGetStarted}\r\n              className=\"btn-primary text-lg px-10 py-5 group hover-lift\"\r\n            >\r\n              <Brain className=\"w-6 h-6\" />\r\n              Discover Talent\r\n              <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\r\n            </button>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 40 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1, delay: 0.8 }}\r\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto\"\r\n          >\r\n            <div className=\"glass-card p-8 rounded-2xl text-center hover-lift\">\r\n              <div className=\"icon-container mx-auto\">\r\n                <Brain className=\"w-8 h-8 text-blue-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold mb-3\">AI-Powered Intelligence</h3>\r\n              <p className=\"text-gray-400 text-sm leading-relaxed\">Advanced multi-agent system for comprehensive talent evaluation with unprecedented depth and accuracy</p>\r\n            </div>\r\n            <div className=\"glass-card p-8 rounded-2xl text-center hover-lift\">\r\n              <div className=\"icon-container mx-auto\">\r\n                <Search className=\"w-8 h-8 text-purple-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold mb-3\">Deep Research Engine</h3>\r\n              <p className=\"text-gray-400 text-sm leading-relaxed\">Automated research across LinkedIn, GitHub, and professional platforms for complete talent profiles</p>\r\n            </div>\r\n            <div className=\"glass-card p-8 rounded-2xl text-center hover-lift\">\r\n              <div className=\"icon-container mx-auto\">\r\n                <Target className=\"w-8 h-8 text-cyan-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold mb-3\">Strategic Insights</h3>\r\n              <p className=\"text-gray-400 text-sm leading-relaxed\">Comprehensive analysis with rankings, insights, and strategic hiring recommendations</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </motion.section>\r\n\r\n      {/* Features Section */}\r\n      <FeatureSection />\r\n\r\n      {/* Workflow Animation Section */}\r\n      <section className=\"relative z-10 py-32 px-4 section-glow\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-20\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              whileInView={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <Cpu className=\"w-4 h-4 text-purple-400\" />\r\n              <span className=\"text-sm font-medium text-purple-300\">AI-Powered Workflow</span>\r\n            </motion.div>\r\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n              <span className=\"gradient-text\">How TalentSphere Works</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\r\n              Experience our revolutionary multi-agent AI system as it processes talent through \r\n              advanced intelligence pipelines, research networks, and validation protocols\r\n            </p>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Performance Stats */}\r\n      <section className=\"relative z-10 py-32 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"glass-intense p-12 lg:p-16 rounded-3xl hover-glow\"\r\n          >\r\n            <div className=\"text-center mb-16\">\r\n              <motion.div\r\n                className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n                initial={{ scale: 0 }}\r\n                whileInView={{ scale: 1 }}\r\n                transition={{ type: \"spring\", stiffness: 200 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                <TrendingUp className=\"w-4 h-4 text-green-400\" />\r\n                <span className=\"text-sm font-medium text-green-300\">Performance Excellence</span>\r\n              </motion.div>\r\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\r\n                <span className=\"gradient-text\">Unmatched Performance</span>\r\n              </h2>\r\n              <p className=\"text-xl text-gray-300 max-width-3xl mx-auto\">\r\n                Built for speed, precision, and scalability with cutting-edge AI optimizations\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\r\n              {[\r\n                { value: \"15x\", label: \"Faster Analysis\", icon: Zap, color: \"text-yellow-400\" },\r\n                { value: \"98%\", label: \"Accuracy Rate\", icon: Target, color: \"text-green-400\" },\r\n                { value: \"7\", label: \"AI Agents\", icon: Brain, color: \"text-purple-400\" },\r\n                { value: \"100+\", label: \"Data Points\", icon: Database, color: \"text-cyan-400\" }\r\n              ].map((stat, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 30 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1, duration: 0.8 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"stat-card hover-lift\"\r\n                >\r\n                  <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-4`} />\r\n                  <div className=\"stat-value\">{stat.value}</div>\r\n                  <div className=\"stat-label\">{stat.label}</div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Enhanced Features Grid */}\r\n      <section className=\"relative z-10 py-32 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-20\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              whileInView={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <Award className=\"w-4 h-4 text-amber-400\" />\r\n              <span className=\"text-sm font-medium text-amber-300\">Advanced Capabilities</span>\r\n            </motion.div>\r\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n              <span className=\"gradient-text\">Revolutionary Features</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\r\n              Comprehensive suite of AI-powered tools designed for the future of talent acquisition\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {enhancedFeatures.map((feature, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 40 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"feature-card hover-lift\"\r\n              >\r\n                <div className=\"icon-container\">\r\n                  <feature.icon className=\"w-8 h-8 text-blue-400\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold mb-4\">{feature.title}</h3>\r\n                <p className=\"text-gray-400 leading-relaxed mb-6\">{feature.description}</p>\r\n                <div className=\"space-y-3\">\r\n                  {feature.benefits.map((benefit, i) => (\r\n                    <div key={i} className=\"flex items-center gap-3\">\r\n                      <CheckCircle className=\"w-4 h-4 text-green-400 flex-shrink-0\" />\r\n                      <span className=\"text-sm text-gray-300\">{benefit}</span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"relative z-10 py-32 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center glass-intense p-12 lg:p-20 rounded-3xl hover-glow section-glow\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              whileInView={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <Sparkles className=\"w-4 h-4 text-cyan-400\" />\r\n              <span className=\"text-sm font-medium text-cyan-300\">Ready to Transform?</span>\r\n            </motion.div>\r\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n              Revolutionize Your <span className=\"gradient-text\">Talent Strategy</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed\">\r\n              Experience the future of talent discovery with our AI-powered platform. \r\n              Unlock unprecedented insights and make data-driven hiring decisions with confidence.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\r\n              <button\r\n                onClick={handleGetStarted}\r\n                className=\"btn-primary text-xl px-12 py-6 group hover-lift\"\r\n              >\r\n                <Brain className=\"w-6 h-6\" />\r\n                Start Discovery\r\n                <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\r\n              </button>\r\n              <button className=\"btn-secondary text-xl px-12 py-6 hover-glow\">\r\n                <Globe className=\"w-6 h-6\" />\r\n                Schedule Demo\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"relative z-10 py-16 border-t border-white/10 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center\">\r\n            <div className=\"mb-8\">\r\n              <h3 className=\"text-3xl font-bold gradient-text mb-3\">TalentSphere AI</h3>\r\n              <p className=\"text-gray-400 text-lg\">Revolutionary AI-Powered Talent Analysis Platform</p>\r\n            </div>\r\n            <div className=\"flex justify-center gap-8 mb-8\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">15x</div>\r\n                <div className=\"text-sm text-gray-500\">Faster</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">98%</div>\r\n                <div className=\"text-sm text-gray-500\">Accurate</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">7</div>\r\n                <div className=\"text-sm text-gray-500\">AI Agents</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">100+</div>\r\n                <div className=\"text-sm text-gray-500\">Data Points</div>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-gray-500 text-sm\">\r\n              © 2024 TalentSphere AI. Powered by Advanced Multi-Agent Intelligence.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst FeatureSection = () => {\r\n  return (\r\n    <section className=\"relative z-10 py-32 px-4\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 50 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-20\"\r\n        >\r\n          <motion.div\r\n            className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n            initial={{ scale: 0 }}\r\n            whileInView={{ scale: 1 }}\r\n            transition={{ type: \"spring\", stiffness: 200 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <Brain className=\"w-4 h-4 text-blue-400\" />\r\n            <span className=\"text-sm font-medium text-blue-300\">AI Excellence</span>\r\n          </motion.div>\r\n          <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n            <span className=\"gradient-text\">Advanced Intelligence</span>\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\r\n            Experience cutting-edge AI technology with our comprehensive platform designed for precision and performance\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Technical Excellence Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 50 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"glass-intense p-8 lg:p-12 rounded-3xl hover-glow\"\r\n        >\r\n          <h3 className=\"text-3xl font-bold text-center mb-12\">\r\n            <span className=\"gradient-text\">Technical Excellence</span>\r\n          </h3>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            {technicalSpecs.map((spec, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-center glass p-6 rounded-2xl hover-lift\"\r\n              >\r\n                <div className={`w-16 h-16 rounded-2xl ${spec.bgColor} flex items-center justify-center mx-auto mb-6`}>\r\n                  <spec.icon className={`w-8 h-8 ${spec.iconColor}`} />\r\n                </div>\r\n                <h4 className=\"text-xl font-semibold mb-3\">{spec.title}</h4>\r\n                <p className=\"text-4xl font-bold gradient-text mb-3\">{spec.value}</p>\r\n                <p className=\"text-sm text-gray-400 leading-relaxed\">{spec.description}</p>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mt-12\"\r\n          >\r\n            <div className=\"glass p-6 rounded-2xl max-w-4xl mx-auto\">\r\n              <h4 className=\"text-2xl font-bold mb-6 gradient-text\">Advanced Capabilities</h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\r\n                  <span className=\"text-gray-300\">Real-time processing & analysis</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\r\n                  <span className=\"text-gray-300\">Multi-platform integration</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\r\n                  <span className=\"text-gray-300\">Advanced security protocols</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\r\n                  <span className=\"text-gray-300\">Scalable architecture</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\r\n                  <span className=\"text-gray-300\">Intelligent automation</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\r\n                  <span className=\"text-gray-300\">Performance optimization</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nconst enhancedFeatures = [\r\n  {\r\n    icon: Brain,\r\n    title: \"Multi-Agent AI System\",\r\n    description: \"Seven specialized AI agents working in parallel for comprehensive talent analysis with unprecedented depth and accuracy.\",\r\n    benefits: [\r\n      \"GPT-4 powered intelligence\",\r\n      \"Parallel processing architecture\",\r\n      \"98% accuracy rate\",\r\n      \"Semantic understanding\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Search,\r\n    title: \"Deep Research Engine\",\r\n    description: \"Automated research across LinkedIn, GitHub, and professional platforms to build complete talent profiles with strategic insights.\",\r\n    benefits: [\r\n      \"Multi-platform intelligence\",\r\n      \"Real-time data collection\",\r\n      \"Professional verification\",\r\n      \"Social proof analysis\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Shield,\r\n    title: \"Identity Validation\",\r\n    description: \"Advanced validation algorithms ensure talent authenticity through cross-platform verification and consistency analysis.\",\r\n    benefits: [\r\n      \"Multi-factor verification\",\r\n      \"Fraud detection AI\",\r\n      \"Consistency analysis\",\r\n      \"Confidence scoring\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Target,\r\n    title: \"Precision Matching\",\r\n    description: \"Intelligent scoring and ranking against job requirements with semantic analysis and cultural fit assessment.\",\r\n    benefits: [\r\n      \"Semantic matching\",\r\n      \"Cultural fit analysis\",\r\n      \"Skills gap identification\",\r\n      \"Growth potential scoring\"\r\n    ]\r\n  },\r\n  {\r\n    icon: TrendingUp,\r\n    title: \"Predictive Analytics\",\r\n    description: \"Advanced analytics to predict candidate success, retention probability, and career trajectory patterns.\",\r\n    benefits: [\r\n      \"Success prediction\",\r\n      \"Retention modeling\",\r\n      \"Career trajectory analysis\",\r\n      \"Performance forecasting\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Globe,\r\n    title: \"Global Talent Pool\",\r\n    description: \"Access worldwide talent with localized insights, cultural considerations, and regional expertise mapping.\",\r\n    benefits: [\r\n      \"Global reach\",\r\n      \"Cultural intelligence\",\r\n      \"Regional expertise\",\r\n      \"Diversity insights\"\r\n    ]\r\n  }\r\n];\r\n\r\nconst technicalSpecs = [\r\n  {\r\n    icon: Zap,\r\n    title: \"Processing Speed\",\r\n    value: \"15x\",\r\n    description: \"Faster than traditional recruitment methods with parallel AI processing\",\r\n    bgColor: \"bg-yellow-500/20\",\r\n    iconColor: \"text-yellow-400\"\r\n  },\r\n  {\r\n    icon: Database,\r\n    title: \"Data Points\",\r\n    value: \"100+\",\r\n    description: \"Comprehensive data extraction per candidate profile for complete analysis\",\r\n    bgColor: \"bg-cyan-500/20\",\r\n    iconColor: \"text-cyan-400\"\r\n  },\r\n  {\r\n    icon: Brain,\r\n    title: \"AI Agents\",\r\n    value: \"7\",\r\n    description: \"Specialized multi-agent system for comprehensive talent evaluation\",\r\n    bgColor: \"bg-purple-500/20\",\r\n    iconColor: \"text-purple-400\"\r\n  },\r\n  {\r\n    icon: Globe,\r\n    title: \"Platforms\",\r\n    value: \"10+\",\r\n    description: \"Research sources across professional networks and platforms\",\r\n    bgColor: \"bg-blue-500/20\",\r\n    iconColor: \"text-blue-400\"\r\n  }\r\n];\r\n\r\nexport default HomePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,KAAK,EACLC,MAAM,EACNC,WAAW,EAGXC,GAAG,EACHC,UAAU,EAEVC,MAAM,EAENC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMqB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EAC1C,CAAC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAEpDT,OAAA;MAAKQ,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChC,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBb,OAAA;QAEEQ,SAAS,EAAC,UAAU;QACpBM,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,cAAc,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCE,iBAAiB,EAAE,GAAG,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GANGJ,CAAC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvB,OAAA,CAACf,MAAM,CAACuC,OAAO;MACbhB,SAAS,EAAC,+EAA+E;MACzFiB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAEvB,QAAQ,GAAG,CAAC,GAAG;MAAE,CAAE;MACvCyB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAApB,QAAA,eAE9BT,OAAA;QAAKQ,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CT,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAI,CAAE;UACxCxB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAEhBT,OAAA,CAACf,MAAM,CAAC6C,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBN,OAAO,EAAE;cAAEM,KAAK,EAAE;YAAE,CAAE;YACtBL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE,GAAG;cAAEH,KAAK,EAAE;YAAI,CAAE;YAAAvB,QAAA,gBAE3DT,OAAA,CAACR,QAAQ;cAACgB,SAAS,EAAC;YAAuB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CvB,OAAA;cAAMQ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAkC;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEbvB,OAAA;YAAIQ,SAAS,EAAC,0EAA0E;YAAAC,QAAA,gBACtFT,OAAA;cAAMQ,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDvB,OAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNvB,OAAA;cAAMQ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACLvB,OAAA;YAAGQ,SAAS,EAAC,mGAAmG;YAAAC,QAAA,EAAC;UAGjH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbvB,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAI,CAAE;UACxCxB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eAElDT,OAAA;YACEoC,OAAO,EAAE/B,gBAAiB;YAC1BG,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAE3DT,OAAA,CAACd,KAAK;cAACsB,SAAS,EAAC;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAE7B,eAAAvB,OAAA,CAACV,UAAU;cAACkB,SAAS,EAAC;YAAwD;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEbvB,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAI,CAAE;UACxCxB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBAEnET,OAAA;YAAKQ,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChET,OAAA;cAAKQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCT,OAAA,CAACd,KAAK;gBAACsB,SAAS,EAAC;cAAuB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNvB,OAAA;cAAIQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAuB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEvB,OAAA;cAAGQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAqG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3J,CAAC,eACNvB,OAAA;YAAKQ,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChET,OAAA;cAAKQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCT,OAAA,CAACb,MAAM;gBAACqB,SAAS,EAAC;cAAyB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNvB,OAAA;cAAIQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAoB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEvB,OAAA;cAAGQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAmG;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzJ,CAAC,eACNvB,OAAA;YAAKQ,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChET,OAAA;cAAKQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCT,OAAA,CAACP,MAAM;gBAACe,SAAS,EAAC;cAAuB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNvB,OAAA;cAAIQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAkB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DvB,OAAA;cAAGQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAoF;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1I,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBvB,OAAA,CAACqC,cAAc;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGlBvB,OAAA;MAASQ,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACxDT,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCT,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BU,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBhC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BT,OAAA,CAACf,MAAM,CAAC6C,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBK,WAAW,EAAE;cAAEL,KAAK,EAAE;YAAE,CAAE;YAC1BL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC/CI,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA/B,QAAA,gBAEzBT,OAAA,CAACF,GAAG;cAACU,SAAS,EAAC;YAAyB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CvB,OAAA;cAAMQ,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAmB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACbvB,OAAA;YAAIQ,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACjDT,OAAA;cAAMQ,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACLvB,OAAA;YAAGQ,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAGvE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvB,OAAA;MAASQ,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CT,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCT,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BU,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBhC,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7DT,OAAA;YAAKQ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCT,OAAA,CAACf,MAAM,CAAC6C,GAAG;cACTtB,SAAS,EAAC,+GAA+G;cACzHiB,OAAO,EAAE;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cACtBK,WAAW,EAAE;gBAAEL,KAAK,EAAE;cAAE,CAAE;cAC1BL,UAAU,EAAE;gBAAEM,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE;cAAI,CAAE;cAC/CI,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cAAA/B,QAAA,gBAEzBT,OAAA,CAACN,UAAU;gBAACc,SAAS,EAAC;cAAwB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDvB,OAAA;gBAAMQ,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACbvB,OAAA;cAAIQ,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eACjDT,OAAA;gBAAMQ,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAqB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACLvB,OAAA;cAAGQ,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENvB,OAAA;YAAKQ,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnD,CACC;cAAEgC,KAAK,EAAE,KAAK;cAAEC,KAAK,EAAE,iBAAiB;cAAEC,IAAI,EAAEtD,GAAG;cAAEuD,KAAK,EAAE;YAAkB,CAAC,EAC/E;cAAEH,KAAK,EAAE,KAAK;cAAEC,KAAK,EAAE,eAAe;cAAEC,IAAI,EAAElD,MAAM;cAAEmD,KAAK,EAAE;YAAiB,CAAC,EAC/E;cAAEH,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,WAAW;cAAEC,IAAI,EAAEzD,KAAK;cAAE0D,KAAK,EAAE;YAAkB,CAAC,EACzE;cAAEH,KAAK,EAAE,MAAM;cAAEC,KAAK,EAAE,aAAa;cAAEC,IAAI,EAAE/C,QAAQ;cAAEgD,KAAK,EAAE;YAAgB,CAAC,CAChF,CAACjC,GAAG,CAAC,CAACkC,IAAI,EAAEC,KAAK,kBAChB9C,OAAA,CAACf,MAAM,CAAC6C,GAAG;cAETL,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cAC/BO,WAAW,EAAE;gBAAEZ,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAE,CAAE;cAClCH,UAAU,EAAE;gBAAEI,KAAK,EAAEc,KAAK,GAAG,GAAG;gBAAEjB,QAAQ,EAAE;cAAI,CAAE;cAClDU,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBhC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBAEhCT,OAAA,CAAC6C,IAAI,CAACF,IAAI;gBAACnC,SAAS,EAAE,WAAWqC,IAAI,CAACD,KAAK;cAAgB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DvB,OAAA;gBAAKQ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEoC,IAAI,CAACJ;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CvB,OAAA;gBAAKQ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEoC,IAAI,CAACH;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GATzCuB,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvB,OAAA;MAASQ,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CT,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCT,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BU,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBhC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BT,OAAA,CAACf,MAAM,CAAC6C,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBK,WAAW,EAAE;cAAEL,KAAK,EAAE;YAAE,CAAE;YAC1BL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC/CI,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA/B,QAAA,gBAEzBT,OAAA,CAACL,KAAK;cAACa,SAAS,EAAC;YAAwB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5CvB,OAAA;cAAMQ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAqB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACbvB,OAAA;YAAIQ,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACjDT,OAAA;cAAMQ,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACLvB,OAAA;YAAGQ,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbvB,OAAA;UAAKQ,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEsC,gBAAgB,CAACpC,GAAG,CAAC,CAACqC,OAAO,EAAEF,KAAK,kBACnC9C,OAAA,CAACf,MAAM,CAAC6C,GAAG;YAETL,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAG,CAAE;YAC/BO,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEc,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBhC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAEnCT,OAAA;cAAKQ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BT,OAAA,CAACgD,OAAO,CAACL,IAAI;gBAACnC,SAAS,EAAC;cAAuB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNvB,OAAA;cAAIQ,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEuC,OAAO,CAACC;YAAK;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5DvB,OAAA;cAAGQ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEuC,OAAO,CAACE;YAAW;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EvB,OAAA;cAAKQ,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBuC,OAAO,CAACG,QAAQ,CAACxC,GAAG,CAAC,CAACyC,OAAO,EAAEvC,CAAC,kBAC/Bb,OAAA;gBAAaQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBAC9CT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE2C;gBAAO;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFhDV,CAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAnBDuB,KAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvB,OAAA;MAASQ,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CT,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCT,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BU,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBhC,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBAEtFT,OAAA,CAACf,MAAM,CAAC6C,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBK,WAAW,EAAE;cAAEL,KAAK,EAAE;YAAE,CAAE;YAC1BL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC/CI,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA/B,QAAA,gBAEzBT,OAAA,CAACR,QAAQ;cAACgB,SAAS,EAAC;YAAuB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CvB,OAAA;cAAMQ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAmB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACbvB,OAAA;YAAIQ,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,qBAC/B,eAAAT,OAAA;cAAMQ,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACLvB,OAAA;YAAGQ,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAG7E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJvB,OAAA;YAAKQ,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1ET,OAAA;cACEoC,OAAO,EAAE/B,gBAAiB;cAC1BG,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAE3DT,OAAA,CAACd,KAAK;gBAACsB,SAAS,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE7B,eAAAvB,OAAA,CAACV,UAAU;gBAACkB,SAAS,EAAC;cAAwD;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACTvB,OAAA;cAAQQ,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC7DT,OAAA,CAACH,KAAK;gBAACW,SAAS,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvB,OAAA;MAAQQ,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eACnET,OAAA;QAAKQ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCT,OAAA;UAAKQ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BT,OAAA;YAAKQ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBT,OAAA;cAAIQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAe;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EvB,OAAA;cAAGQ,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACNvB,OAAA;YAAKQ,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CT,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BT,OAAA;gBAAKQ,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DvB,OAAA;gBAAKQ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNvB,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BT,OAAA;gBAAKQ,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DvB,OAAA;gBAAKQ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNvB,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BT,OAAA;gBAAKQ,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzDvB,OAAA;gBAAKQ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNvB,OAAA;cAAKQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BT,OAAA;gBAAKQ,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5DvB,OAAA;gBAAKQ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvB,OAAA;YAAKQ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACrB,EAAA,CA3UID,QAAQ;AAAAoD,EAAA,GAARpD,QAAQ;AA6Ud,MAAMoC,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACErC,OAAA;IAASQ,SAAS,EAAC,0BAA0B;IAAAC,QAAA,eAC3CT,OAAA;MAAKQ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCT,OAAA,CAACf,MAAM,CAAC6C,GAAG;QACTL,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BO,WAAW,EAAE;UAAEZ,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BU,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QACzBhC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BT,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTtB,SAAS,EAAC,+GAA+G;UACzHiB,OAAO,EAAE;YAAEQ,KAAK,EAAE;UAAE,CAAE;UACtBK,WAAW,EAAE;YAAEL,KAAK,EAAE;UAAE,CAAE;UAC1BL,UAAU,EAAE;YAAEM,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC/CI,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAA/B,QAAA,gBAEzBT,OAAA,CAACd,KAAK;YAACsB,SAAS,EAAC;UAAuB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CvB,OAAA;YAAMQ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAa;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACbvB,OAAA;UAAIQ,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eACjDT,OAAA;YAAMQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAqB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACLvB,OAAA;UAAGQ,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAEvE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbvB,OAAA,CAACf,MAAM,CAAC6C,GAAG;QACTL,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BO,WAAW,EAAE;UAAEZ,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BU,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QACzBhC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAE5DT,OAAA;UAAIQ,SAAS,EAAC,sCAAsC;UAAAC,QAAA,eAClDT,OAAA;YAAMQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAoB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAELvB,OAAA;UAAKQ,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClE6C,cAAc,CAAC3C,GAAG,CAAC,CAAC4C,IAAI,EAAET,KAAK,kBAC9B9C,OAAA,CAACf,MAAM,CAAC6C,GAAG;YAETL,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAI,CAAE;YACpCK,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAE,CAAE;YACtCL,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEc,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBhC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAExDT,OAAA;cAAKQ,SAAS,EAAE,yBAAyB+C,IAAI,CAACC,OAAO,gDAAiD;cAAA/C,QAAA,eACpGT,OAAA,CAACuD,IAAI,CAACZ,IAAI;gBAACnC,SAAS,EAAE,WAAW+C,IAAI,CAACE,SAAS;cAAG;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNvB,OAAA;cAAIQ,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE8C,IAAI,CAACN;YAAK;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5DvB,OAAA;cAAGQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAE8C,IAAI,CAACd;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEvB,OAAA;cAAGQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAE8C,IAAI,CAACL;YAAW;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAZtEuB,KAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvB,OAAA,CAACf,MAAM,CAAC6C,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CO,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBhC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7BT,OAAA;YAAKQ,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDT,OAAA;cAAIQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAqB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFvB,OAAA;cAAKQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDT,OAAA;gBAAKQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNvB,OAAA;gBAAKQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNvB,OAAA;gBAAKQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNvB,OAAA;gBAAKQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNvB,OAAA;gBAAKQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNvB,OAAA;gBAAKQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCT,OAAA,CAACZ,WAAW;kBAACoB,SAAS,EAAC;gBAAsC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvB,OAAA;kBAAMQ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACmC,GAAA,GAtGIrB,cAAc;AAwGpB,MAAMU,gBAAgB,GAAG,CACvB;EACEJ,IAAI,EAAEzD,KAAK;EACX+D,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,0HAA0H;EACvIC,QAAQ,EAAE,CACR,4BAA4B,EAC5B,kCAAkC,EAClC,mBAAmB,EACnB,wBAAwB;AAE5B,CAAC,EACD;EACER,IAAI,EAAExD,MAAM;EACZ8D,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,mIAAmI;EAChJC,QAAQ,EAAE,CACR,6BAA6B,EAC7B,2BAA2B,EAC3B,2BAA2B,EAC3B,uBAAuB;AAE3B,CAAC,EACD;EACER,IAAI,EAAEpD,MAAM;EACZ0D,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE,yHAAyH;EACtIC,QAAQ,EAAE,CACR,2BAA2B,EAC3B,oBAAoB,EACpB,sBAAsB,EACtB,oBAAoB;AAExB,CAAC,EACD;EACER,IAAI,EAAElD,MAAM;EACZwD,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,8GAA8G;EAC3HC,QAAQ,EAAE,CACR,mBAAmB,EACnB,uBAAuB,EACvB,2BAA2B,EAC3B,0BAA0B;AAE9B,CAAC,EACD;EACER,IAAI,EAAEjD,UAAU;EAChBuD,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,yGAAyG;EACtHC,QAAQ,EAAE,CACR,oBAAoB,EACpB,oBAAoB,EACpB,4BAA4B,EAC5B,yBAAyB;AAE7B,CAAC,EACD;EACER,IAAI,EAAE9C,KAAK;EACXoD,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,2GAA2G;EACxHC,QAAQ,EAAE,CACR,cAAc,EACd,uBAAuB,EACvB,oBAAoB,EACpB,oBAAoB;AAExB,CAAC,CACF;AAED,MAAMG,cAAc,GAAG,CACrB;EACEX,IAAI,EAAEtD,GAAG;EACT4D,KAAK,EAAE,kBAAkB;EACzBR,KAAK,EAAE,KAAK;EACZS,WAAW,EAAE,yEAAyE;EACtFM,OAAO,EAAE,kBAAkB;EAC3BC,SAAS,EAAE;AACb,CAAC,EACD;EACEd,IAAI,EAAE/C,QAAQ;EACdqD,KAAK,EAAE,aAAa;EACpBR,KAAK,EAAE,MAAM;EACbS,WAAW,EAAE,2EAA2E;EACxFM,OAAO,EAAE,gBAAgB;EACzBC,SAAS,EAAE;AACb,CAAC,EACD;EACEd,IAAI,EAAEzD,KAAK;EACX+D,KAAK,EAAE,WAAW;EAClBR,KAAK,EAAE,GAAG;EACVS,WAAW,EAAE,oEAAoE;EACjFM,OAAO,EAAE,kBAAkB;EAC3BC,SAAS,EAAE;AACb,CAAC,EACD;EACEd,IAAI,EAAE9C,KAAK;EACXoD,KAAK,EAAE,WAAW;EAClBR,KAAK,EAAE,KAAK;EACZS,WAAW,EAAE,6DAA6D;EAC1EM,OAAO,EAAE,gBAAgB;EACzBC,SAAS,EAAE;AACb,CAAC,CACF;AAED,eAAexD,QAAQ;AAAC,IAAAoD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}