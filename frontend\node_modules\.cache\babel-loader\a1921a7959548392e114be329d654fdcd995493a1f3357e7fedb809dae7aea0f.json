{"ast": null, "code": "import * as React from 'react';\nimport { useId, useRef, useInsertionEffect } from 'react';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n  getSnapshotBeforeUpdate(prevProps) {\n    const element = this.props.childRef.current;\n    if (element && prevProps.isPresent && !this.props.isPresent) {\n      const size = this.props.sizeRef.current;\n      size.height = element.offsetHeight || 0;\n      size.width = element.offsetWidth || 0;\n      size.top = element.offsetTop;\n      size.left = element.offsetLeft;\n    }\n    return null;\n  }\n  /**\n   * Required with getSnapshotBeforeUpdate to stop React complaining.\n   */\n  componentDidUpdate() {}\n  render() {\n    return this.props.children;\n  }\n}\nfunction PopChild({\n  children,\n  isPresent\n}) {\n  const id = useId();\n  const ref = useRef(null);\n  const size = useRef({\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  });\n  /**\n   * We create and inject a style block so we can apply this explicit\n   * sizing in a non-destructive manner by just deleting the style block.\n   *\n   * We can't apply size via render as the measurement happens\n   * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n   * styles directly on the DOM node, we might be overwriting\n   * styles set via the style prop.\n   */\n  useInsertionEffect(() => {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = size.current;\n    if (isPresent || !ref.current || !width || !height) return;\n    ref.current.dataset.motionPopId = id;\n    const style = document.createElement(\"style\");\n    document.head.appendChild(style);\n    if (style.sheet) {\n      style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n    }\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, [isPresent]);\n  return React.createElement(PopChildMeasure, {\n    isPresent: isPresent,\n    childRef: ref,\n    sizeRef: size\n  }, React.cloneElement(children, {\n    ref\n  }));\n}\nexport { PopChild };", "map": {"version": 3, "names": ["React", "useId", "useRef", "useInsertionEffect", "PopChildMeasure", "Component", "getSnapshotBeforeUpdate", "prevProps", "element", "props", "childRef", "current", "isPresent", "size", "sizeRef", "height", "offsetHeight", "width", "offsetWidth", "top", "offsetTop", "left", "offsetLeft", "componentDidUpdate", "render", "children", "PopChild", "id", "ref", "dataset", "motionPopId", "style", "document", "createElement", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement"], "sources": ["D:/Projects/ai-hr-agent/frontend/node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useId, useRef, useInsertionEffect } from 'react';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (React.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, React.cloneElement(children, { ref })));\n}\n\nexport { PopChild };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,OAAO;;AAEzD;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASJ,KAAK,CAACK,SAAS,CAAC;EAC1CC,uBAAuBA,CAACC,SAAS,EAAE;IAC/B,MAAMC,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,OAAO;IAC3C,IAAIH,OAAO,IAAID,SAAS,CAACK,SAAS,IAAI,CAAC,IAAI,CAACH,KAAK,CAACG,SAAS,EAAE;MACzD,MAAMC,IAAI,GAAG,IAAI,CAACJ,KAAK,CAACK,OAAO,CAACH,OAAO;MACvCE,IAAI,CAACE,MAAM,GAAGP,OAAO,CAACQ,YAAY,IAAI,CAAC;MACvCH,IAAI,CAACI,KAAK,GAAGT,OAAO,CAACU,WAAW,IAAI,CAAC;MACrCL,IAAI,CAACM,GAAG,GAAGX,OAAO,CAACY,SAAS;MAC5BP,IAAI,CAACQ,IAAI,GAAGb,OAAO,CAACc,UAAU;IAClC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG,CAAE;EACvBC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACf,KAAK,CAACgB,QAAQ;EAC9B;AACJ;AACA,SAASC,QAAQA,CAAC;EAAED,QAAQ;EAAEb;AAAU,CAAC,EAAE;EACvC,MAAMe,EAAE,GAAG1B,KAAK,CAAC,CAAC;EAClB,MAAM2B,GAAG,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMW,IAAI,GAAGX,MAAM,CAAC;IAChBe,KAAK,EAAE,CAAC;IACRF,MAAM,EAAE,CAAC;IACTI,GAAG,EAAE,CAAC;IACNE,IAAI,EAAE;EACV,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlB,kBAAkB,CAAC,MAAM;IACrB,MAAM;MAAEc,KAAK;MAAEF,MAAM;MAAEI,GAAG;MAAEE;IAAK,CAAC,GAAGR,IAAI,CAACF,OAAO;IACjD,IAAIC,SAAS,IAAI,CAACgB,GAAG,CAACjB,OAAO,IAAI,CAACM,KAAK,IAAI,CAACF,MAAM,EAC9C;IACJa,GAAG,CAACjB,OAAO,CAACkB,OAAO,CAACC,WAAW,GAAGH,EAAE;IACpC,MAAMI,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;IAChC,IAAIA,KAAK,CAACK,KAAK,EAAE;MACbL,KAAK,CAACK,KAAK,CAACC,UAAU,CAAC;AACnC,iCAAiCV,EAAE;AACnC;AACA,qBAAqBV,KAAK;AAC1B,sBAAsBF,MAAM;AAC5B,mBAAmBI,GAAG;AACtB,oBAAoBE,IAAI;AACxB;AACA,SAAS,CAAC;IACF;IACA,OAAO,MAAM;MACTW,QAAQ,CAACE,IAAI,CAACI,WAAW,CAACP,KAAK,CAAC;IACpC,CAAC;EACL,CAAC,EAAE,CAACnB,SAAS,CAAC,CAAC;EACf,OAAQZ,KAAK,CAACiC,aAAa,CAAC7B,eAAe,EAAE;IAAEQ,SAAS,EAAEA,SAAS;IAAEF,QAAQ,EAAEkB,GAAG;IAAEd,OAAO,EAAED;EAAK,CAAC,EAAEb,KAAK,CAACuC,YAAY,CAACd,QAAQ,EAAE;IAAEG;EAAI,CAAC,CAAC,CAAC;AAC/I;AAEA,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}