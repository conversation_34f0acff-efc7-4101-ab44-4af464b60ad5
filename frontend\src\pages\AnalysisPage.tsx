import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import { 
  ArrowLeft, 
  Upload, 
  FileText, 
  X, 
  Zap,
  Brain,
  Search,
  Shield,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  Clock,
  Sparkles,
  Target,
  Users,
  Cpu
} from 'lucide-react';
import AnimatedBackground from '../components/AnimatedBackground';
import ProcessingAnimation from '../components/ProcessingAnimation';
import ResultsDisplay from '../components/ResultsDisplay';

interface JobRequirements {
  position_title: string;
  job_description: string;
  required_skills: string;
  preferred_skills: string;
  experience_level: string;
  education_requirements: string;
  location: string;
  employment_type: string;
  company_name: string;
  keywords: string;
}

interface AnalysisResult {
  success: boolean;
  job_id: string;
  message: string;
  processing_time?: number;
  statistics?: any;
  final_report?: string;
  candidate_reports?: Record<string, string>;
  report_files?: Record<string, string | string[]>;
  warnings?: string[];
  errors?: string[];
}

const AnalysisPage: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<'form' | 'processing' | 'results'>('form');
  const [files, setFiles] = useState<File[]>([]);
  const [jobRequirements, setJobRequirements] = useState<JobRequirements>({
    position_title: '',
    job_description: '',
    required_skills: '',
    preferred_skills: '',
    experience_level: '',
    education_requirements: '',
    location: '',
    employment_type: '',
    company_name: '',
    keywords: ''
  });
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [currentProcessingStep, setCurrentProcessingStep] = useState('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(prev => [...prev, ...acceptedFiles]);
    toast.success(`${acceptedFiles.length} file(s) added successfully`);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt']
    },
    multiple: true
  });

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    toast.success('File removed');
  };

  const handleInputChange = (field: keyof JobRequirements, value: string) => {
    setJobRequirements(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!jobRequirements.position_title || !jobRequirements.job_description) {
      toast.error('Please fill in position title and job description');
      return;
    }

    if (files.length === 0) {
      toast.error('Please upload at least one resume file');
      return;
    }

    setStep('processing');
    setProcessingProgress(0);
    setCurrentProcessingStep('Initializing AI agents...');

    try {
      const formData = new FormData();
      
      // Add job requirements
      Object.entries(jobRequirements).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Add files
      files.forEach(file => {
        formData.append('files', file);
      });

      // Simulate processing steps with TalentSphere AI branding
      const steps = [
        'Initializing AI agents...',
        'Extracting talent data...',
        'Analyzing candidate profiles...',
        'Matching against requirements...',
        'Deep research across platforms...',
        'Validating talent information...',
        'Generating strategic insights...'
      ];

      // Simulate progress
      for (let i = 0; i < steps.length; i++) {
        setCurrentProcessingStep(steps[i]);
        setProcessingProgress((i + 1) / steps.length * 90); // Stop at 90%
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Make API call
      const response = await fetch('http://localhost:8000/analyze', {
        method: 'POST',
        body: formData,
      });

      const result: AnalysisResult = await response.json();
      
      setProcessingProgress(100);
      setCurrentProcessingStep('Analysis complete!');
      
      if (result.success) {
        setAnalysisResult(result);
        setStep('results');
        toast.success('Talent analysis completed successfully!');
      } else {
        toast.error(result.message || 'Analysis failed');
        setStep('form');
      }
      
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error('Failed to analyze talent profiles. Please try again.');
      setStep('form');
    }
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleStartNew = () => {
    setStep('form');
    setFiles([]);
    setJobRequirements({
      position_title: '',
      job_description: '',
      required_skills: '',
      preferred_skills: '',
      experience_level: '',
      education_requirements: '',
      location: '',
      employment_type: '',
      company_name: '',
      keywords: ''
    });
    setAnalysisResult(null);
    setProcessingProgress(0);
    setCurrentProcessingStep('');
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <AnimatedBackground />
      
      {/* Floating particles effect */}
      <div className="floating-particles">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${8 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
      
      {/* Header */}
      <header className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <button
            onClick={handleBackToHome}
            className="btn-secondary px-6 py-3 flex items-center gap-3 hover-lift"
          >
            <ArrowLeft className="w-5 h-5" />
            Back to Home
          </button>
          
          <motion.h1 
            className="text-2xl md:text-3xl font-bold gradient-text"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            TalentSphere AI Analysis
          </motion.h1>
          
          <div className="w-32" /> {/* Spacer */}
        </div>
      </header>

      <div className="relative z-10 max-w-7xl mx-auto px-4 py-8">
        <AnimatePresence mode="wait">
          {step === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ duration: 0.6 }}
              className="max-w-6xl mx-auto"
            >
              <div className="text-center mb-12">
                <motion.div
                  className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 200, delay: 0.2 }}
                >
                  <Sparkles className="w-4 h-4 text-cyan-400" />
                  <span className="text-sm font-medium text-cyan-300">AI-Powered Talent Discovery</span>
                </motion.div>
                <h2 className="text-4xl md:text-5xl font-bold mb-6">
                  Begin Your <span className="gradient-text">AI Analysis</span>
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                  Upload talent profiles and define position requirements for comprehensive 
                  AI-powered analysis and strategic insights
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-10">
                {/* Job Requirements Section */}
                <motion.div 
                  className="glass-intense p-8 lg:p-12 rounded-3xl hover-glow"
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <h3 className="text-3xl font-bold mb-8 flex items-center gap-4">
                    <div className="icon-container">
                      <Target className="w-8 h-8 text-blue-400" />
                    </div>
                    Position Requirements
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Position Title *
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.position_title}
                        onChange={(e) => handleInputChange('position_title', e.target.value)}
                        className="form-input w-full"
                        placeholder="Senior Software Engineer"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Company Name
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.company_name}
                        onChange={(e) => handleInputChange('company_name', e.target.value)}
                        className="form-input w-full"
                        placeholder="Your Company Name"
                      />
                    </div>
                  </div>

                  <div className="mt-8">
                    <label className="block text-sm font-semibold mb-3 text-gray-300">
                      Position Description *
                    </label>
                    <textarea
                      value={jobRequirements.job_description}
                      onChange={(e) => handleInputChange('job_description', e.target.value)}
                      className="form-textarea w-full"
                      rows={6}
                      placeholder="Enter detailed position description including responsibilities, requirements, and qualifications..."
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Required Skills (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.required_skills}
                        onChange={(e) => handleInputChange('required_skills', e.target.value)}
                        className="form-input w-full"
                        placeholder="Python, React, AWS, Machine Learning"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Preferred Skills (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.preferred_skills}
                        onChange={(e) => handleInputChange('preferred_skills', e.target.value)}
                        className="form-input w-full"
                        placeholder="Docker, Kubernetes, GraphQL"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Experience Level
                      </label>
                      <select
                        value={jobRequirements.experience_level}
                        onChange={(e) => handleInputChange('experience_level', e.target.value)}
                        className="form-input w-full"
                      >
                        <option value="">Select experience level</option>
                        <option value="Entry Level">Entry Level (0-2 years)</option>
                        <option value="Mid Level">Mid Level (3-5 years)</option>
                        <option value="Senior Level">Senior Level (6-10 years)</option>
                        <option value="Executive Level">Executive Level (10+ years)</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Location
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        className="form-input w-full"
                        placeholder="San Francisco, CA or Remote"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Education Requirements
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.education_requirements}
                        onChange={(e) => handleInputChange('education_requirements', e.target.value)}
                        className="form-input w-full"
                        placeholder="Bachelor's degree in Computer Science"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Additional Keywords (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.keywords}
                        onChange={(e) => handleInputChange('keywords', e.target.value)}
                        className="form-input w-full"
                        placeholder="Agile, Scrum, Leadership"
                      />
                    </div>
                  </div>
                </motion.div>

                {/* File Upload Section */}
                <motion.div 
                  className="glass-intense p-8 lg:p-12 rounded-3xl hover-glow"
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  <h3 className="text-3xl font-bold mb-8 flex items-center gap-4">
                    <div className="icon-container">
                      <Users className="w-8 h-8 text-purple-400" />
                    </div>
                    Talent Profiles
                  </h3>
                  
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-400 ${
                      isDragActive
                        ? 'border-blue-400 bg-blue-400/10 scale-105'
                        : 'border-white/20 hover:border-white/40 hover:bg-white/5'
                    }`}
                  >
                    <input {...getInputProps()} />
                    <motion.div
                      animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Upload className="w-16 h-16 mx-auto mb-6 text-gray-400" />
                      <p className="text-xl mb-3">
                        {isDragActive
                          ? 'Drop the talent profiles here...'
                          : 'Drag & drop talent profiles here, or click to select'
                        }
                      </p>
                      <p className="text-sm text-gray-500">
                        Supports PDF, DOC, DOCX, and TXT files
                      </p>
                    </motion.div>
                  </div>

                  {/* Selected Files */}
                  {files.length > 0 && (
                    <motion.div 
                      className="mt-8"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      transition={{ duration: 0.4 }}
                    >
                      <h4 className="text-xl font-bold mb-6 flex items-center gap-3">
                        <CheckCircle className="w-6 h-6 text-green-400" />
                        Selected Files ({files.length})
                      </h4>
                      <div className="grid gap-3">
                        {files.map((file, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center justify-between p-4 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl hover:bg-white/10 transition-all"
                          >
                            <div className="flex items-center gap-4">
                              <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                                <FileText className="w-6 h-6 text-blue-400" />
                              </div>
                              <div>
                                <span className="font-medium">{file.name}</span>
                                <div className="text-sm text-gray-500">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </div>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeFile(index)}
                              className="w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded-lg flex items-center justify-center text-red-400 hover:text-red-300 transition-all"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </motion.div>

                {/* Submit Button */}
                <motion.div 
                  className="text-center"
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <button
                    type="submit"
                    className="btn-primary text-xl px-12 py-6 mx-auto hover-lift"
                    disabled={!jobRequirements.position_title || !jobRequirements.job_description || files.length === 0}
                  >
                    <Brain className="w-7 h-7" />
                    Begin AI Analysis
                    <Sparkles className="w-7 h-7" />
                  </button>
                  <p className="text-sm text-gray-400 mt-4">
                    Our AI agents will analyze {files.length} profile{files.length !== 1 ? 's' : ''} with advanced intelligence
                  </p>
                </motion.div>
              </form>
            </motion.div>
          )}

          {step === 'processing' && (
            <ProcessingAnimation
              progress={processingProgress}
              currentStep={currentProcessingStep}
              filesCount={files.length}
            />
          )}

          {step === 'results' && analysisResult && (
            <ResultsDisplay
              result={analysisResult}
              onStartNew={handleStartNew}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AnalysisPage;