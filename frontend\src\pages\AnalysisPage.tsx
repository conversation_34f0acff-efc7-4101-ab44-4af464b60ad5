import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import {
  ArrowLeft,
  Upload,
  FileText,
  X,
  Brain,
  Search,
  CheckCircle,
  Sparkles,
  Target,
  Folder,
  FileSpreadsheet,
  Cloud,
  RefreshCw,
  Info
} from 'lucide-react';
import AnimatedBackground from '../components/AnimatedBackground';
import ProcessingAnimation from '../components/ProcessingAnimation';
import ResultsDisplay from '../components/ResultsDisplay';

interface JobRequirements {
  position_title: string;
  job_description: string;
  required_skills: string;
  preferred_skills: string;
  experience_level: string;
  education_requirements: string;
  location: string;
  employment_type: string;
  company_name: string;
  keywords: string;
}

interface AnalysisResult {
  success: boolean;
  job_id: string;
  message: string;
  processing_time?: number;
  statistics?: any;
  final_report?: string;
  candidate_reports?: Record<string, string>;
  report_files?: Record<string, string | string[]>;
  warnings?: string[];
  errors?: string[];
}

interface GoogleDriveFile {
  id: string;
  name: string;
  size: number;
  created_time: string;
  modified_time: string;
}

interface ExcelValidationResult {
  total_rows: number;
  valid_rows: number;
  invalid_rows: Array<{
    row: number;
    name: string;
    resume_link: string;
    error: string;
  }>;
  download_urls: string[];
}

type UploadMethod = 'files' | 'google_drive' | 'excel';

const AnalysisPage: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<'form' | 'processing' | 'results'>('form');
  const [uploadMethod, setUploadMethod] = useState<UploadMethod>('files');
  const [files, setFiles] = useState<File[]>([]);
  const [jobRequirements, setJobRequirements] = useState<JobRequirements>({
    position_title: '',
    job_description: '',
    required_skills: '',
    preferred_skills: '',
    experience_level: '',
    education_requirements: '',
    location: '',
    employment_type: '',
    company_name: '',
    keywords: ''
  });
  
  // Google Drive state
  const [googleDriveConnected, setGoogleDriveConnected] = useState(false);
  const [googleAccessToken, setGoogleAccessToken] = useState('');
  const [googleFolderId, setGoogleFolderId] = useState('');
  const [googleDriveFiles, setGoogleDriveFiles] = useState<GoogleDriveFile[]>([]);
  const [isConnectingGoogleDrive, setIsConnectingGoogleDrive] = useState(false);

  // Excel state
  const [excelFile, setExcelFile] = useState<File | null>(null);
  const [excelValidation, setExcelValidation] = useState<ExcelValidationResult | null>(null);
  const [isValidatingExcel, setIsValidatingExcel] = useState(false);

  // Common state
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [currentProcessingStep, setCurrentProcessingStep] = useState('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (uploadMethod === 'files') {
      setFiles(prev => [...prev, ...acceptedFiles]);
      toast.success(`${acceptedFiles.length} file(s) added successfully`);
    } else if (uploadMethod === 'excel') {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
          setExcelFile(file);
          toast.success('Excel file selected for validation');
        } else {
          toast.error('Please select a valid Excel file (.xlsx or .xls)');
        }
      }
    }
  }, [uploadMethod]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: uploadMethod === 'files' ? {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt']
    } : uploadMethod === 'excel' ? {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    } : {},
    multiple: uploadMethod === 'files',
    disabled: uploadMethod === 'google_drive'
  });

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    toast.success('File removed');
  };

  const handleInputChange = (field: keyof JobRequirements, value: string) => {
    setJobRequirements(prev => ({ ...prev, [field]: value }));
  };

  const handleUploadMethodChange = (method: UploadMethod) => {
    setUploadMethod(method);
    // Reset method-specific state
    setFiles([]);
    setExcelFile(null);
    setExcelValidation(null);
    setGoogleDriveFiles([]);
    setGoogleFolderId('');
  };

  // Google Drive functions
  const connectGoogleDrive = async () => {
    setIsConnectingGoogleDrive(true);
    try {
      const response = await fetch('http://localhost:8000/auth/google/url');
      const data = await response.json();

      if (data.auth_url) {
        // Open Google OAuth in popup
        const popup = window.open(data.auth_url, 'google-auth', 'width=500,height=600,scrollbars=yes,resizable=yes');

        // Listen for postMessage from popup
        const handleMessage = (event: MessageEvent) => {
          // Verify origin for security (in production, use your actual domain)
          if (event.origin !== window.location.origin && event.origin !== 'http://localhost:8000') {
            return;
          }

          if (event.data.type === 'GOOGLE_AUTH_SUCCESS') {
            // Clean up event listener
            window.removeEventListener('message', handleMessage);

            // Set the access token
            setGoogleAccessToken(event.data.access_token);
            setGoogleDriveConnected(true);
            toast.success('Google Drive connected successfully!');
            setIsConnectingGoogleDrive(false);

            // Close popup if still open
            if (popup && !popup.closed) {
              popup.close();
            }
          } else if (event.data.type === 'GOOGLE_AUTH_ERROR') {
            // Clean up event listener
            window.removeEventListener('message', handleMessage);

            toast.error('Google Drive connection failed');
            setIsConnectingGoogleDrive(false);

            // Close popup if still open
            if (popup && !popup.closed) {
              popup.close();
            }
          }
        };

        // Add message listener
        window.addEventListener('message', handleMessage);

        // Fallback: Check localStorage periodically (for browsers that block postMessage)
        const pollTimer = setInterval(() => {
          try {
            // Check if popup is closed
            if (popup?.closed) {
              clearInterval(pollTimer);
              window.removeEventListener('message', handleMessage);

              // Check if token was set in localStorage by popup
              const token = localStorage.getItem('google_access_token');
              if (token) {
                setGoogleAccessToken(token);
                setGoogleDriveConnected(true);
                toast.success('Google Drive connected successfully!');
                localStorage.removeItem('google_access_token');
              } else {
                toast.error('Google Drive connection failed or was cancelled');
              }
              setIsConnectingGoogleDrive(false);
            }
          } catch (e) {
            // Cross-origin error is expected, continue polling
          }
        }, 1000);

        // Cleanup after 5 minutes
        setTimeout(() => {
          clearInterval(pollTimer);
          window.removeEventListener('message', handleMessage);
          if (popup && !popup.closed) {
            popup.close();
          }
          if (isConnectingGoogleDrive) {
            toast.error('Google Drive connection timed out');
            setIsConnectingGoogleDrive(false);
          }
        }, 300000); // 5 minutes

      } else {
        throw new Error('Failed to get authorization URL');
      }
    } catch (error) {
      console.error('Google Drive connection error:', error);
      toast.error('Failed to connect to Google Drive');
      setIsConnectingGoogleDrive(false);
    }
  };

  const loadGoogleDriveFiles = async () => {
    if (!googleFolderId || !googleAccessToken) {
      toast.error('Please enter a folder ID and ensure Google Drive is connected');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('access_token', googleAccessToken);

      const response = await fetch(`http://localhost:8000/google-drive/folders/${googleFolderId}/files`, {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.success) {
        setGoogleDriveFiles(data.files);
        toast.success(`Found ${data.files.length} PDF files in the folder`);
      } else {
        throw new Error(data.detail || 'Failed to load files');
      }
    } catch (error) {
      console.error('Error loading Google Drive files:', error);
      toast.error('Failed to load files from Google Drive folder');
    }
  };

  // Excel functions
  const validateExcelFile = async () => {
    if (!excelFile) {
      toast.error('Please select an Excel file first');
      return;
    }

    setIsValidatingExcel(true);
    try {
      const formData = new FormData();
      formData.append('file', excelFile);

      const response = await fetch('http://localhost:8000/process-excel', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.success) {
        setExcelValidation(data.data);
        toast.success(`Excel validation complete: ${data.data.valid_rows} valid rows found`);
        
        if (data.data.invalid_rows.length > 0) {
          toast.error(`${data.data.invalid_rows.length} rows have errors - check validation results`);
        }
      } else {
        throw new Error(data.detail || 'Excel validation failed');
      }
    } catch (error) {
      console.error('Excel validation error:', error);
      toast.error('Failed to validate Excel file');
    } finally {
      setIsValidatingExcel(false);
    }
  };

  const canProceedWithAnalysis = () => {
    if (!jobRequirements.position_title || !jobRequirements.job_description) {
      return false;
    }

    switch (uploadMethod) {
      case 'files':
        return files.length > 0;
      case 'google_drive':
        return googleDriveConnected && googleFolderId && googleDriveFiles.length > 0;
      case 'excel':
        return excelValidation && excelValidation.valid_rows > 0;
      default:
        return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canProceedWithAnalysis()) {
      toast.error('Please complete all required fields and upload files');
      return;
    }

    setStep('processing');
    setProcessingProgress(0);
    setCurrentProcessingStep('Initializing AI agents...');

    try {
      const formData = new FormData();
      
      // Add job requirements
      Object.entries(jobRequirements).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Add upload method
      formData.append('upload_method', uploadMethod);

      // Add method-specific data
      if (uploadMethod === 'files') {
        files.forEach(file => {
          formData.append('files', file);
        });
      } else if (uploadMethod === 'google_drive') {
        formData.append('google_folder_id', googleFolderId);
        formData.append('google_access_token', googleAccessToken);
      } else if (uploadMethod === 'excel') {
        formData.append('excel_file', excelFile!);
      }

      // Simulate processing steps
      const steps = [
        'Initializing AI agents...',
        'Processing uploaded data...',
        'Extracting talent profiles...',
        'Analyzing candidate profiles...',
        'Matching against requirements...',
        'Deep research across platforms...',
        'Validating talent information...',
        'Generating strategic insights...'
      ];

      // Simulate progress
      for (let i = 0; i < steps.length; i++) {
        setCurrentProcessingStep(steps[i]);
        setProcessingProgress((i + 1) / steps.length * 90);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Make API call
      const response = await fetch('http://localhost:8000/analyze', {
        method: 'POST',
        body: formData,
      });

      const result: AnalysisResult = await response.json();
      
      setProcessingProgress(100);
      setCurrentProcessingStep('Analysis complete!');
      
      if (result.success) {
        setAnalysisResult(result);
        setStep('results');
        toast.success('Talent analysis completed successfully!');
      } else {
        toast.error(result.message || 'Analysis failed');
        setStep('form');
      }
      
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error('Failed to analyze talent profiles. Please try again.');
      setStep('form');
    }
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleStartNew = () => {
    setStep('form');
    setUploadMethod('files');
    setFiles([]);
    setExcelFile(null);
    setExcelValidation(null);
    setGoogleDriveFiles([]);
    setGoogleFolderId('');
    setGoogleDriveConnected(false);
    setGoogleAccessToken('');
    setJobRequirements({
      position_title: '',
      job_description: '',
      required_skills: '',
      preferred_skills: '',
      experience_level: '',
      education_requirements: '',
      location: '',
      employment_type: '',
      company_name: '',
      keywords: ''
    });
    setAnalysisResult(null);
    setProcessingProgress(0);
    setCurrentProcessingStep('');
  };

  const getFileCountText = () => {
    switch (uploadMethod) {
      case 'files':
        return `${files.length} file${files.length !== 1 ? 's' : ''}`;
      case 'google_drive':
        return `${googleDriveFiles.length} file${googleDriveFiles.length !== 1 ? 's' : ''}`;
      case 'excel':
        return excelValidation ? `${excelValidation.valid_rows} resume${excelValidation.valid_rows !== 1 ? 's' : ''}` : '0 resumes';
      default:
        return '0 files';
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <AnimatedBackground />
      
      {/* Floating particles effect */}
      <div className="floating-particles">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${8 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
      
      {/* Header */}
      <header className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <button
            onClick={handleBackToHome}
            className="btn-secondary px-6 py-3 flex items-center gap-3 hover-lift"
          >
            <ArrowLeft className="w-5 h-5" />
            Back to Home
          </button>
          
          <motion.h1 
            className="text-2xl md:text-3xl font-bold gradient-text"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            TalentSphere AI Analysis
          </motion.h1>
          
          <div className="w-32" /> {/* Spacer */}
        </div>
      </header>

      <div className="relative z-10 max-w-7xl mx-auto px-4 py-8">
        <AnimatePresence mode="wait">
          {step === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ duration: 0.6 }}
              className="max-w-6xl mx-auto"
            >
              <div className="text-center mb-12">
                <motion.div
                  className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 200, delay: 0.2 }}
                >
                  <Sparkles className="w-4 h-4 text-cyan-400" />
                  <span className="text-sm font-medium text-cyan-300">AI-Powered Talent Discovery</span>
                </motion.div>
                <h2 className="text-4xl md:text-5xl font-bold mb-6">
                  Begin Your <span className="gradient-text">AI Analysis</span>
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                  Upload talent profiles using multiple methods and define position requirements for comprehensive 
                  AI-powered analysis and strategic insights
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-10">
                {/* Upload Method Selection */}
                <motion.div 
                  className="glass-intense p-8 lg:p-12 rounded-3xl hover-glow"
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.1 }}
                >
                  <h3 className="text-3xl font-bold mb-8 flex items-center gap-4">
                    <div className="icon-container">
                      <Cloud className="w-8 h-8 text-purple-400" />
                    </div>
                    Choose Upload Method
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    {[
                      {
                        id: 'files' as UploadMethod,
                        title: 'Direct File Upload',
                        description: 'Upload resume files directly from your computer',
                        icon: Upload,
                        color: 'blue'
                      },
                      {
                        id: 'google_drive' as UploadMethod,
                        title: 'Google Drive Folder',
                        description: 'Connect to Google Drive and select a folder with resumes',
                        icon: Folder,
                        color: 'green'
                      },
                      {
                        id: 'excel' as UploadMethod,
                        title: 'Excel with Links',
                        description: 'Upload Excel file with names and Google Drive resume links',
                        icon: FileSpreadsheet,
                        color: 'orange'
                      }
                    ].map((method) => (
                      <motion.button
                        key={method.id}
                        type="button"
                        onClick={() => handleUploadMethodChange(method.id)}
                        className={`p-6 rounded-2xl border-2 transition-all duration-300 text-left ${
                          uploadMethod === method.id
                            ? `border-${method.color}-400 bg-${method.color}-400/10`
                            : 'border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <method.icon className={`w-8 h-8 text-${method.color}-400 mb-4`} />
                        <h4 className="font-bold text-lg mb-2">{method.title}</h4>
                        <p className="text-gray-400 text-sm">{method.description}</p>
                        {uploadMethod === method.id && (
                          <div className="mt-3 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-400" />
                            <span className="text-sm text-green-400">Selected</span>
                          </div>
                        )}
                      </motion.button>
                    ))}
                  </div>

                  {/* Method-specific UI */}
                  <AnimatePresence mode="wait">
                    {uploadMethod === 'files' && (
                      <motion.div
                        key="files-upload"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.4 }}
                      >
                        <div
                          {...getRootProps()}
                          className={`border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-400 ${
                            isDragActive
                              ? 'border-blue-400 bg-blue-400/10 scale-105'
                              : 'border-white/20 hover:border-white/40 hover:bg-white/5'
                          }`}
                        >
                          <input {...getInputProps()} />
                          <motion.div
                            animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Upload className="w-16 h-16 mx-auto mb-6 text-gray-400" />
                            <p className="text-xl mb-3">
                              {isDragActive
                                ? 'Drop the talent profiles here...'
                                : 'Drag & drop talent profiles here, or click to select'
                              }
                            </p>
                            <p className="text-sm text-gray-500">
                              Supports PDF, DOC, DOCX, and TXT files
                            </p>
                          </motion.div>
                        </div>

                        {files.length > 0 && (
                          <motion.div 
                            className="mt-8"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            transition={{ duration: 0.4 }}
                          >
                            <h4 className="text-xl font-bold mb-6 flex items-center gap-3">
                              <CheckCircle className="w-6 h-6 text-green-400" />
                              Selected Files ({files.length})
                            </h4>
                            <div className="grid gap-3 max-h-60 overflow-y-auto">
                              {files.map((file, index) => (
                                <motion.div
                                  key={index}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: index * 0.1 }}
                                  className="flex items-center justify-between p-4 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl hover:bg-white/10 transition-all"
                                >
                                  <div className="flex items-center gap-4">
                                    <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                                      <FileText className="w-6 h-6 text-blue-400" />
                                    </div>
                                    <div>
                                      <span className="font-medium">{file.name}</span>
                                      <div className="text-sm text-gray-500">
                                        {(file.size / 1024 / 1024).toFixed(2)} MB
                                      </div>
                                    </div>
                                  </div>
                                  <button
                                    type="button"
                                    onClick={() => removeFile(index)}
                                    className="w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded-lg flex items-center justify-center text-red-400 hover:text-red-300 transition-all"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </motion.div>
                    )}

                    {uploadMethod === 'google_drive' && (
                      <motion.div
                        key="google-drive-upload"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.4 }}
                        className="space-y-6"
                      >
                        {!googleDriveConnected ? (
                          <div className="text-center p-8 border-2 border-dashed border-white/20 rounded-2xl">
                            <Cloud className="w-16 h-16 mx-auto mb-6 text-gray-400" />
                            <h4 className="text-xl font-bold mb-4">Connect to Google Drive</h4>
                            <p className="text-gray-400 mb-6">
                              Connect your Google Drive account to access folders with resume files
                            </p>
                            <button
                              type="button"
                              onClick={connectGoogleDrive}
                              disabled={isConnectingGoogleDrive}
                              className="btn-primary px-8 py-4 flex items-center gap-3 mx-auto"
                            >
                              {isConnectingGoogleDrive ? (
                                <RefreshCw className="w-5 h-5 animate-spin" />
                              ) : (
                                <Cloud className="w-5 h-5" />
                              )}
                              {isConnectingGoogleDrive ? 'Connecting...' : 'Connect Google Drive'}
                            </button>
                          </div>
                        ) : (
                          <div className="space-y-6">
                            <div className="flex items-center gap-3 p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
                              <CheckCircle className="w-6 h-6 text-green-400" />
                              <span className="text-green-400 font-medium">Google Drive Connected</span>
                            </div>
                            
                            <div>
                              <label className="block text-sm font-semibold mb-3 text-gray-300">
                                Google Drive Folder ID *
                              </label>
                              <div className="flex gap-3">
                                <input
                                  type="text"
                                  value={googleFolderId}
                                  onChange={(e) => setGoogleFolderId(e.target.value)}
                                  className="form-input flex-1"
                                  placeholder="Enter Google Drive folder ID"
                                />
                                <button
                                  type="button"
                                  onClick={loadGoogleDriveFiles}
                                  className="btn-secondary px-6 py-3 flex items-center gap-2"
                                >
                                  <Search className="w-4 h-4" />
                                  Load Files
                                </button>
                              </div>
                              <div className="mt-2 flex items-center gap-2 text-sm text-gray-400">
                                <Info className="w-4 h-4" />
                                <span>Copy the folder ID from your Google Drive URL</span>
                              </div>
                            </div>

                            {googleDriveFiles.length > 0 && (
                              <div>
                                <h4 className="text-xl font-bold mb-4 flex items-center gap-3">
                                  <Folder className="w-6 h-6 text-green-400" />
                                  Found Files ({googleDriveFiles.length})
                                </h4>
                                <div className="grid gap-3 max-h-60 overflow-y-auto">
                                  {googleDriveFiles.map((file) => (
                                    <div
                                      key={file.id}
                                      className="flex items-center justify-between p-4 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl"
                                    >
                                      <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                                          <FileText className="w-6 h-6 text-green-400" />
                                        </div>
                                        <div>
                                          <span className="font-medium">{file.name}</span>
                                          <div className="text-sm text-gray-500">
                                            {(file.size / 1024 / 1024).toFixed(2)} MB
                                          </div>
                                        </div>
                                      </div>
                                      <CheckCircle className="w-5 h-5 text-green-400" />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </motion.div>
                    )}

                    {uploadMethod === 'excel' && (
                      <motion.div
                        key="excel-upload"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.4 }}
                        className="space-y-6"
                      >
                        <div
                          {...getRootProps()}
                          className={`border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-400 ${
                            isDragActive
                              ? 'border-orange-400 bg-orange-400/10 scale-105'
                              : 'border-white/20 hover:border-white/40 hover:bg-white/5'
                          }`}
                        >
                          <input {...getInputProps()} />
                          <motion.div
                            animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
                            transition={{ duration: 0.2 }}
                          >
                            <FileSpreadsheet className="w-16 h-16 mx-auto mb-6 text-gray-400" />
                            <p className="text-xl mb-3">
                              {isDragActive
                                ? 'Drop the Excel file here...'
                                : 'Upload Excel file with resume links'
                              }
                            </p>
                            <p className="text-sm text-gray-500">
                              Excel file should have columns: "name" and "resume_link"
                            </p>
                          </motion.div>
                        </div>

                        {excelFile && (
                          <div className="space-y-4">
                            <div className="flex items-center justify-between p-4 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl">
                              <div className="flex items-center gap-4">
                                <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                                  <FileSpreadsheet className="w-6 h-6 text-orange-400" />
                                </div>
                                <div>
                                  <span className="font-medium">{excelFile.name}</span>
                                  <div className="text-sm text-gray-500">
                                    {(excelFile.size / 1024 / 1024).toFixed(2)} MB
                                  </div>
                                </div>
                              </div>
                              <div className="flex gap-2">
                                <button
                                  type="button"
                                  onClick={validateExcelFile}
                                  disabled={isValidatingExcel}
                                  className="btn-secondary px-4 py-2 flex items-center gap-2"
                                >
                                  {isValidatingExcel ? (
                                    <RefreshCw className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <CheckCircle className="w-4 h-4" />
                                  )}
                                  {isValidatingExcel ? 'Validating...' : 'Validate'}
                                </button>
                                <button
                                  type="button"
                                  onClick={() => {
                                    setExcelFile(null);
                                    setExcelValidation(null);
                                  }}
                                  className="w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded-lg flex items-center justify-center text-red-400 hover:text-red-300 transition-all"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            </div>

                            {excelValidation && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl text-center">
                                    <div className="text-2xl font-bold text-blue-400">{excelValidation.total_rows}</div>
                                    <div className="text-sm text-gray-400">Total Rows</div>
                                  </div>
                                  <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-xl text-center">
                                    <div className="text-2xl font-bold text-green-400">{excelValidation.valid_rows}</div>
                                    <div className="text-sm text-gray-400">Valid Resumes</div>
                                  </div>
                                  <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl text-center">
                                    <div className="text-2xl font-bold text-red-400">{excelValidation.invalid_rows.length}</div>
                                    <div className="text-sm text-gray-400">Invalid Rows</div>
                                  </div>
                                </div>

                                {excelValidation.invalid_rows.length > 0 && (
                                  <div>
                                    <h5 className="text-lg font-bold mb-3 text-red-400">Invalid Rows</h5>
                                    <div className="max-h-40 overflow-y-auto space-y-2">
                                      {excelValidation.invalid_rows.map((row, index) => (
                                        <div key={index} className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                                          <div className="text-sm">
                                            <strong>Row {row.row}:</strong> {row.name} - {row.error}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}

                        <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                          <h5 className="font-bold mb-2 flex items-center gap-2">
                            <Info className="w-4 h-4" />
                            Excel File Format
                          </h5>
                          <div className="text-sm text-gray-300 space-y-1">
                            <p>• Column 1: <strong>name</strong> - Candidate's full name</p>
                            <p>• Column 2: <strong>resume_link</strong> - Google Drive link to resume</p>
                            <p>• Supported link formats: drive.google.com/file/d/ID, docs.google.com/document/d/ID</p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>

                {/* Job Requirements Section - Keep existing code */}
                <motion.div 
                  className="glass-intense p-8 lg:p-12 rounded-3xl hover-glow"
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <h3 className="text-3xl font-bold mb-8 flex items-center gap-4">
                    <div className="icon-container">
                      <Target className="w-8 h-8 text-blue-400" />
                    </div>
                    Position Requirements
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Position Title *
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.position_title}
                        onChange={(e) => handleInputChange('position_title', e.target.value)}
                        className="form-input w-full"
                        placeholder="Senior Software Engineer"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Company Name
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.company_name}
                        onChange={(e) => handleInputChange('company_name', e.target.value)}
                        className="form-input w-full"
                        placeholder="Your Company Name"
                      />
                    </div>
                  </div>

                  <div className="mt-8">
                    <label className="block text-sm font-semibold mb-3 text-gray-300">
                      Position Description *
                    </label>
                    <textarea
                      value={jobRequirements.job_description}
                      onChange={(e) => handleInputChange('job_description', e.target.value)}
                      className="form-textarea w-full"
                      rows={6}
                      placeholder="Enter detailed position description including responsibilities, requirements, and qualifications..."
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Required Skills (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.required_skills}
                        onChange={(e) => handleInputChange('required_skills', e.target.value)}
                        className="form-input w-full"
                        placeholder="Python, React, AWS, Machine Learning"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Preferred Skills (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.preferred_skills}
                        onChange={(e) => handleInputChange('preferred_skills', e.target.value)}
                        className="form-input w-full"
                        placeholder="Docker, Kubernetes, GraphQL"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Experience Level
                      </label>
                      <select
                        value={jobRequirements.experience_level}
                        onChange={(e) => handleInputChange('experience_level', e.target.value)}
                        className="form-input w-full"
                      >
                        <option value="">Select experience level</option>
                        <option value="Entry Level">Entry Level (0-2 years)</option>
                        <option value="Mid Level">Mid Level (3-5 years)</option>
                        <option value="Senior Level">Senior Level (6-10 years)</option>
                        <option value="Executive Level">Executive Level (10+ years)</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Location
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        className="form-input w-full"
                        placeholder="San Francisco, CA or Remote"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Education Requirements
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.education_requirements}
                        onChange={(e) => handleInputChange('education_requirements', e.target.value)}
                        className="form-input w-full"
                        placeholder="Bachelor's degree in Computer Science"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-semibold mb-3 text-gray-300">
                        Additional Keywords (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={jobRequirements.keywords}
                        onChange={(e) => handleInputChange('keywords', e.target.value)}
                        className="form-input w-full"
                        placeholder="Agile, Scrum, Leadership"
                      />
                    </div>
                  </div>
                </motion.div>

                {/* Submit Button */}
                <motion.div 
                  className="text-center"
                  initial={{ opacity: 0, y: 40 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <button
                    type="submit"
                    className="btn-primary text-xl px-12 py-6 mx-auto hover-lift"
                    disabled={!canProceedWithAnalysis()}
                  >
                    <Brain className="w-7 h-7" />
                    Begin AI Analysis
                    <Sparkles className="w-7 h-7" />
                  </button>
                  <p className="text-sm text-gray-400 mt-4">
                    Our AI agents will analyze {getFileCountText()} with advanced intelligence
                  </p>
                </motion.div>
              </form>
            </motion.div>
          )}

          {step === 'processing' && (
            <ProcessingAnimation
              progress={processingProgress}
              currentStep={currentProcessingStep}
              filesCount={
                uploadMethod === 'files' ? files.length :
                uploadMethod === 'google_drive' ? googleDriveFiles.length :
                uploadMethod === 'excel' ? (excelValidation?.valid_rows || 0) : 0
              }
            />
          )}

          {step === 'results' && analysisResult && (
            <ResultsDisplay
              result={analysisResult}
              onStartNew={handleStartNew}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AnalysisPage;