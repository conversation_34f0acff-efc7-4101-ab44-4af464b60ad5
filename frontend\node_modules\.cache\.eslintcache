[{"D:\\Projects\\ai-hr-agent\\frontend\\src\\index.tsx": "1", "D:\\Projects\\ai-hr-agent\\frontend\\src\\App.tsx": "2", "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\AnalysisPage.tsx": "3", "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\HomePage.tsx": "4", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ProcessingAnimation.tsx": "5", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\WorkflowAnimation.tsx": "6", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\AnimatedBackground.tsx": "7", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ResultsDisplay.tsx": "8", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\FeatureSection.tsx": "9", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\GoogleOAuthHandler.tsx": "10"}, {"size": 263, "mtime": 1748957615758, "results": "11", "hashOfConfig": "12"}, {"size": 1068, "mtime": 1748969092329, "results": "13", "hashOfConfig": "12"}, {"size": 46285, "mtime": 1748969111494, "results": "14", "hashOfConfig": "12"}, {"size": 18291, "mtime": 1748966080469, "results": "15", "hashOfConfig": "12"}, {"size": 9980, "mtime": 1748957114698, "results": "16", "hashOfConfig": "12"}, {"size": 15994, "mtime": 1748957086803, "results": "17", "hashOfConfig": "12"}, {"size": 6948, "mtime": 1748956767263, "results": "18", "hashOfConfig": "12"}, {"size": 17143, "mtime": 1748956623652, "results": "19", "hashOfConfig": "12"}, {"size": 4414, "mtime": 1748966045888, "results": "20", "hashOfConfig": "12"}, {"size": 1921, "mtime": 1748968674946, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1um90xm", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\ai-hr-agent\\frontend\\src\\index.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\App.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\AnalysisPage.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\HomePage.tsx", ["52", "53"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ProcessingAnimation.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\WorkflowAnimation.tsx", ["54"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\AnimatedBackground.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ResultsDisplay.tsx", ["55", "56", "57", "58"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\FeatureSection.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\GoogleOAuthHandler.tsx", [], [], {"ruleId": "59", "severity": 1, "message": "60", "line": 8, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 8, "endColumn": 12}, {"ruleId": "59", "severity": 1, "message": "63", "line": 9, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 9, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "64", "line": 12, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 12, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "64", "line": 12, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 12, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "65", "line": 19, "column": 8, "nodeType": "61", "messageId": "62", "endLine": 19, "endColumn": 19}, {"ruleId": "59", "severity": 1, "message": "66", "line": 96, "column": 9, "nodeType": "61", "messageId": "62", "endLine": 96, "endColumn": 22}, {"ruleId": "59", "severity": 1, "message": "67", "line": 103, "column": 9, "nodeType": "61", "messageId": "62", "endLine": 103, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'BarChart3' is defined but never used.", "Identifier", "unusedVar", "'Users' is defined but never used.", "'Clock' is defined but never used.", "'html2canvas' is defined but never used.", "'getScoreColor' is assigned a value but never used.", "'getScoreLabel' is assigned a value but never used."]