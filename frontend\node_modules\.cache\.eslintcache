[{"D:\\Projects\\ai-hr-agent\\frontend\\src\\index.tsx": "1", "D:\\Projects\\ai-hr-agent\\frontend\\src\\App.tsx": "2", "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\AnalysisPage.tsx": "3", "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\HomePage.tsx": "4", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ProcessingAnimation.tsx": "5", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\WorkflowAnimation.tsx": "6", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\AnimatedBackground.tsx": "7", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ResultsDisplay.tsx": "8", "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\FeatureSection.tsx": "9"}, {"size": 263, "mtime": 1748957615758, "results": "10", "hashOfConfig": "11"}, {"size": 918, "mtime": 1748957396875, "results": "12", "hashOfConfig": "11"}, {"size": 21816, "mtime": 1748964994179, "results": "13", "hashOfConfig": "11"}, {"size": 18523, "mtime": 1748965854588, "results": "14", "hashOfConfig": "11"}, {"size": 9980, "mtime": 1748957114698, "results": "15", "hashOfConfig": "11"}, {"size": 15994, "mtime": 1748957086803, "results": "16", "hashOfConfig": "11"}, {"size": 6948, "mtime": 1748956767263, "results": "17", "hashOfConfig": "11"}, {"size": 17143, "mtime": 1748956623652, "results": "18", "hashOfConfig": "11"}, {"size": 9415, "mtime": 1748965873070, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1um90xm", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects\\ai-hr-agent\\frontend\\src\\index.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\App.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\AnalysisPage.tsx", ["47", "48", "49", "50", "51", "52", "53", "54", "55"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\pages\\HomePage.tsx", ["56", "57", "58", "59"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ProcessingAnimation.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\WorkflowAnimation.tsx", ["60"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\AnimatedBackground.tsx", [], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\ResultsDisplay.tsx", ["61", "62", "63", "64"], [], "D:\\Projects\\ai-hr-agent\\frontend\\src\\components\\FeatureSection.tsx", ["65"], [], {"ruleId": "66", "severity": 1, "message": "67", "line": 11, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 11, "endColumn": 6}, {"ruleId": "66", "severity": 1, "message": "70", "line": 13, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 13, "endColumn": 9}, {"ruleId": "66", "severity": 1, "message": "71", "line": 14, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 14, "endColumn": 9}, {"ruleId": "66", "severity": 1, "message": "72", "line": 15, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 15, "endColumn": 12}, {"ruleId": "66", "severity": 1, "message": "73", "line": 17, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 17, "endColumn": 14}, {"ruleId": "66", "severity": 1, "message": "74", "line": 18, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 18, "endColumn": 11}, {"ruleId": "66", "severity": 1, "message": "75", "line": 19, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 19, "endColumn": 6}, {"ruleId": "66", "severity": 1, "message": "76", "line": 20, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 20, "endColumn": 8}, {"ruleId": "66", "severity": 1, "message": "77", "line": 24, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 24, "endColumn": 6}, {"ruleId": "66", "severity": 1, "message": "72", "line": 8, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 8, "endColumn": 12}, {"ruleId": "66", "severity": 1, "message": "78", "line": 9, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 9, "endColumn": 8}, {"ruleId": "66", "severity": 1, "message": "79", "line": 13, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 13, "endColumn": 7}, {"ruleId": "66", "severity": 1, "message": "76", "line": 15, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 15, "endColumn": 8}, {"ruleId": "66", "severity": 1, "message": "76", "line": 12, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 12, "endColumn": 8}, {"ruleId": "66", "severity": 1, "message": "76", "line": 12, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 12, "endColumn": 8}, {"ruleId": "66", "severity": 1, "message": "80", "line": 19, "column": 8, "nodeType": "68", "messageId": "69", "endLine": 19, "endColumn": 19}, {"ruleId": "66", "severity": 1, "message": "81", "line": 96, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 96, "endColumn": 22}, {"ruleId": "66", "severity": 1, "message": "82", "line": 103, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 103, "endColumn": 22}, {"ruleId": "66", "severity": 1, "message": "76", "line": 10, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 10, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'Zap' is defined but never used.", "Identifier", "unusedVar", "'Search' is defined but never used.", "'Shield' is defined but never used.", "'BarChart3' is defined but never used.", "'AlertCircle' is defined but never used.", "'Download' is defined but never used.", "'Eye' is defined but never used.", "'Clock' is defined but never used.", "'Cpu' is defined but never used.", "'Users' is defined but never used.", "'Star' is defined but never used.", "'html2canvas' is defined but never used.", "'getScoreColor' is assigned a value but never used.", "'getScoreLabel' is assigned a value but never used."]