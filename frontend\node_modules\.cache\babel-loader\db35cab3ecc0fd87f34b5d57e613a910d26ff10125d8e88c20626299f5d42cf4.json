{"ast": null, "code": "import { createHtmlRenderState } from '../../html/utils/create-render-state.mjs';\nconst createSvgRenderState = () => ({\n  ...createHtmlRenderState(),\n  attrs: {}\n});\nexport { createSvgRenderState };", "map": {"version": 3, "names": ["createHtmlRenderState", "createSvgRenderState", "attrs"], "sources": ["D:/Projects/ai-hr-agent/frontend/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs"], "sourcesContent": ["import { createHtmlRenderState } from '../../html/utils/create-render-state.mjs';\n\nconst createSvgRenderState = () => ({\n    ...createHtmlRenderState(),\n    attrs: {},\n});\n\nexport { createSvgRenderState };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,0CAA0C;AAEhF,MAAMC,oBAAoB,GAAGA,CAAA,MAAO;EAChC,GAAGD,qBAAqB,CAAC,CAAC;EAC1BE,KAAK,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,SAASD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}