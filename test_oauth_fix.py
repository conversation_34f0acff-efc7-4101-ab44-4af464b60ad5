#!/usr/bin/env python3
"""
Test script for Google OAuth fixes in ResumeGPT Pro
Tests the OAuth flow and state management
"""

import requests
import json
import time
from urllib.parse import urlparse, parse_qs

# Configuration
BASE_URL = "http://localhost:8000"

def test_oauth_url_generation():
    """Test OAuth URL generation and state management"""
    print("🔍 Testing OAuth URL generation...")
    try:
        response = requests.get(f"{BASE_URL}/auth/google/url", timeout=5)
        if response.status_code == 200:
            data = response.json()
            auth_url = data.get('auth_url')
            state = data.get('state')
            
            if auth_url and state:
                print("✅ OAuth URL generated successfully")
                print(f"   State: {state}")
                print(f"   URL: {auth_url[:80]}...")
                
                # Parse the URL to verify state is included
                parsed_url = urlparse(auth_url)
                query_params = parse_qs(parsed_url.query)
                url_state = query_params.get('state', [None])[0]
                
                if url_state == state:
                    print("✅ State parameter correctly included in URL")
                    return True, state
                else:
                    print(f"❌ State mismatch: URL has {url_state}, response has {state}")
                    return False, None
            else:
                print("❌ Missing auth_url or state in response")
                return False, None
        else:
            print(f"❌ OAuth URL generation failed: {response.status_code}")
            if response.status_code == 500:
                print("   💡 This likely means client_secrets.json is missing")
            return False, None
    except requests.exceptions.RequestException as e:
        print(f"❌ OAuth URL generation test failed: {e}")
        return False, None

def test_oauth_debug_endpoint():
    """Test the OAuth debug endpoint"""
    print("\n🔍 Testing OAuth debug endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/auth/google/debug", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ OAuth debug endpoint accessible")
            print(f"   Active sessions: {data.get('active_sessions', 0)}")
            
            sessions = data.get('sessions', {})
            for state, session_data in sessions.items():
                print(f"   Session {state[:8]}...: age={session_data.get('age_seconds', 0):.1f}s, used={session_data.get('used', False)}")
            
            return True
        else:
            print(f"❌ OAuth debug endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ OAuth debug test failed: {e}")
        return False

def test_oauth_callback_with_invalid_state():
    """Test OAuth callback with invalid state"""
    print("\n🔍 Testing OAuth callback with invalid state...")
    try:
        # Test with completely invalid state
        response = requests.get(
            f"{BASE_URL}/auth/google/callback?code=test_code&state=invalid_state",
            timeout=5
        )
        
        if response.status_code == 400:
            print("✅ Invalid state correctly rejected")
            return True
        else:
            print(f"❌ Expected 400 error, got {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ OAuth callback test failed: {e}")
        return False

def test_oauth_state_cleanup():
    """Test OAuth state cleanup functionality"""
    print("\n🔍 Testing OAuth state cleanup...")
    try:
        # Generate a few OAuth URLs to create states
        states = []
        for i in range(3):
            response = requests.get(f"{BASE_URL}/auth/google/url", timeout=5)
            if response.status_code == 200:
                data = response.json()
                states.append(data.get('state'))
        
        print(f"   Generated {len(states)} test states")
        
        # Check debug endpoint
        response = requests.get(f"{BASE_URL}/auth/google/debug", timeout=5)
        if response.status_code == 200:
            data = response.json()
            active_sessions = data.get('active_sessions', 0)
            print(f"   Active sessions: {active_sessions}")
            
            if active_sessions >= len(states):
                print("✅ States are being stored correctly")
                return True
            else:
                print("❌ Not all states were stored")
                return False
        else:
            print("❌ Could not check debug endpoint")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ OAuth state cleanup test failed: {e}")
        return False

def test_oauth_callback_html_response():
    """Test that OAuth callback returns HTML (not JSON)"""
    print("\n🔍 Testing OAuth callback HTML response...")
    try:
        # First generate a valid state
        response = requests.get(f"{BASE_URL}/auth/google/url", timeout=5)
        if response.status_code != 200:
            print("❌ Could not generate OAuth URL for test")
            return False
        
        data = response.json()
        valid_state = data.get('state')
        
        # Test callback with valid state but dummy code
        response = requests.get(
            f"{BASE_URL}/auth/google/callback?code=dummy_code&state={valid_state}",
            timeout=5
        )
        
        # This should fail at token exchange, but we can check if it gets past state validation
        if response.status_code == 400:
            # Check if it's a token exchange error (not state error)
            if "text/html" in response.headers.get('content-type', ''):
                print("✅ OAuth callback returns HTML response")
                return True
            else:
                try:
                    error_data = response.json()
                    error_detail = error_data.get('detail', '')
                    if 'state' in error_detail.lower():
                        print(f"❌ State validation still failing: {error_detail}")
                        return False
                    else:
                        print("✅ State validation passed, failed at token exchange (expected)")
                        return True
                except:
                    print("✅ OAuth callback returns non-JSON response (likely HTML)")
                    return True
        else:
            print(f"❌ Unexpected response code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ OAuth callback HTML test failed: {e}")
        return False

def run_oauth_tests():
    """Run all OAuth tests"""
    print("🚀 Starting Google OAuth Fix Test Suite")
    print("=" * 60)
    
    tests = [
        ("OAuth URL Generation", test_oauth_url_generation),
        ("OAuth Debug Endpoint", test_oauth_debug_endpoint),
        ("Invalid State Rejection", test_oauth_callback_with_invalid_state),
        ("State Cleanup", test_oauth_state_cleanup),
        ("HTML Response", test_oauth_callback_html_response),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if test_name == "OAuth URL Generation":
                result, state = test_func()
                results[test_name] = result
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 OAUTH TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All OAuth tests passed! The fixes are working correctly.")
        print("\n💡 Next steps:")
        print("   1. Test the OAuth flow in the browser")
        print("   2. Ensure client_secrets.json is properly configured")
        print("   3. Try connecting Google Drive in the frontend")
    else:
        print("⚠️  Some OAuth tests failed. Check the implementation.")
        
        if not results.get("OAuth URL Generation"):
            print("\n💡 OAuth URL generation failed:")
            print("   1. Make sure the backend server is running")
            print("   2. Add client_secrets.json to backend/google_credentials/")
        
        if not results.get("Invalid State Rejection"):
            print("\n💡 State validation issues:")
            print("   1. Check the state management logic in the callback")
            print("   2. Verify state parameter handling")

if __name__ == "__main__":
    run_oauth_tests()
