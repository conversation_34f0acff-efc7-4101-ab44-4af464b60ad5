import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText, 
  Brain, 
  Search, 
  CheckCircle, 
  BarChart3,
  Shield,
  Users,
  Zap,
  Clock
} from 'lucide-react';

const WorkflowAnimation: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  const steps = [
    {
      id: 'upload',
      title: 'Resume Upload',
      description: 'Upload multiple resume files for analysis',
      icon: FileText,
      color: 'text-blue-400',
      bgColor: 'bg-blue-400/20',
      details: 'Support for PDF, DOC, DOCX files with parallel processing'
    },
    {
      id: 'analysis',
      title: 'AI Analysis',
      description: 'Advanced AI extracts and structures candidate data',
      icon: Brain,
      color: 'text-purple-400',
      bgColor: 'bg-purple-400/20',
      details: 'GPT-4 powered extraction with 95% accuracy'
    },
    {
      id: 'matching',
      title: 'Smart Matching',
      description: 'Intelligent matching against job requirements',
      icon: Users,
      color: 'text-green-400',
      bgColor: 'bg-green-400/20',
      details: 'Multi-criteria scoring with semantic analysis'
    },
    {
      id: 'research',
      title: 'Deep Research',
      description: 'Automated research across professional platforms',
      icon: Search,
      color: 'text-orange-400',
      bgColor: 'bg-orange-400/20',
      details: 'LinkedIn, GitHub, and web presence analysis'
    },
    {
      id: 'validation',
      title: 'Identity Validation',
      description: 'Cross-platform validation for authenticity',
      icon: Shield,
      color: 'text-red-400',
      bgColor: 'bg-red-400/20',
      details: 'Multi-factor verification and consistency checks'
    },
    {
      id: 'reporting',
      title: 'Report Generation',
      description: 'Comprehensive reports with insights and rankings',
      icon: BarChart3,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-400/20',
      details: 'Executive summaries and detailed candidate profiles'
    }
  ];

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % steps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [isPlaying, steps.length]);

  const handleStepClick = (index: number) => {
    setCurrentStep(index);
    setIsPlaying(false);
    setTimeout(() => setIsPlaying(true), 5000);
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Progress bar */}
      <div className="mb-12">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className="flex items-center cursor-pointer"
              onClick={() => handleStepClick(index)}
            >
              <motion.div
                className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                  index <= currentStep
                    ? `${step.bgColor} border-white/30`
                    : 'bg-white/5 border-white/10'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <step.icon 
                  className={`w-6 h-6 ${
                    index <= currentStep ? step.color : 'text-white/30'
                  }`} 
                />
              </motion.div>
              {index < steps.length - 1 && (
                <motion.div
                  className="flex-1 h-0.5 mx-4 bg-white/10 relative overflow-hidden"
                  style={{ minWidth: '60px' }}
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400"
                    initial={{ scaleX: 0 }}
                    animate={{ 
                      scaleX: index < currentStep ? 1 : 0 
                    }}
                    transition={{ duration: 0.5 }}
                    style={{ transformOrigin: 'left' }}
                  />
                </motion.div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main content area */}
      <div className="glass-intense p-8 rounded-3xl min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            className="grid md:grid-cols-2 gap-8 items-center"
          >
            {/* Left side - Content */}
            <div>
              <div className="flex items-center gap-4 mb-6">
                <div className={`p-4 rounded-2xl ${steps[currentStep].bgColor}`}>
                  {(() => {
                    const Icon = steps[currentStep].icon;
                    return <Icon className={`w-8 h-8 ${steps[currentStep].color}`} />;
                  })()}
                </div>
                <div>
                  <span className="text-sm text-gray-400 font-medium">
                    Step {currentStep + 1} of {steps.length}
                  </span>
                  <h3 className="text-3xl font-bold text-white">
                    {steps[currentStep].title}
                  </h3>
                </div>
              </div>

              <p className="text-xl text-gray-300 mb-4">
                {steps[currentStep].description}
              </p>

              <p className="text-gray-400 mb-6">
                {steps[currentStep].details}
              </p>

              {/* Features for current step */}
              <div className="space-y-3">
                {getStepFeatures(currentStep).map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-3"
                  >
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Right side - Visualization */}
            <div className="relative">
              <motion.div
                className="aspect-square max-w-sm mx-auto relative"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6 }}
              >
                {/* Animated visualization based on current step */}
                <StepVisualization step={currentStep} />
              </motion.div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Control buttons */}
      <div className="flex justify-center gap-4 mt-8">
        <button
          onClick={() => setIsPlaying(!isPlaying)}
          className="btn-secondary px-6 py-3 flex items-center gap-2"
        >
          {isPlaying ? 'Pause' : 'Play'}
          <Zap className="w-4 h-4" />
        </button>
        <button
          onClick={() => setCurrentStep(0)}
          className="btn-primary px-6 py-3"
        >
          Restart Demo
        </button>
      </div>
    </div>
  );
};

const getStepFeatures = (step: number): string[] => {
  const features = [
    [
      'Multiple file format support',
      'Batch processing capability',
      'Secure file handling',
      'Real-time upload progress'
    ],
    [
      'GPT-4 powered extraction',
      'Structured data parsing',
      'Skills categorization',
      'Experience timeline analysis'
    ],
    [
      'Multi-criteria scoring algorithm',
      'Semantic similarity matching',
      'Ranking and prioritization',
      'Customizable weight factors'
    ],
    [
      'LinkedIn profile discovery',
      'GitHub repository analysis',
      'Professional network mapping',
      'Publication and project research'
    ],
    [
      'Cross-platform verification',
      'Identity consistency checks',
      'Credential validation',
      'Fraud detection algorithms'
    ],
    [
      'Executive summary generation',
      'Individual candidate reports',
      'Comparative analysis',
      'Downloadable PDF reports'
    ]
  ];
  
  return features[step] || [];
};

const StepVisualization: React.FC<{ step: number }> = ({ step }) => {
  const visualizations = [
    // Upload visualization
    <div key="upload" className="relative w-full h-full flex items-center justify-center">
      <motion.div
        className="w-32 h-32 border-2 border-dashed border-blue-400 rounded-xl flex items-center justify-center"
        animate={{ 
          borderColor: ['rgba(59, 130, 246, 0.5)', 'rgba(59, 130, 246, 1)', 'rgba(59, 130, 246, 0.5)']
        }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <FileText className="w-12 h-12 text-blue-400" />
      </motion.div>
      {/* Floating file icons */}
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-8 h-8 bg-blue-400/20 rounded-lg flex items-center justify-center"
          style={{
            left: `${20 + i * 30}%`,
            top: `${20 + i * 20}%`
          }}
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: i * 0.3
          }}
        >
          <FileText className="w-4 h-4 text-blue-400" />
        </motion.div>
      ))}
    </div>,

    // AI Analysis visualization
    <div key="analysis" className="relative w-full h-full flex items-center justify-center">
      <motion.div
        className="w-32 h-32 rounded-full bg-purple-400/20 flex items-center justify-center relative"
        animate={{ rotate: 360 }}
        transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
      >
        <Brain className="w-12 h-12 text-purple-400" />
        {/* Pulsing rings */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute border border-purple-400/30 rounded-full"
            style={{
              width: `${140 + i * 20}px`,
              height: `${140 + i * 20}px`
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.1, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.5
            }}
          />
        ))}
      </motion.div>
    </div>,

    // Matching visualization
    <div key="matching" className="relative w-full h-full flex items-center justify-center">
      <div className="grid grid-cols-2 gap-8 items-center">
        <motion.div
          className="w-20 h-20 bg-green-400/20 rounded-xl flex items-center justify-center"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Users className="w-8 h-8 text-green-400" />
        </motion.div>
        <motion.div
          className="w-20 h-20 bg-blue-400/20 rounded-xl flex items-center justify-center"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
        >
          <FileText className="w-8 h-8 text-blue-400" />
        </motion.div>
      </div>
      {/* Connecting lines */}
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 1, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-16 h-0.5 bg-gradient-to-r from-green-400 to-blue-400" />
      </motion.div>
    </div>,

    // Research visualization
    <div key="research" className="relative w-full h-full flex items-center justify-center">
      <motion.div
        className="w-24 h-24 bg-orange-400/20 rounded-full flex items-center justify-center"
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 3, repeat: Infinity }}
      >
        <Search className="w-8 h-8 text-orange-400" />
      </motion.div>
      {/* Radar sweep effect */}
      <motion.div
        className="absolute inset-0 rounded-full border-2 border-orange-400/20"
        animate={{ scale: [1, 2], opacity: [0.5, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      />
      <motion.div
        className="absolute inset-0 rounded-full border-2 border-orange-400/20"
        animate={{ scale: [1, 2], opacity: [0.5, 0] }}
        transition={{ duration: 2, repeat: Infinity, delay: 1 }}
      />
    </div>,

    // Validation visualization
    <div key="validation" className="relative w-full h-full flex items-center justify-center">
      <motion.div
        className="w-24 h-24 bg-red-400/20 rounded-xl flex items-center justify-center"
        animate={{ 
          boxShadow: [
            '0 0 0 0 rgba(248, 113, 113, 0.4)',
            '0 0 0 20px rgba(248, 113, 113, 0)',
          ]
        }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <Shield className="w-8 h-8 text-red-400" />
      </motion.div>
      {/* Checkmarks */}
      {[...Array(4)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-6 h-6 bg-green-400/20 rounded-full flex items-center justify-center"
          style={{
            left: `${50 + Math.cos(i * Math.PI / 2) * 80}px`,
            top: `${50 + Math.sin(i * Math.PI / 2) * 80}px`
          }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: i * 0.5
          }}
        >
          <CheckCircle className="w-4 h-4 text-green-400" />
        </motion.div>
      ))}
    </div>,

    // Reporting visualization
    <div key="reporting" className="relative w-full h-full flex items-center justify-center">
      <motion.div
        className="w-32 h-32 bg-cyan-400/20 rounded-2xl flex items-center justify-center relative overflow-hidden"
        animate={{
          background: [
            'rgba(34, 211, 238, 0.1)',
            'rgba(34, 211, 238, 0.2)',
            'rgba(34, 211, 238, 0.1)'
          ]
        }}
        transition={{ duration: 3, repeat: Infinity }}
      >
        <BarChart3 className="w-12 h-12 text-cyan-400" />
        {/* Animated bars */}
        <div className="absolute bottom-4 left-4 right-4 flex gap-1">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="bg-cyan-400 rounded-sm flex-1"
              animate={{
                height: [
                  `${20 + Math.random() * 20}px`,
                  `${30 + Math.random() * 25}px`,
                  `${20 + Math.random() * 20}px`
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  ];

  return visualizations[step] || visualizations[0];
};

export default WorkflowAnimation;