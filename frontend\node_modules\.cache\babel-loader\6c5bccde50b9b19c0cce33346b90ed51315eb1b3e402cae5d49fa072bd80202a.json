{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\ai-hr-agent\\\\frontend\\\\src\\\\pages\\\\HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { Brain, Search, CheckCircle, BarChart3, Zap, ArrowRight, Play, Shield, Clock } from 'lucide-react';\nimport AnimatedBackground from '../components/AnimatedBackground';\nimport FeatureSection from '../components/FeatureSection';\nimport WorkflowAnimation from '../components/WorkflowAnimation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [isLoaded, setIsLoaded] = useState(false);\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n  const handleGetStarted = () => {\n    navigate('/analysis');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(AnimatedBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      className: \"relative z-10 min-h-screen flex items-center justify-center px-4\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: isLoaded ? 1 : 0\n      },\n      transition: {\n        duration: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"ResumeGPT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: \"Pro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed\",\n            children: \"High-Performance Multi-Agent AI System for Complete Candidate Analysis, Research, and Validation with Comprehensive Reporting\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGetStarted,\n            className: \"btn-primary text-lg px-8 py-4 group\",\n            children: [/*#__PURE__*/_jsxDEV(Zap, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), \"Get Started\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary text-lg px-8 py-4\",\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), \"Watch Demo\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.6\n          },\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass p-6 rounded-xl text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Brain, {\n              className: \"w-12 h-12 mx-auto mb-4 text-blue-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"AI-Powered Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Advanced multi-agent system for comprehensive candidate evaluation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass p-6 rounded-xl text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"w-12 h-12 mx-auto mb-4 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Deep Research\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Automated research across LinkedIn, GitHub, and professional platforms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass p-6 rounded-xl text-center\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"w-12 h-12 mx-auto mb-4 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Detailed Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Comprehensive analysis with rankings, insights, and recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeatureSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-20 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-5xl font-bold mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"How It Works\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n            children: \"Watch our multi-agent system in action as it processes resumes through advanced AI analysis, research, and validation pipelines\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WorkflowAnimation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-20 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"glass-intense p-8 lg:p-12 rounded-3xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl md:text-4xl font-bold mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"gradient-text\",\n                children: \"Performance Optimized\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-gray-300\",\n              children: \"Built for speed, accuracy, and scalability with cutting-edge optimizations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-bold gradient-text mb-2\",\n                children: \"10x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm md:text-base\",\n                children: \"Faster Processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-bold gradient-text mb-2\",\n                children: \"95%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm md:text-base\",\n                children: \"Accuracy Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-bold gradient-text mb-2\",\n                children: \"5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm md:text-base\",\n                children: \"AI Agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl md:text-4xl font-bold gradient-text mb-2\",\n                children: \"50+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm md:text-base\",\n                children: \"Data Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-20 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-5xl font-bold mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Advanced Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n            children: \"Comprehensive suite of tools designed for modern recruitment challenges\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"card hover:scale-105 transition-transform cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-12 h-12 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 leading-relaxed\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-20 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center glass-intense p-8 lg:p-16 rounded-3xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-5xl font-bold mb-6\",\n            children: [\"Ready to \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Transform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 24\n            }, this), \" Your Hiring?\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\",\n            children: \"Experience the future of candidate analysis with our AI-powered platform. Start analyzing resumes with unprecedented depth and accuracy.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGetStarted,\n            className: \"btn-primary text-xl px-12 py-5 group mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(Zap, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), \"Start Analysis Now\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              className: \"w-6 h-6 group-hover:translate-x-1 transition-transform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"relative z-10 py-12 border-t border-gray-800 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold gradient-text mb-2\",\n            children: \"ResumeGPT Pro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"High-Performance Multi-Agent CV Analysis Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-500 text-sm\",\n          children: \"\\xA9 2024 ResumeGPT Pro. Powered by Advanced AI Technology.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"Y2by1Op+c0i5mal9nc1sARD3+0o=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nconst features = [{\n  icon: Brain,\n  title: \"Multi-Agent AI System\",\n  description: \"Five specialized AI agents working in parallel for comprehensive analysis\"\n}, {\n  icon: Search,\n  title: \"Deep Research Engine\",\n  description: \"Automated research across LinkedIn, GitHub, and professional platforms\"\n}, {\n  icon: CheckCircle,\n  title: \"Identity Validation\",\n  description: \"Advanced validation algorithms to ensure candidate authenticity\"\n}, {\n  icon: BarChart3,\n  title: \"Detailed Analytics\",\n  description: \"Comprehensive scoring and ranking with detailed insights\"\n}, {\n  icon: Shield,\n  title: \"Secure Processing\",\n  description: \"Enterprise-grade security with encrypted data handling\"\n}, {\n  icon: Clock,\n  title: \"Real-time Processing\",\n  description: \"Fast parallel processing with real-time progress tracking\"\n}];\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "useNavigate", "Brain", "Search", "CheckCircle", "BarChart3", "Zap", "ArrowRight", "Play", "Shield", "Clock", "AnimatedBackground", "FeatureSection", "WorkflowAnimation", "jsxDEV", "_jsxDEV", "HomePage", "_s", "navigate", "isLoaded", "setIsLoaded", "handleGetStarted", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "section", "initial", "opacity", "animate", "transition", "duration", "div", "y", "delay", "onClick", "whileInView", "viewport", "once", "features", "map", "feature", "index", "icon", "title", "description", "_c", "$RefreshReg$"], "sources": ["D:/Projects/ai-hr-agent/frontend/src/pages/HomePage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { \r\n  Brain, \r\n  Search, \r\n  CheckCircle, \r\n  BarChart3, \r\n  Users, \r\n  Zap,\r\n  ArrowRight,\r\n  Play,\r\n  Star,\r\n  Shield,\r\n  Clock\r\n} from 'lucide-react';\r\nimport AnimatedBackground from '../components/AnimatedBackground';\r\nimport FeatureSection from '../components/FeatureSection';\r\nimport WorkflowAnimation from '../components/WorkflowAnimation';\r\n\r\nconst HomePage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n  }, []);\r\n\r\n  const handleGetStarted = () => {\r\n    navigate('/analysis');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen relative overflow-hidden\">\r\n      <AnimatedBackground />\r\n      \r\n      {/* Hero Section */}\r\n      <motion.section \r\n        className=\"relative z-10 min-h-screen flex items-center justify-center px-4\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: isLoaded ? 1 : 0 }}\r\n        transition={{ duration: 1 }}\r\n      >\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <h1 className=\"text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight\">\r\n              <span className=\"gradient-text\">ResumeGPT</span>\r\n              <br />\r\n              <span className=\"text-white\">Pro</span>\r\n            </h1>\r\n            <p className=\"text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed\">\r\n              High-Performance Multi-Agent AI System for Complete Candidate Analysis, \r\n              Research, and Validation with Comprehensive Reporting\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\"\r\n          >\r\n            <button\r\n              onClick={handleGetStarted}\r\n              className=\"btn-primary text-lg px-8 py-4 group\"\r\n            >\r\n              <Zap className=\"w-5 h-5\" />\r\n              Get Started\r\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\r\n            </button>\r\n            <button className=\"btn-secondary text-lg px-8 py-4\">\r\n              <Play className=\"w-5 h-5\" />\r\n              Watch Demo\r\n            </button>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.6 }}\r\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto\"\r\n          >\r\n            <div className=\"glass p-6 rounded-xl text-center\">\r\n              <Brain className=\"w-12 h-12 mx-auto mb-4 text-blue-400\" />\r\n              <h3 className=\"text-lg font-semibold mb-2\">AI-Powered Analysis</h3>\r\n              <p className=\"text-gray-400 text-sm\">Advanced multi-agent system for comprehensive candidate evaluation</p>\r\n            </div>\r\n            <div className=\"glass p-6 rounded-xl text-center\">\r\n              <Search className=\"w-12 h-12 mx-auto mb-4 text-purple-400\" />\r\n              <h3 className=\"text-lg font-semibold mb-2\">Deep Research</h3>\r\n              <p className=\"text-gray-400 text-sm\">Automated research across LinkedIn, GitHub, and professional platforms</p>\r\n            </div>\r\n            <div className=\"glass p-6 rounded-xl text-center\">\r\n              <BarChart3 className=\"w-12 h-12 mx-auto mb-4 text-green-400\" />\r\n              <h3 className=\"text-lg font-semibold mb-2\">Detailed Reports</h3>\r\n              <p className=\"text-gray-400 text-sm\">Comprehensive analysis with rankings, insights, and recommendations</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </motion.section>\r\n\r\n      {/* Features Section */}\r\n      <FeatureSection />\r\n\r\n      {/* Workflow Animation Section */}\r\n      <section className=\"relative z-10 py-20 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-3xl md:text-5xl font-bold mb-6\">\r\n              <span className=\"gradient-text\">How It Works</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n              Watch our multi-agent system in action as it processes resumes through \r\n              advanced AI analysis, research, and validation pipelines\r\n            </p>\r\n          </motion.div>\r\n          \r\n          <WorkflowAnimation />\r\n        </div>\r\n      </section>\r\n\r\n      {/* Performance Stats */}\r\n      <section className=\"relative z-10 py-20 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"glass-intense p-8 lg:p-12 rounded-3xl\"\r\n          >\r\n            <div className=\"text-center mb-12\">\r\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\r\n                <span className=\"gradient-text\">Performance Optimized</span>\r\n              </h2>\r\n              <p className=\"text-xl text-gray-300\">\r\n                Built for speed, accuracy, and scalability with cutting-edge optimizations\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-3xl md:text-4xl font-bold gradient-text mb-2\">10x</div>\r\n                <div className=\"text-gray-300 text-sm md:text-base\">Faster Processing</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-3xl md:text-4xl font-bold gradient-text mb-2\">95%</div>\r\n                <div className=\"text-gray-300 text-sm md:text-base\">Accuracy Rate</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-3xl md:text-4xl font-bold gradient-text mb-2\">5</div>\r\n                <div className=\"text-gray-300 text-sm md:text-base\">AI Agents</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-3xl md:text-4xl font-bold gradient-text mb-2\">50+</div>\r\n                <div className=\"text-gray-300 text-sm md:text-base\">Data Points</div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Grid */}\r\n      <section className=\"relative z-10 py-20 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-3xl md:text-5xl font-bold mb-6\">\r\n              <span className=\"gradient-text\">Advanced Features</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n              Comprehensive suite of tools designed for modern recruitment challenges\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {features.map((feature, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"card hover:scale-105 transition-transform cursor-pointer\"\r\n              >\r\n                <div className=\"mb-4\">\r\n                  <feature.icon className=\"w-12 h-12 text-blue-400\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-semibold mb-3\">{feature.title}</h3>\r\n                <p className=\"text-gray-400 leading-relaxed\">{feature.description}</p>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"relative z-10 py-20 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center glass-intense p-8 lg:p-16 rounded-3xl\"\r\n          >\r\n            <h2 className=\"text-3xl md:text-5xl font-bold mb-6\">\r\n              Ready to <span className=\"gradient-text\">Transform</span> Your Hiring?\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\r\n              Experience the future of candidate analysis with our AI-powered platform. \r\n              Start analyzing resumes with unprecedented depth and accuracy.\r\n            </p>\r\n            <button\r\n              onClick={handleGetStarted}\r\n              className=\"btn-primary text-xl px-12 py-5 group mx-auto\"\r\n            >\r\n              <Zap className=\"w-6 h-6\" />\r\n              Start Analysis Now\r\n              <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\r\n            </button>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"relative z-10 py-12 border-t border-gray-800 px-4\">\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          <div className=\"mb-8\">\r\n            <h3 className=\"text-2xl font-bold gradient-text mb-2\">ResumeGPT Pro</h3>\r\n            <p className=\"text-gray-400\">High-Performance Multi-Agent CV Analysis Platform</p>\r\n          </div>\r\n          <div className=\"text-gray-500 text-sm\">\r\n            © 2024 ResumeGPT Pro. Powered by Advanced AI Technology.\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst features = [\r\n  {\r\n    icon: Brain,\r\n    title: \"Multi-Agent AI System\",\r\n    description: \"Five specialized AI agents working in parallel for comprehensive analysis\"\r\n  },\r\n  {\r\n    icon: Search,\r\n    title: \"Deep Research Engine\",\r\n    description: \"Automated research across LinkedIn, GitHub, and professional platforms\"\r\n  },\r\n  {\r\n    icon: CheckCircle,\r\n    title: \"Identity Validation\",\r\n    description: \"Advanced validation algorithms to ensure candidate authenticity\"\r\n  },\r\n  {\r\n    icon: BarChart3,\r\n    title: \"Detailed Analytics\",\r\n    description: \"Comprehensive scoring and ranking with detailed insights\"\r\n  },\r\n  {\r\n    icon: Shield,\r\n    title: \"Secure Processing\",\r\n    description: \"Enterprise-grade security with encrypted data handling\"\r\n  },\r\n  {\r\n    icon: Clock,\r\n    title: \"Real-time Processing\",\r\n    description: \"Fast parallel processing with real-time progress tracking\"\r\n  }\r\n];\r\n\r\nexport default HomePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,SAAS,EAETC,GAAG,EACHC,UAAU,EACVC,IAAI,EAEJC,MAAM,EACNC,KAAK,QACA,cAAc;AACrB,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACdsB,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BH,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED,oBACEH,OAAA;IAAKO,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDR,OAAA,CAACJ,kBAAkB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtBZ,OAAA,CAACf,MAAM,CAAC4B,OAAO;MACbN,SAAS,EAAC,kEAAkE;MAC5EO,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAEX,QAAQ,GAAG,CAAC,GAAG;MAAE,CAAE;MACvCa,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAE,CAAE;MAAAV,QAAA,eAE5BR,OAAA;QAAKO,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CR,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAEhBR,OAAA;YAAIO,SAAS,EAAC,+DAA+D;YAAAC,QAAA,gBAC3ER,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDZ,OAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNZ,OAAA;cAAMO,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,qFAAqF;YAAAC,QAAA,EAAC;UAGnG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbZ,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAE7ER,OAAA;YACEsB,OAAO,EAAEhB,gBAAiB;YAC1BC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAE/CR,OAAA,CAACT,GAAG;cAACgB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3B,eAAAZ,OAAA,CAACR,UAAU;cAACe,SAAS,EAAC;YAAwD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACTZ,OAAA;YAAQO,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBACjDR,OAAA,CAACP,IAAI;cAACc,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAE9B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEbZ,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBAEnER,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CR,OAAA,CAACb,KAAK;cAACoB,SAAS,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DZ,OAAA;cAAIO,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEZ,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CR,OAAA,CAACZ,MAAM;cAACmB,SAAS,EAAC;YAAwC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DZ,OAAA;cAAIO,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DZ,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAsE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CR,OAAA,CAACV,SAAS;cAACiB,SAAS,EAAC;YAAuC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DZ,OAAA;cAAIO,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEZ,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBZ,OAAA,CAACH,cAAc;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGlBZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BG,WAAW,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBlB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BR,OAAA;YAAIO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACjDR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAGvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbZ,OAAA,CAACF,iBAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BG,WAAW,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBlB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEjDR,OAAA;YAAKO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCR,OAAA;cAAIO,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eACjDR,OAAA;gBAAMO,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACLZ,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENZ,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDR,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EZ,OAAA;gBAAKO,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EZ,OAAA;gBAAKO,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EZ,OAAA;gBAAKO,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EZ,OAAA;gBAAKO,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BG,WAAW,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBlB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BR,OAAA;YAAIO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACjDR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbZ,OAAA;UAAKO,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEkB,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B7B,OAAA,CAACf,MAAM,CAACkC,GAAG;YAETL,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAG,CAAE;YAC/BG,WAAW,EAAE;cAAER,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEQ,KAAK,GAAG;YAAI,CAAE;YAClDL,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBlB,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAEpER,OAAA;cAAKO,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBR,OAAA,CAAC4B,OAAO,CAACE,IAAI;gBAACvB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNZ,OAAA;cAAIO,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEoB,OAAO,CAACG;YAAK;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/DZ,OAAA;cAAGO,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAEoB,OAAO,CAACI;YAAW;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAXjEiB,KAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA,CAACf,MAAM,CAACkC,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BG,WAAW,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBlB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7DR,OAAA;YAAIO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,WACzC,eAAAR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAC3D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAG5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YACEsB,OAAO,EAAEhB,gBAAiB;YAC1BC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAExDR,OAAA,CAACT,GAAG;cAACgB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAE3B,eAAAZ,OAAA,CAACR,UAAU;cAACe,SAAS,EAAC;YAAwD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAAQO,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eACnER,OAAA;QAAKO,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CR,OAAA;UAAKO,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBR,OAAA;YAAIO,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEZ,OAAA;YAAGO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNZ,OAAA;UAAKO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAEvC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACV,EAAA,CA3OID,QAAkB;EAAA,QACLf,WAAW;AAAA;AAAA+C,EAAA,GADxBhC,QAAkB;AA6OxB,MAAMyB,QAAQ,GAAG,CACf;EACEI,IAAI,EAAE3C,KAAK;EACX4C,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE1C,MAAM;EACZ2C,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAEzC,WAAW;EACjB0C,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAExC,SAAS;EACfyC,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAEpC,MAAM;EACZqC,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAEnC,KAAK;EACXoC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE;AACf,CAAC,CACF;AAED,eAAe/B,QAAQ;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}