{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\ai-hr-agent\\\\frontend\\\\src\\\\components\\\\GoogleOAuthHandler.tsx\",\n  _s = $RefreshSig$();\n// GoogleOAuthHandler.tsx - Handle Google OAuth callback in popup\nimport React, { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GoogleOAuthHandler = () => {\n  _s();\n  useEffect(() => {\n    // Extract parameters from URL\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    const error = urlParams.get('error');\n    const handleOAuthCallback = async () => {\n      if (error) {\n        console.error('OAuth error:', error);\n        window.close();\n        return;\n      }\n      if (code && state) {\n        try {\n          // Exchange code for token\n          const response = await fetch(`http://localhost:8000/auth/google/callback?code=${code}&state=${state}`);\n          const data = await response.json();\n          if (data.success && data.access_token) {\n            // Store token in localStorage for parent window to access\n            localStorage.setItem('google_access_token', data.access_token);\n\n            // Close popup - parent window will detect this\n            window.close();\n          } else {\n            console.error('Token exchange failed:', data);\n            window.close();\n          }\n        } catch (error) {\n          console.error('Error exchanging code for token:', error);\n          window.close();\n        }\n      } else {\n        console.error('Missing code or state parameter');\n        window.close();\n      }\n    };\n    handleOAuthCallback();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Completing Google Drive connection...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(GoogleOAuthHandler, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = GoogleOAuthHandler;\nexport default GoogleOAuthHandler;\nvar _c;\n$RefreshReg$(_c, \"GoogleOAuthHandler\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "GoogleOAuthHandler", "_s", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "error", "handleOAuthCallback", "console", "close", "response", "fetch", "data", "json", "success", "access_token", "localStorage", "setItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Projects/ai-hr-agent/frontend/src/components/GoogleOAuthHandler.tsx"], "sourcesContent": ["// GoogleOAuthHandler.tsx - Handle Google OAuth callback in popup\r\nimport React, { useEffect } from 'react';\r\n\r\nconst GoogleOAuthHandler: React.FC = () => {\r\n  useEffect(() => {\r\n    // Extract parameters from URL\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const code = urlParams.get('code');\r\n    const state = urlParams.get('state');\r\n    const error = urlParams.get('error');\r\n\r\n    const handleOAuthCallback = async () => {\r\n      if (error) {\r\n        console.error('OAuth error:', error);\r\n        window.close();\r\n        return;\r\n      }\r\n\r\n      if (code && state) {\r\n        try {\r\n          // Exchange code for token\r\n          const response = await fetch(`http://localhost:8000/auth/google/callback?code=${code}&state=${state}`);\r\n          const data = await response.json();\r\n\r\n          if (data.success && data.access_token) {\r\n            // Store token in localStorage for parent window to access\r\n            localStorage.setItem('google_access_token', data.access_token);\r\n            \r\n            // Close popup - parent window will detect this\r\n            window.close();\r\n          } else {\r\n            console.error('Token exchange failed:', data);\r\n            window.close();\r\n          }\r\n        } catch (error) {\r\n          console.error('Error exchanging code for token:', error);\r\n          window.close();\r\n        }\r\n      } else {\r\n        console.error('Missing code or state parameter');\r\n        window.close();\r\n      }\r\n    };\r\n\r\n    handleOAuthCallback();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-900 text-white\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4\"></div>\r\n        <p>Completing Google Drive connection...</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GoogleOAuthHandler;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzCJ,SAAS,CAAC,MAAM;IACd;IACA,MAAMK,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IACpC,MAAME,KAAK,GAAGR,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpC,MAAMG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAID,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpCN,MAAM,CAACS,KAAK,CAAC,CAAC;QACd;MACF;MAEA,IAAIN,IAAI,IAAIE,KAAK,EAAE;QACjB,IAAI;UACF;UACA,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,mDAAmDR,IAAI,UAAUE,KAAK,EAAE,CAAC;UACtG,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;UAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACG,YAAY,EAAE;YACrC;YACAC,YAAY,CAACC,OAAO,CAAC,qBAAqB,EAAEL,IAAI,CAACG,YAAY,CAAC;;YAE9D;YACAf,MAAM,CAACS,KAAK,CAAC,CAAC;UAChB,CAAC,MAAM;YACLD,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEM,IAAI,CAAC;YAC7CZ,MAAM,CAACS,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdE,OAAO,CAACF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxDN,MAAM,CAACS,KAAK,CAAC,CAAC;QAChB;MACF,CAAC,MAAM;QACLD,OAAO,CAACF,KAAK,CAAC,iCAAiC,CAAC;QAChDN,MAAM,CAACS,KAAK,CAAC,CAAC;MAChB;IACF,CAAC;IAEDF,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA;IAAKuB,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFxB,OAAA;MAAKuB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxB,OAAA;QAAKuB,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnG5B,OAAA;QAAAwB,QAAA,EAAG;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CApDID,kBAA4B;AAAA4B,EAAA,GAA5B5B,kBAA4B;AAsDlC,eAAeA,kBAAkB;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}