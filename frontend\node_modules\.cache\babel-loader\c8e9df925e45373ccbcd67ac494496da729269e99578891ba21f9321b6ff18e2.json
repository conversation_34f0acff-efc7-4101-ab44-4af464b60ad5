{"ast": null, "code": "import { createMotionComponent } from '../../motion/index.mjs';\n\n/**\n * Convert any React component into a `motion` component. The provided component\n * **must** use `React.forwardRef` to the underlying DOM component you want to animate.\n *\n * ```jsx\n * const Component = React.forwardRef((props, ref) => {\n *   return <div ref={ref} />\n * })\n *\n * const MotionComponent = motion(Component)\n * ```\n *\n * @public\n */\nfunction createMotionProxy(createConfig) {\n  function custom(Component, customMotionComponentConfig = {}) {\n    return createMotionComponent(createConfig(Component, customMotionComponentConfig));\n  }\n  if (typeof Proxy === \"undefined\") {\n    return custom;\n  }\n  /**\n   * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n   * Rather than generating them anew every render.\n   */\n  const componentCache = new Map();\n  return new Proxy(custom, {\n    /**\n     * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n     * The prop name is passed through as `key` and we can use that to generate a `motion`\n     * DOM component with that name.\n     */\n    get: (_target, key) => {\n      /**\n       * If this element doesn't exist in the component cache, create it and cache.\n       */\n      if (!componentCache.has(key)) {\n        componentCache.set(key, custom(key));\n      }\n      return componentCache.get(key);\n    }\n  });\n}\nexport { createMotionProxy };", "map": {"version": 3, "names": ["createMotionComponent", "createMotionProxy", "createConfig", "custom", "Component", "customMotionComponentConfig", "Proxy", "componentCache", "Map", "get", "_target", "key", "has", "set"], "sources": ["D:/Projects/ai-hr-agent/frontend/node_modules/framer-motion/dist/es/render/dom/motion-proxy.mjs"], "sourcesContent": ["import { createMotionComponent } from '../../motion/index.mjs';\n\n/**\n * Convert any React component into a `motion` component. The provided component\n * **must** use `React.forwardRef` to the underlying DOM component you want to animate.\n *\n * ```jsx\n * const Component = React.forwardRef((props, ref) => {\n *   return <div ref={ref} />\n * })\n *\n * const MotionComponent = motion(Component)\n * ```\n *\n * @public\n */\nfunction createMotionProxy(createConfig) {\n    function custom(Component, customMotionComponentConfig = {}) {\n        return createMotionComponent(createConfig(Component, customMotionComponentConfig));\n    }\n    if (typeof Proxy === \"undefined\") {\n        return custom;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    return new Proxy(custom, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, custom(key));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\nexport { createMotionProxy };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,wBAAwB;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EACrC,SAASC,MAAMA,CAACC,SAAS,EAAEC,2BAA2B,GAAG,CAAC,CAAC,EAAE;IACzD,OAAOL,qBAAqB,CAACE,YAAY,CAACE,SAAS,EAAEC,2BAA2B,CAAC,CAAC;EACtF;EACA,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;IAC9B,OAAOH,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACI,MAAMI,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC,OAAO,IAAIF,KAAK,CAACH,MAAM,EAAE;IACrB;AACR;AACA;AACA;AACA;IACQM,GAAG,EAAEA,CAACC,OAAO,EAAEC,GAAG,KAAK;MACnB;AACZ;AACA;MACY,IAAI,CAACJ,cAAc,CAACK,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BJ,cAAc,CAACM,GAAG,CAACF,GAAG,EAAER,MAAM,CAACQ,GAAG,CAAC,CAAC;MACxC;MACA,OAAOJ,cAAc,CAACE,GAAG,CAACE,GAAG,CAAC;IAClC;EACJ,CAAC,CAAC;AACN;AAEA,SAASV,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}