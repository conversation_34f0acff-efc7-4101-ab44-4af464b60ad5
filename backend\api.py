# api_enhanced.py - Enhanced API with Google Drive and Excel support
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
import os
import tempfile
import logging
from datetime import datetime
import json
import uuid
import pandas as pd
import io
from urllib.parse import urlparse
import requests

# Google Drive imports
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import pickle

# Import your existing modules
from agents.orchestrator_agent import run_orchestrated_workflow_optimized
from state_management import JobRequirements, create_initial_state
from dotenv import load_dotenv
load_dotenv(override=True)

app = FastAPI(title="TalentSphere AI", version="3.1")

# Enable CORS for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories
os.makedirs("temp_uploads", exist_ok=True)
os.makedirs("output_reports", exist_ok=True)
os.makedirs("static", exist_ok=True)
os.makedirs("google_credentials", exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Google Drive configuration
# Include all necessary scopes to avoid scope mismatch errors
GOOGLE_DRIVE_SCOPES = [
    'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/drive.metadata.readonly',
    'https://www.googleapis.com/auth/drive.file',
    'https://www.googleapis.com/auth/documents.readonly'
]
GOOGLE_CLIENT_SECRETS_FILE = "google_credentials/client_secrets.json"
GOOGLE_REDIRECT_URI = "http://localhost:8000/auth/google/callback"

# Pydantic models
class JobRequirementsModel(BaseModel):
    position_title: str
    job_description: str
    required_skills: List[str] = []
    preferred_skills: List[str] = []
    experience_level: str = ""
    education_requirements: str = ""
    location: str = ""
    employment_type: str = ""
    company_name: str = ""
    keywords: List[str] = []

class GoogleDriveRequest(BaseModel):
    folder_id: str
    access_token: str

class ExcelUploadResponse(BaseModel):
    total_rows: int
    valid_rows: int
    invalid_rows: List[Dict[str, Any]]
    download_urls: List[str]

class AnalysisResponse(BaseModel):
    success: bool
    message: str
    job_id: str
    processing_time: Optional[float] = None
    statistics: Optional[dict] = None
    final_report: Optional[str] = None
    candidate_reports: Optional[dict] = None
    report_files: Optional[dict] = None
    warnings: Optional[List[str]] = None
    errors: Optional[List[str]] = None

# In-memory storage for job results and sessions
job_results = {}
google_auth_sessions = {}

class GoogleDriveService:
    """Service for Google Drive operations"""
    
    @staticmethod
    def get_authorization_url(state: str) -> str:
        """Get Google OAuth authorization URL"""
        try:
            if not os.path.exists(GOOGLE_CLIENT_SECRETS_FILE):
                raise HTTPException(
                    status_code=500,
                    detail="Google Drive integration not configured. Please add client_secrets.json"
                )

            flow = Flow.from_client_secrets_file(
                GOOGLE_CLIENT_SECRETS_FILE,
                scopes=GOOGLE_DRIVE_SCOPES,
                redirect_uri=GOOGLE_REDIRECT_URI
            )
            # Fix: Properly set state parameter
            authorization_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                state=state
            )
            return authorization_url
        except Exception as e:
            logger.error(f"Error getting authorization URL: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def exchange_code_for_token(code: str, state: str) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        try:
            # Create flow without state parameter to avoid scope validation issues
            flow = Flow.from_client_secrets_file(
                GOOGLE_CLIENT_SECRETS_FILE,
                scopes=GOOGLE_DRIVE_SCOPES,
                redirect_uri=GOOGLE_REDIRECT_URI
            )

            # Fetch token with the authorization code
            flow.fetch_token(code=code)

            credentials = flow.credentials

            # Calculate actual expiry time
            expires_in = 3600  # Default to 1 hour
            if hasattr(credentials, 'expiry') and credentials.expiry:
                from datetime import datetime, timezone
                now = datetime.now(timezone.utc)
                expires_in = int((credentials.expiry - now).total_seconds())

            return {
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "expires_in": expires_in
            }
        except Exception as e:
            logger.error(f"Error exchanging code for token: {e}")
            raise HTTPException(status_code=400, detail="Invalid authorization code")
    
    @staticmethod
    async def list_folder_files(folder_id: str, access_token: str) -> List[Dict[str, Any]]:
        """List PDF files in Google Drive folder"""
        try:
            credentials = Credentials(token=access_token)
            service = build('drive', 'v3', credentials=credentials)
            
            # Query for PDF files in the specified folder
            query = f"'{folder_id}' in parents and mimeType='application/pdf' and trashed=false"
            
            results = service.files().list(
                q=query,
                pageSize=100,
                fields="files(id, name, size, createdTime, modifiedTime)"
            ).execute()
            
            files = results.get('files', [])
            
            return [{
                'id': file['id'],
                'name': file['name'],
                'size': int(file.get('size', 0)),
                'created_time': file.get('createdTime'),
                'modified_time': file.get('modifiedTime')
            } for file in files]
            
        except HttpError as e:
            logger.error(f"Google Drive API error: {e}")
            if e.resp.status == 404:
                raise HTTPException(status_code=404, detail="Folder not found or access denied")
            elif e.resp.status == 403:
                raise HTTPException(status_code=403, detail="Access denied to Google Drive folder")
            else:
                raise HTTPException(status_code=500, detail="Google Drive API error")
        except Exception as e:
            logger.error(f"Error listing folder files: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def download_file(file_id: str, access_token: str) -> bytes:
        """Download file content from Google Drive"""
        try:
            credentials = Credentials(token=access_token)
            service = build('drive', 'v3', credentials=credentials)
            
            # Download file content
            file_content = service.files().get_media(fileId=file_id).execute()
            return file_content
            
        except HttpError as e:
            logger.error(f"Error downloading file {file_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to download file: {e}")
        except Exception as e:
            logger.error(f"Error downloading file {file_id}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

class ExcelProcessingService:
    """Service for processing Excel files with resume links"""
    
    @staticmethod
    async def process_excel_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process Excel file and extract resume links"""
        try:
            # Read Excel file
            df = pd.read_excel(io.BytesIO(file_content))
            
            # Validate required columns
            required_columns = ['name', 'resume_link']
            df.columns = [col.lower().strip().replace(' ', '_') for col in df.columns]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Missing required columns: {missing_columns}. Required: name, resume_link"
                )
            
            # Process rows
            valid_rows = []
            invalid_rows = []
            download_urls = []
            
            for index, row in df.iterrows():
                name = str(row.get('name', '')).strip()
                resume_link = str(row.get('resume_link', '')).strip()
                
                if not name or not resume_link:
                    invalid_rows.append({
                        'row': index + 1,
                        'name': name,
                        'resume_link': resume_link,
                        'error': 'Missing name or resume link'
                    })
                    continue
                
                # Validate and normalize Google Drive URL
                normalized_url = ExcelProcessingService._normalize_google_drive_url(resume_link)
                if not normalized_url:
                    invalid_rows.append({
                        'row': index + 1,
                        'name': name,
                        'resume_link': resume_link,
                        'error': 'Invalid Google Drive URL format'
                    })
                    continue
                
                valid_rows.append({
                    'name': name,
                    'resume_link': normalized_url,
                    'row': index + 1
                })
                download_urls.append(normalized_url)
            
            return {
                'total_rows': len(df),
                'valid_rows': len(valid_rows),
                'invalid_rows': invalid_rows,
                'download_urls': download_urls,
                'valid_data': valid_rows
            }
            
        except pd.errors.EmptyDataError:
            raise HTTPException(status_code=400, detail="Excel file is empty")
        except Exception as e:
            logger.error(f"Error processing Excel file: {e}")
            raise HTTPException(status_code=400, detail=f"Failed to process Excel file: {str(e)}")
    
    @staticmethod
    def _normalize_google_drive_url(url: str) -> Optional[str]:
        """Normalize Google Drive URL to direct download format"""
        try:
            # Handle different Google Drive URL formats
            if 'drive.google.com' not in url:
                return None
            
            # Extract file ID from various URL formats
            file_id = None
            
            # Format 1: https://drive.google.com/file/d/FILE_ID/view
            if '/file/d/' in url:
                file_id = url.split('/file/d/')[1].split('/')[0]
            
            # Format 2: https://docs.google.com/document/d/FILE_ID/
            elif '/document/d/' in url:
                file_id = url.split('/document/d/')[1].split('/')[0]
            
            # Format 3: https://drive.google.com/open?id=FILE_ID
            elif 'open?id=' in url:
                file_id = url.split('open?id=')[1].split('&')[0]
            
            # Format 4: Direct export URL
            elif '/export?' in url and 'id=' in url:
                file_id = url.split('id=')[1].split('&')[0]
            
            if not file_id:
                return None
            
            # Return direct download URL
            return f"https://drive.google.com/uc?export=download&id={file_id}"
            
        except Exception as e:
            logger.error(f"Error normalizing Google Drive URL: {e}")
            return None
    
    @staticmethod
    async def download_resume_from_url(url: str, filename: str) -> str:
        """Download resume from Google Drive URL"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Save to temporary file
            temp_file_path = os.path.join("temp_uploads", f"{uuid.uuid4()}_{filename}.pdf")
            
            with open(temp_file_path, 'wb') as f:
                f.write(response.content)
            
            return temp_file_path
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading resume from {url}: {e}")
            raise HTTPException(status_code=400, detail=f"Failed to download resume: {str(e)}")

# Google Drive authentication endpoints
@app.get("/auth/google/url")
async def get_google_auth_url():
    """Get Google OAuth authorization URL"""
    try:
        state = str(uuid.uuid4())
        auth_url = GoogleDriveService.get_authorization_url(state)

        # Store state for validation
        google_auth_sessions[state] = {
            'created_at': datetime.now(),
            'used': False
        }

        logger.info(f"Generated OAuth URL with state: {state}")
        return {"auth_url": auth_url, "state": state}
    except Exception as e:
        logger.error(f"Error generating Google auth URL: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/auth/google/debug")
async def debug_google_auth():
    """Debug endpoint to check OAuth session state"""
    return {
        "active_sessions": len(google_auth_sessions),
        "sessions": {
            state: {
                "created_at": data["created_at"].isoformat(),
                "used": data["used"],
                "age_seconds": (datetime.now() - data["created_at"]).total_seconds()
            }
            for state, data in google_auth_sessions.items()
        }
    }

@app.get("/auth/google/callback")
async def google_auth_callback(code: str, state: str):
    """Handle Google OAuth callback"""
    try:
        logger.info(f"OAuth callback received - Code: {code[:20]}..., State: {state}")
        logger.info(f"Available states: {list(google_auth_sessions.keys())}")

        # Clean up expired states (older than 10 minutes)
        current_time = datetime.now()
        expired_states = [
            s for s, data in google_auth_sessions.items()
            if (current_time - data['created_at']).total_seconds() > 600
        ]
        for expired_state in expired_states:
            del google_auth_sessions[expired_state]
            logger.info(f"Cleaned up expired state: {expired_state}")

        # Validate state
        if state not in google_auth_sessions:
            logger.error(f"State not found: {state}")
            raise HTTPException(status_code=400, detail="Invalid state parameter")

        if google_auth_sessions[state]['used']:
            logger.error(f"State already used: {state}")
            raise HTTPException(status_code=400, detail="State already used")

        # Mark state as used
        google_auth_sessions[state]['used'] = True

        # Exchange code for token
        token_data = await GoogleDriveService.exchange_code_for_token(code, state)

        logger.info("OAuth token exchange successful")

        # Return HTML page that communicates with parent window
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Google Drive Connected</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }}
                .container {{
                    text-align: center;
                    padding: 2rem;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    backdrop-filter: blur(10px);
                }}
                .success {{
                    font-size: 1.5rem;
                    margin-bottom: 1rem;
                }}
                .spinner {{
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-top: 3px solid white;
                    border-radius: 50%;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                }}
                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="spinner"></div>
                <div class="success">✅ Google Drive Connected!</div>
                <p>Closing window...</p>
            </div>
            <script>
                // Store token in localStorage
                localStorage.setItem('google_access_token', '{token_data["access_token"]}');

                // Try to communicate with parent window
                try {{
                    if (window.opener) {{
                        window.opener.postMessage({{
                            type: 'GOOGLE_AUTH_SUCCESS',
                            access_token: '{token_data["access_token"]}',
                            expires_in: {token_data["expires_in"]}
                        }}, '*');
                    }}
                }} catch (e) {{
                    console.log('Could not communicate with parent window:', e);
                }}

                // Close window after a short delay
                setTimeout(() => {{
                    window.close();
                }}, 2000);
            </script>
        </body>
        </html>
        """

        from fastapi.responses import HTMLResponse
        return HTMLResponse(content=html_content)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in Google auth callback: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# Google Drive folder listing
@app.post("/google-drive/folders/{folder_id}/files")
async def list_google_drive_files(folder_id: str, access_token: str = Form(...)):
    """List PDF files in Google Drive folder"""
    try:
        files = await GoogleDriveService.list_folder_files(folder_id, access_token)
        return {
            "success": True,
            "folder_id": folder_id,
            "files": files,
            "total_files": len(files)
        }
    except Exception as e:
        logger.error(f"Error listing Google Drive files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Excel processing endpoint
@app.post("/process-excel")
async def process_excel_upload(file: UploadFile = File(...)):
    """Process Excel file with resume links"""
    try:
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="Only Excel files (.xlsx, .xls) are supported")
        
        # Read file content
        file_content = await file.read()
        
        # Process Excel file
        result = await ExcelProcessingService.process_excel_file(file_content, file.filename)
        
        return {
            "success": True,
            "message": f"Processed Excel file: {result['valid_rows']} valid rows, {len(result['invalid_rows'])} invalid rows",
            "data": {
                "total_rows": result['total_rows'],
                "valid_rows": result['valid_rows'],
                "invalid_rows": result['invalid_rows'],
                "download_urls": result['download_urls']
            }
        }
        
    except Exception as e:
        logger.error(f"Error processing Excel file: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Enhanced analysis endpoint with multiple upload methods
@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_candidates(
    position_title: str = Form(...),
    job_description: str = Form(...),
    upload_method: str = Form("files"),  # "files", "google_drive", "excel"
    required_skills: str = Form(""),
    preferred_skills: str = Form(""),
    experience_level: str = Form(""),
    education_requirements: str = Form(""),
    location: str = Form(""),
    employment_type: str = Form(""),
    company_name: str = Form(""),
    keywords: str = Form(""),
    files: List[UploadFile] = File(default=[]),
    # Google Drive parameters
    google_folder_id: str = Form(""),
    google_access_token: str = Form(""),
    # Excel parameters
    excel_file: UploadFile = File(default=None),
):
    """Enhanced analysis endpoint supporting multiple upload methods"""
    
    job_id = str(uuid.uuid4())
    start_time = datetime.now()
    
    try:
        logger.info(f"🚀 Starting analysis job {job_id} with method: {upload_method}")
        
        # Validate inputs
        if not position_title or not job_description:
            raise HTTPException(status_code=400, detail="Position title and job description are required")
        
        # Create job requirements
        job_requirements = JobRequirements(
            position_title=position_title.strip(),
            job_description=job_description.strip(),
            required_skills=[s.strip() for s in required_skills.split(',') if s.strip()],
            preferred_skills=[s.strip() for s in preferred_skills.split(',') if s.strip()],
            experience_level=experience_level.strip(),
            education_requirements=education_requirements.strip(),
            location=location.strip(),
            employment_type=employment_type.strip(),
            company_name=company_name.strip(),
            keywords=[k.strip() for k in keywords.split(',') if k.strip()]
        )
        
        # Process files based on upload method
        uploaded_files = []
        
        if upload_method == "files":
            # Traditional file upload
            if not files or len(files) == 0:
                raise HTTPException(status_code=400, detail="No files uploaded for file upload method")
            
            uploaded_files = await process_traditional_files(files)
            
        elif upload_method == "google_drive":
            # Google Drive folder upload
            if not google_folder_id or not google_access_token:
                raise HTTPException(status_code=400, detail="Google Drive folder ID and access token are required")
            
            uploaded_files = await process_google_drive_files(google_folder_id, google_access_token)
            
        elif upload_method == "excel":
            # Excel file with resume links
            if not excel_file:
                raise HTTPException(status_code=400, detail="Excel file is required for Excel upload method")
            
            uploaded_files = await process_excel_resume_links(excel_file)
            
        else:
            raise HTTPException(status_code=400, detail="Invalid upload method. Use 'files', 'google_drive', or 'excel'")
        
        if not uploaded_files:
            raise HTTPException(status_code=400, detail="No valid files were processed")
        
        # Get API keys
        openai_api_key = os.getenv('OPENAI_API_KEY')
        serp_api_key = os.getenv('SERP_API_KEY')
        tavily_api_key = os.getenv('TAVILY_API_KEY')
        github_token = os.getenv('GITHUB_TOKEN')
        
        if not openai_api_key:
            raise HTTPException(status_code=500, detail="OpenAI API key not configured")
        
        logger.info(f"📊 Processing {len(uploaded_files)} files for job {job_id}")
        
        # Run the optimized workflow
        final_state = await run_orchestrated_workflow_optimized(
            job_requirements=job_requirements,
            uploaded_files=uploaded_files,
            openai_api_key=openai_api_key,
            serp_api_key=serp_api_key,
            tavily_api_key=tavily_api_key,
            github_token=github_token
        )
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Check for errors
        if final_state.get("errors"):
            logger.error(f"Workflow errors for job {job_id}: {final_state['errors']}")
            return AnalysisResponse(
                success=False,
                message="Analysis completed with errors",
                job_id=job_id,
                processing_time=processing_time,
                errors=final_state["errors"],
                warnings=final_state.get("warnings", [])
            )
        
        # Save reports to files
        report_files = await save_reports_to_files(final_state, job_requirements, job_id)
        
        # Store results for later retrieval
        job_results[job_id] = {
            "final_state": final_state,
            "report_files": report_files,
            "job_requirements": job_requirements,
            "upload_method": upload_method,
            "timestamp": datetime.now().isoformat()
        }
        
        # Prepare response
        response_data = AnalysisResponse(
            success=True,
            message=f"Analysis completed successfully using {upload_method} method",
            job_id=job_id,
            processing_time=processing_time,
            statistics={
                'total_files_processed': len(uploaded_files),
                'candidates_parsed': len(final_state.get("parsed_candidates", [])),
                'candidates_matched': len(final_state.get("matched_candidates", [])),
                'candidates_researched': len(final_state.get("researched_candidates", [])),
                'candidates_validated': len(final_state.get("validated_candidates", [])),
                'reports_generated': len(final_state.get("candidate_reports", {})),
                'upload_method': upload_method
            },
            final_report=final_state.get("final_report", ""),
            candidate_reports=final_state.get("candidate_reports", {}),
            report_files=report_files,
            warnings=final_state.get("warnings", [])
        )
        
        # Cleanup temporary files
        cleanup_temp_files_async(uploaded_files)
        
        logger.info(f"✅ Analysis job {job_id} completed in {processing_time:.2f}s using {upload_method}")
        return response_data
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"❌ Analysis job {job_id} failed after {processing_time:.2f}s: {e}")
        
        # Cleanup on error
        if 'uploaded_files' in locals():
            cleanup_temp_files_async(uploaded_files)
        
        raise HTTPException(status_code=500, detail=str(e))

async def process_traditional_files(files: List[UploadFile]) -> List[str]:
    """Process traditional file uploads"""
    uploaded_files = []
    
    for file in files:
        if file.filename:
            # Create unique filename
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join("temp_uploads", unique_filename)
            
            # Save file
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            uploaded_files.append(file_path)
    
    return uploaded_files

async def process_google_drive_files(folder_id: str, access_token: str) -> List[str]:
    """Process Google Drive folder files"""
    try:
        # List files in folder
        files = await GoogleDriveService.list_folder_files(folder_id, access_token)
        
        if not files:
            raise HTTPException(status_code=400, detail="No PDF files found in the specified Google Drive folder")
        
        uploaded_files = []
        
        # Download each file
        for file_info in files:
            try:
                file_content = await GoogleDriveService.download_file(file_info['id'], access_token)
                
                # Save to temporary file
                file_path = os.path.join("temp_uploads", f"{uuid.uuid4()}_{file_info['name']}")
                
                with open(file_path, 'wb') as f:
                    f.write(file_content)
                
                uploaded_files.append(file_path)
                logger.info(f"Downloaded: {file_info['name']}")
                
            except Exception as e:
                logger.error(f"Failed to download {file_info['name']}: {e}")
                continue
        
        if not uploaded_files:
            raise HTTPException(status_code=400, detail="Failed to download any files from Google Drive folder")
        
        return uploaded_files
        
    except Exception as e:
        logger.error(f"Error processing Google Drive files: {e}")
        raise e

async def process_excel_resume_links(excel_file: UploadFile) -> List[str]:
    """Process Excel file with resume links"""
    try:
        # Read Excel file content
        file_content = await excel_file.read()
        
        # Process Excel file
        result = await ExcelProcessingService.process_excel_file(file_content, excel_file.filename)
        
        if result['valid_rows'] == 0:
            raise HTTPException(status_code=400, detail="No valid resume links found in Excel file")
        
        uploaded_files = []
        
        # Download each resume
        for row_data in result['valid_data']:
            try:
                file_path = await ExcelProcessingService.download_resume_from_url(
                    row_data['resume_link'],
                    f"{row_data['name']}_resume"
                )
                uploaded_files.append(file_path)
                logger.info(f"Downloaded resume for: {row_data['name']}")
                
            except Exception as e:
                logger.error(f"Failed to download resume for {row_data['name']}: {e}")
                continue
        
        if not uploaded_files:
            raise HTTPException(status_code=400, detail="Failed to download any resumes from Excel links")
        
        return uploaded_files
        
    except Exception as e:
        logger.error(f"Error processing Excel resume links: {e}")
        raise e

# Keep all existing endpoints unchanged
@app.get("/")
async def root():
    return {"message": "ResumeGPT Pro API", "version": "3.1", "status": "running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": "3.1-enhanced-multi-upload",
        "features": {
            "resume_analysis": True,
            "candidate_matching": True,
            "deep_research": True,
            "identity_validation": True,
            "comprehensive_reporting": True,
            "parallel_processing": True,
            "google_drive_integration": True,
            "excel_upload_support": True,
            "traditional_file_upload": True
        }
    }

@app.get("/api-status")
async def api_status():
    """Check API configuration status"""
    try:
        status = {
            'openai_configured': bool(os.getenv('OPENAI_API_KEY')),
            'serp_api_configured': bool(os.getenv('SERP_API_KEY')),
            'tavily_api_configured': bool(os.getenv('TAVILY_API_KEY')),
            'github_token_configured': bool(os.getenv('GITHUB_TOKEN')),
            'google_drive_configured': os.path.exists(GOOGLE_CLIENT_SECRETS_FILE),
            'upload_methods': ['files', 'google_drive', 'excel']
        }
        return status
    except Exception as e:
        logger.error(f"API status check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/results/{job_id}")
async def get_results(job_id: str):
    """Get results for a specific job"""
    if job_id not in job_results:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return job_results[job_id]

@app.get("/download/{job_id}/{report_type}")
async def download_report(job_id: str, report_type: str):
    """Download a specific report"""
    if job_id not in job_results:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = job_results[job_id]
    report_files = job_data.get("report_files", {})
    
    if report_type not in report_files:
        raise HTTPException(status_code=404, detail="Report not found")
    
    filename = report_files[report_type]
    file_path = os.path.join("output_reports", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Report file not found")
    
    return FileResponse(
        file_path,
        media_type='application/octet-stream',
        filename=filename
    )

async def save_reports_to_files(final_state, job_requirements, job_id):
    """Save reports to files and return file mapping"""
    
    report_files = {}
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        # Executive summary
        if final_state.get("final_report"):
            filename = f"executive_summary_{job_id}_{timestamp}.txt"
            file_path = os.path.join("output_reports", filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(final_state["final_report"])
            report_files["executive_summary"] = filename
        
        # Individual candidate reports
        candidate_reports = final_state.get("candidate_reports", {})
        individual_files = []
        
        for candidate_id, report in candidate_reports.items():
            safe_name = "".join(c for c in candidate_id if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"candidate_{safe_name}_{job_id}_{timestamp}.txt"
            file_path = os.path.join("output_reports", filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report)
            individual_files.append(filename)
        
        if individual_files:
            report_files["individual_reports"] = individual_files
        
        # JSON export
        from agents.summarization_agent import ReportExporter
        json_data = ReportExporter.export_to_json(final_state)
        filename = f"complete_analysis_{job_id}_{timestamp}.json"
        file_path = os.path.join("output_reports", filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(json_data)
        report_files["json_export"] = filename
        
        # CSV summary
        consolidated_data = {}
        candidate_profiles = {f"{c.full_name}_{c.cv_filename}": c for c in final_state.get("parsed_candidates", [])}
        matching_results = {r.candidate_id: r for r in final_state.get("matched_candidates", [])}
        research_results = {r.candidate_id: r for r in final_state.get("researched_candidates", [])}
        validation_results = {r.candidate_id: r for r in final_state.get("validated_candidates", [])}
        
        for candidate_id in matching_results.keys():
            consolidated_data[candidate_id] = {
                "candidate": candidate_profiles.get(candidate_id),
                "matching": matching_results.get(candidate_id),
                "research": research_results.get(candidate_id),
                "validation": validation_results.get(candidate_id)
            }
        
        csv_data = ReportExporter.export_to_csv_summary(consolidated_data)
        filename = f"candidate_summary_{job_id}_{timestamp}.csv"
        file_path = os.path.join("output_reports", filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(csv_data)
        report_files["csv_summary"] = filename
        
        return report_files
        
    except Exception as e:
        logger.error(f"Error saving reports for job {job_id}: {e}")
        return report_files

def cleanup_temp_files_async(file_paths):
    """Asynchronously clean up temporary files"""
    import threading
    
    def cleanup():
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                logger.warning(f"Failed to cleanup {file_path}: {e}")
    
    # Run cleanup in background thread
    threading.Thread(target=cleanup, daemon=True).start()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)