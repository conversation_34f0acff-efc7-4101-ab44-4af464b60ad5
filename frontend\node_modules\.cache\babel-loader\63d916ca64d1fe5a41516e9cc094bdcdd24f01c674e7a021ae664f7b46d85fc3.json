{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport stripAnsi from \"./utils/stripAnsi.js\";\nimport parseURL from \"./utils/parseURL.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, createOverlay } from \"./overlay.js\";\nimport { log, logEnabledFeatures, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport reloadApp from \"./utils/reloadApp.js\";\nimport createSocketURL from \"./utils/createSocketURL.js\";\n\n/**\n * @typedef {Object} OverlayOptions\n * @property {boolean | (error: Error) => boolean} [warnings]\n * @property {boolean | (error: Error) => boolean} [errors]\n * @property {boolean | (error: Error) => boolean} [runtimeErrors]\n * @property {string} [trustedTypesPolicyName]\n */\n\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | OverlayOptions} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @param {boolean | { warnings?: boolean | string; errors?: boolean | string; runtimeErrors?: boolean | string; }} overlayOptions\n */\nvar decodeOverlayOptions = function decodeOverlayOptions(overlayOptions) {\n  if (typeof overlayOptions === \"object\") {\n    [\"warnings\", \"errors\", \"runtimeErrors\"].forEach(function (property) {\n      if (typeof overlayOptions[property] === \"string\") {\n        var overlayFilterFunctionString = decodeURIComponent(overlayOptions[property]);\n\n        // eslint-disable-next-line no-new-func\n        var overlayFilterFunction = new Function(\"message\", \"var callback = \".concat(overlayFilterFunctionString, \"\\n        return callback(message)\"));\n        overlayOptions[property] = overlayFilterFunction;\n      }\n    });\n  }\n};\n\n/**\n * @type {Status}\n */\nvar status = {\n  isUnloading: false,\n  // TODO Workaround for webpack v4, `__webpack_hash__` is not replaced without HotModuleReplacement\n  // eslint-disable-next-line camelcase\n  currentHash: typeof __webpack_hash__ !== \"undefined\" ? __webpack_hash__ : \"\"\n};\n\n/** @type {Options} */\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\nvar enabledFeatures = {\n  \"Hot Module Replacement\": false,\n  \"Live Reloading\": false,\n  Progress: false,\n  Overlay: false\n};\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  enabledFeatures[\"Hot Module Replacement\"] = true;\n}\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  enabledFeatures[\"Live Reloading\"] = true;\n}\nif (parsedResourceQuery.progress === \"true\") {\n  options.progress = true;\n  enabledFeatures.Progress = true;\n}\nif (parsedResourceQuery.overlay) {\n  try {\n    options.overlay = JSON.parse(parsedResourceQuery.overlay);\n  } catch (e) {\n    log.error(\"Error parsing overlay options from resource query:\", e);\n  }\n\n  // Fill in default \"true\" params for partially-specified objects.\n  if (typeof options.overlay === \"object\") {\n    options.overlay = _objectSpread({\n      errors: true,\n      warnings: true,\n      runtimeErrors: true\n    }, options.overlay);\n    decodeOverlayOptions(options.overlay);\n  }\n  enabledFeatures.Overlay = true;\n}\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n\n/**\n * @param {string} level\n */\nfunction setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n}\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\nlogEnabledFeatures(enabledFeatures);\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar overlay = typeof window !== \"undefined\" ? createOverlay(typeof options.overlay === \"object\" ? {\n  trustedTypesPolicyName: options.overlay.trustedTypesPolicyName,\n  catchRuntimeError: options.overlay.runtimeErrors\n} : {\n  trustedTypesPolicyName: false,\n  catchRuntimeError: options.overlay\n}) : {\n  send: function send() {}\n};\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n    options.hot = true;\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n    options.liveReload = true;\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\");\n\n    // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Invalid\");\n  },\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n    options.overlay = value;\n    decodeOverlayOptions(options.overlay);\n  },\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n    options.reconnect = value;\n  },\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    reloadApp(options, status);\n  },\n  // TODO: remove in v5 in favor of 'static-changed'\n  /**\n   * @param {string} file\n   */\n  \"content-changed\": function contentChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n        header = _formatProblem.header,\n        body = _formatProblem.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Warnings\", printableWarnings);\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n    var overlayWarningsSetting = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n    if (overlayWarningsSetting) {\n      var warningsToDisplay = typeof overlayWarningsSetting === \"function\" ? _warnings.filter(overlayWarningsSetting) : _warnings;\n      if (warningsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"warning\",\n          messages: _warnings\n        });\n      }\n    }\n    if (params && params.preventReloading) {\n      return;\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n        header = _formatProblem2.header,\n        body = _formatProblem2.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Errors\", printableErrors);\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n    var overlayErrorsSettings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n    if (overlayErrorsSettings) {\n      var errorsToDisplay = typeof overlayErrorsSettings === \"function\" ? _errors.filter(overlayErrorsSettings) : _errors;\n      if (errorsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"error\",\n          messages: _errors\n        });\n      }\n    }\n  },\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Close\");\n  }\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "webpackHotLog", "stripAnsi", "parseURL", "socket", "formatProblem", "createOverlay", "log", "logEnabledFeatures", "setLogLevel", "sendMessage", "reloadApp", "createSocketURL", "decodeOverlayOptions", "overlayOptions", "property", "overlayFilterFunctionString", "decodeURIComponent", "overlayFilterFunction", "Function", "concat", "status", "isUnloading", "currentHash", "__webpack_hash__", "options", "hot", "liveReload", "progress", "overlay", "parsedResourceQuery", "__resourceQuery", "enabledFeatures", "Progress", "Overlay", "JSON", "parse", "e", "error", "errors", "warnings", "runtimeErrors", "logging", "reconnect", "setAllLogLevel", "level", "self", "addEventListener", "window", "trustedTypesPolicyName", "catchRuntimeError", "send", "onSocketMessage", "invalid", "info", "type", "hash", "_hash", "previousHash", "document", "progressUpdate", "data", "pluginName", "percent", "msg", "stillOk", "ok", "contentChanged", "file", "location", "reload", "staticChanged", "_warnings", "params", "warn", "printableWarnings", "map", "_formatProblem", "header", "body", "overlayWarningsSetting", "warningsToDisplay", "messages", "preventReloading", "_errors", "printableErrors", "_formatProblem2", "overlayErrorsSettings", "errorsToDisplay", "_error", "close", "socketURL"], "sources": ["D:/Projects/ai-hr-agent/frontend/node_modules/webpack-dev-server/client/index.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport stripAnsi from \"./utils/stripAnsi.js\";\nimport parseURL from \"./utils/parseURL.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, createOverlay } from \"./overlay.js\";\nimport { log, logEnabledFeatures, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport reloadApp from \"./utils/reloadApp.js\";\nimport createSocketURL from \"./utils/createSocketURL.js\";\n\n/**\n * @typedef {Object} OverlayOptions\n * @property {boolean | (error: Error) => boolean} [warnings]\n * @property {boolean | (error: Error) => boolean} [errors]\n * @property {boolean | (error: Error) => boolean} [runtimeErrors]\n * @property {string} [trustedTypesPolicyName]\n */\n\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | OverlayOptions} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @param {boolean | { warnings?: boolean | string; errors?: boolean | string; runtimeErrors?: boolean | string; }} overlayOptions\n */\nvar decodeOverlayOptions = function decodeOverlayOptions(overlayOptions) {\n  if (typeof overlayOptions === \"object\") {\n    [\"warnings\", \"errors\", \"runtimeErrors\"].forEach(function (property) {\n      if (typeof overlayOptions[property] === \"string\") {\n        var overlayFilterFunctionString = decodeURIComponent(overlayOptions[property]);\n\n        // eslint-disable-next-line no-new-func\n        var overlayFilterFunction = new Function(\"message\", \"var callback = \".concat(overlayFilterFunctionString, \"\\n        return callback(message)\"));\n        overlayOptions[property] = overlayFilterFunction;\n      }\n    });\n  }\n};\n\n/**\n * @type {Status}\n */\nvar status = {\n  isUnloading: false,\n  // TODO Workaround for webpack v4, `__webpack_hash__` is not replaced without HotModuleReplacement\n  // eslint-disable-next-line camelcase\n  currentHash: typeof __webpack_hash__ !== \"undefined\" ? __webpack_hash__ : \"\"\n};\n\n/** @type {Options} */\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\nvar enabledFeatures = {\n  \"Hot Module Replacement\": false,\n  \"Live Reloading\": false,\n  Progress: false,\n  Overlay: false\n};\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  enabledFeatures[\"Hot Module Replacement\"] = true;\n}\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  enabledFeatures[\"Live Reloading\"] = true;\n}\nif (parsedResourceQuery.progress === \"true\") {\n  options.progress = true;\n  enabledFeatures.Progress = true;\n}\nif (parsedResourceQuery.overlay) {\n  try {\n    options.overlay = JSON.parse(parsedResourceQuery.overlay);\n  } catch (e) {\n    log.error(\"Error parsing overlay options from resource query:\", e);\n  }\n\n  // Fill in default \"true\" params for partially-specified objects.\n  if (typeof options.overlay === \"object\") {\n    options.overlay = _objectSpread({\n      errors: true,\n      warnings: true,\n      runtimeErrors: true\n    }, options.overlay);\n    decodeOverlayOptions(options.overlay);\n  }\n  enabledFeatures.Overlay = true;\n}\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n\n/**\n * @param {string} level\n */\nfunction setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n}\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\nlogEnabledFeatures(enabledFeatures);\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar overlay = typeof window !== \"undefined\" ? createOverlay(typeof options.overlay === \"object\" ? {\n  trustedTypesPolicyName: options.overlay.trustedTypesPolicyName,\n  catchRuntimeError: options.overlay.runtimeErrors\n} : {\n  trustedTypesPolicyName: false,\n  catchRuntimeError: options.overlay\n}) : {\n  send: function send() {}\n};\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n    options.hot = true;\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n    options.liveReload = true;\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\");\n\n    // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Invalid\");\n  },\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n    options.overlay = value;\n    decodeOverlayOptions(options.overlay);\n  },\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n    options.reconnect = value;\n  },\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    reloadApp(options, status);\n  },\n  // TODO: remove in v5 in favor of 'static-changed'\n  /**\n   * @param {string} file\n   */\n  \"content-changed\": function contentChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n        header = _formatProblem.header,\n        body = _formatProblem.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Warnings\", printableWarnings);\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n    var overlayWarningsSetting = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n    if (overlayWarningsSetting) {\n      var warningsToDisplay = typeof overlayWarningsSetting === \"function\" ? _warnings.filter(overlayWarningsSetting) : _warnings;\n      if (warningsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"warning\",\n          messages: _warnings\n        });\n      }\n    }\n    if (params && params.preventReloading) {\n      return;\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n        header = _formatProblem2.header,\n        body = _formatProblem2.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Errors\", printableErrors);\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n    var overlayErrorsSettings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n    if (overlayErrorsSettings) {\n      var errorsToDisplay = typeof overlayErrorsSettings === \"function\" ? _errors.filter(overlayErrorsSettings) : _errors;\n      if (errorsToDisplay.length) {\n        overlay.send({\n          type: \"BUILD_ERROR\",\n          level: \"error\",\n          messages: _errors\n        });\n      }\n    }\n  },\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n    if (options.overlay) {\n      overlay.send({\n        type: \"DISMISS\"\n      });\n    }\n    sendMessage(\"Close\");\n  }\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AACzf,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAEN,GAAG,GAAGO,cAAc,CAACP,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEkB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEJ,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASE,cAAcA,CAACG,GAAG,EAAE;EAAE,IAAIV,GAAG,GAAGW,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO,OAAOV,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGY,MAAM,CAACZ,GAAG,CAAC;AAAE;AAC1H,SAASW,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EAAE,IAAIF,IAAI,KAAKG,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACP,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGU,MAAM,EAAET,KAAK,CAAC;AAAE;AACxX;AACA;AACA,OAAOU,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,aAAa,EAAEC,aAAa,QAAQ,cAAc;AAC3D,SAASC,GAAG,EAAEC,kBAAkB,EAAEC,WAAW,QAAQ,gBAAgB;AACrE,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,MAAM,4BAA4B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,cAAc,EAAE;EACvE,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACtC,CAAC,UAAU,EAAE,QAAQ,EAAE,eAAe,CAAC,CAACrC,OAAO,CAAC,UAAUsC,QAAQ,EAAE;MAClE,IAAI,OAAOD,cAAc,CAACC,QAAQ,CAAC,KAAK,QAAQ,EAAE;QAChD,IAAIC,2BAA2B,GAAGC,kBAAkB,CAACH,cAAc,CAACC,QAAQ,CAAC,CAAC;;QAE9E;QACA,IAAIG,qBAAqB,GAAG,IAAIC,QAAQ,CAAC,SAAS,EAAE,iBAAiB,CAACC,MAAM,CAACJ,2BAA2B,EAAE,oCAAoC,CAAC,CAAC;QAChJF,cAAc,CAACC,QAAQ,CAAC,GAAGG,qBAAqB;MAClD;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,IAAIG,MAAM,GAAG;EACXC,WAAW,EAAE,KAAK;EAClB;EACA;EACAC,WAAW,EAAE,OAAOC,gBAAgB,KAAK,WAAW,GAAGA,gBAAgB,GAAG;AAC5E,CAAC;;AAED;AACA,IAAIC,OAAO,GAAG;EACZC,GAAG,EAAE,KAAK;EACVC,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,mBAAmB,GAAG3B,QAAQ,CAAC4B,eAAe,CAAC;AACnD,IAAIC,eAAe,GAAG;EACpB,wBAAwB,EAAE,KAAK;EAC/B,gBAAgB,EAAE,KAAK;EACvBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE;AACX,CAAC;AACD,IAAIJ,mBAAmB,CAACJ,GAAG,KAAK,MAAM,EAAE;EACtCD,OAAO,CAACC,GAAG,GAAG,IAAI;EAClBM,eAAe,CAAC,wBAAwB,CAAC,GAAG,IAAI;AAClD;AACA,IAAIF,mBAAmB,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;EACjDL,OAAO,CAACE,UAAU,GAAG,IAAI;EACzBK,eAAe,CAAC,gBAAgB,CAAC,GAAG,IAAI;AAC1C;AACA,IAAIF,mBAAmB,CAACF,QAAQ,KAAK,MAAM,EAAE;EAC3CH,OAAO,CAACG,QAAQ,GAAG,IAAI;EACvBI,eAAe,CAACC,QAAQ,GAAG,IAAI;AACjC;AACA,IAAIH,mBAAmB,CAACD,OAAO,EAAE;EAC/B,IAAI;IACFJ,OAAO,CAACI,OAAO,GAAGM,IAAI,CAACC,KAAK,CAACN,mBAAmB,CAACD,OAAO,CAAC;EAC3D,CAAC,CAAC,OAAOQ,CAAC,EAAE;IACV9B,GAAG,CAAC+B,KAAK,CAAC,oDAAoD,EAAED,CAAC,CAAC;EACpE;;EAEA;EACA,IAAI,OAAOZ,OAAO,CAACI,OAAO,KAAK,QAAQ,EAAE;IACvCJ,OAAO,CAACI,OAAO,GAAG1D,aAAa,CAAC;MAC9BoE,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE;IACjB,CAAC,EAAEhB,OAAO,CAACI,OAAO,CAAC;IACnBhB,oBAAoB,CAACY,OAAO,CAACI,OAAO,CAAC;EACvC;EACAG,eAAe,CAACE,OAAO,GAAG,IAAI;AAChC;AACA,IAAIJ,mBAAmB,CAACY,OAAO,EAAE;EAC/BjB,OAAO,CAACiB,OAAO,GAAGZ,mBAAmB,CAACY,OAAO;AAC/C;AACA,IAAI,OAAOZ,mBAAmB,CAACa,SAAS,KAAK,WAAW,EAAE;EACxDlB,OAAO,CAACkB,SAAS,GAAG3C,MAAM,CAAC8B,mBAAmB,CAACa,SAAS,CAAC;AAC3D;;AAEA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B;EACA5C,aAAa,CAACQ,WAAW,CAACoC,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,KAAK,GAAG,MAAM,GAAGA,KAAK,CAAC;EAClFpC,WAAW,CAACoC,KAAK,CAAC;AACpB;AACA,IAAIpB,OAAO,CAACiB,OAAO,EAAE;EACnBE,cAAc,CAACnB,OAAO,CAACiB,OAAO,CAAC;AACjC;AACAlC,kBAAkB,CAACwB,eAAe,CAAC;AACnCc,IAAI,CAACC,gBAAgB,CAAC,cAAc,EAAE,YAAY;EAChD1B,MAAM,CAACC,WAAW,GAAG,IAAI;AAC3B,CAAC,CAAC;AACF,IAAIO,OAAO,GAAG,OAAOmB,MAAM,KAAK,WAAW,GAAG1C,aAAa,CAAC,OAAOmB,OAAO,CAACI,OAAO,KAAK,QAAQ,GAAG;EAChGoB,sBAAsB,EAAExB,OAAO,CAACI,OAAO,CAACoB,sBAAsB;EAC9DC,iBAAiB,EAAEzB,OAAO,CAACI,OAAO,CAACY;AACrC,CAAC,GAAG;EACFQ,sBAAsB,EAAE,KAAK;EAC7BC,iBAAiB,EAAEzB,OAAO,CAACI;AAC7B,CAAC,CAAC,GAAG;EACHsB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG,CAAC;AACzB,CAAC;AACD,IAAIC,eAAe,GAAG;EACpB1B,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,IAAII,mBAAmB,CAACJ,GAAG,KAAK,OAAO,EAAE;MACvC;IACF;IACAD,OAAO,CAACC,GAAG,GAAG,IAAI;EACpB,CAAC;EACDC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,IAAIG,mBAAmB,CAAC,aAAa,CAAC,KAAK,OAAO,EAAE;MAClD;IACF;IACAL,OAAO,CAACE,UAAU,GAAG,IAAI;EAC3B,CAAC;EACD0B,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B9C,GAAG,CAAC+C,IAAI,CAAC,6BAA6B,CAAC;;IAEvC;IACA,IAAI7B,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACsB,IAAI,CAAC;QACXI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA7C,WAAW,CAAC,SAAS,CAAC;EACxB,CAAC;EACD;AACF;AACA;EACE8C,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;IACzBpC,MAAM,CAACqC,YAAY,GAAGrC,MAAM,CAACE,WAAW;IACxCF,MAAM,CAACE,WAAW,GAAGkC,KAAK;EAC5B,CAAC;EACDf,OAAO,EAAEE,cAAc;EACvB;AACF;AACA;EACEf,OAAO,EAAE,SAASA,OAAOA,CAAC7C,KAAK,EAAE;IAC/B,IAAI,OAAO2E,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IACAlC,OAAO,CAACI,OAAO,GAAG7C,KAAK;IACvB6B,oBAAoB,CAACY,OAAO,CAACI,OAAO,CAAC;EACvC,CAAC;EACD;AACF;AACA;EACEc,SAAS,EAAE,SAASA,SAASA,CAAC3D,KAAK,EAAE;IACnC,IAAI8C,mBAAmB,CAACa,SAAS,KAAK,OAAO,EAAE;MAC7C;IACF;IACAlB,OAAO,CAACkB,SAAS,GAAG3D,KAAK;EAC3B,CAAC;EACD;AACF;AACA;EACE4C,QAAQ,EAAE,SAASA,QAAQA,CAAC5C,KAAK,EAAE;IACjCyC,OAAO,CAACG,QAAQ,GAAG5C,KAAK;EAC1B,CAAC;EACD;AACF;AACA;EACE,iBAAiB,EAAE,SAAS4E,cAAcA,CAACC,IAAI,EAAE;IAC/C,IAAIpC,OAAO,CAACG,QAAQ,EAAE;MACpBrB,GAAG,CAAC+C,IAAI,CAAC,EAAE,CAAClC,MAAM,CAACyC,IAAI,CAACC,UAAU,GAAG,GAAG,CAAC1C,MAAM,CAACyC,IAAI,CAACC,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC1C,MAAM,CAACyC,IAAI,CAACE,OAAO,EAAE,MAAM,CAAC,CAAC3C,MAAM,CAACyC,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAAC;IAClI;IACAtD,WAAW,CAAC,UAAU,EAAEmD,IAAI,CAAC;EAC/B,CAAC;EACD,UAAU,EAAE,SAASI,OAAOA,CAAA,EAAG;IAC7B1D,GAAG,CAAC+C,IAAI,CAAC,kBAAkB,CAAC;IAC5B,IAAI7B,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACsB,IAAI,CAAC;QACXI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA7C,WAAW,CAAC,SAAS,CAAC;EACxB,CAAC;EACDwD,EAAE,EAAE,SAASA,EAAEA,CAAA,EAAG;IAChBxD,WAAW,CAAC,IAAI,CAAC;IACjB,IAAIe,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACsB,IAAI,CAAC;QACXI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA5C,SAAS,CAACc,OAAO,EAAEJ,MAAM,CAAC;EAC5B,CAAC;EACD;EACA;AACF;AACA;EACE,iBAAiB,EAAE,SAAS8C,cAAcA,CAACC,IAAI,EAAE;IAC/C7D,GAAG,CAAC+C,IAAI,CAAC,EAAE,CAAClC,MAAM,CAACgD,IAAI,GAAG,IAAI,CAAChD,MAAM,CAACgD,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,kDAAkD,CAAC,CAAC;IACnHtB,IAAI,CAACuB,QAAQ,CAACC,MAAM,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;EACE,gBAAgB,EAAE,SAASC,aAAaA,CAACH,IAAI,EAAE;IAC7C7D,GAAG,CAAC+C,IAAI,CAAC,EAAE,CAAClC,MAAM,CAACgD,IAAI,GAAG,IAAI,CAAChD,MAAM,CAACgD,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,kDAAkD,CAAC,CAAC;IACnHtB,IAAI,CAACuB,QAAQ,CAACC,MAAM,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;AACA;EACE9B,QAAQ,EAAE,SAASA,QAAQA,CAACgC,SAAS,EAAEC,MAAM,EAAE;IAC7ClE,GAAG,CAACmE,IAAI,CAAC,2BAA2B,CAAC;IACrC,IAAIC,iBAAiB,GAAGH,SAAS,CAACI,GAAG,CAAC,UAAUtC,KAAK,EAAE;MACrD,IAAIuC,cAAc,GAAGxE,aAAa,CAAC,SAAS,EAAEiC,KAAK,CAAC;QAClDwC,MAAM,GAAGD,cAAc,CAACC,MAAM;QAC9BC,IAAI,GAAGF,cAAc,CAACE,IAAI;MAC5B,OAAO,EAAE,CAAC3D,MAAM,CAAC0D,MAAM,EAAE,IAAI,CAAC,CAAC1D,MAAM,CAAClB,SAAS,CAAC6E,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IACFrE,WAAW,CAAC,UAAU,EAAEiE,iBAAiB,CAAC;IAC1C,KAAK,IAAItG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsG,iBAAiB,CAACpG,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjDkC,GAAG,CAACmE,IAAI,CAACC,iBAAiB,CAACtG,CAAC,CAAC,CAAC;IAChC;IACA,IAAI2G,sBAAsB,GAAG,OAAOvD,OAAO,CAACI,OAAO,KAAK,SAAS,GAAGJ,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAACW,QAAQ;IACjI,IAAIwC,sBAAsB,EAAE;MAC1B,IAAIC,iBAAiB,GAAG,OAAOD,sBAAsB,KAAK,UAAU,GAAGR,SAAS,CAAC3G,MAAM,CAACmH,sBAAsB,CAAC,GAAGR,SAAS;MAC3H,IAAIS,iBAAiB,CAAC1G,MAAM,EAAE;QAC5BsD,OAAO,CAACsB,IAAI,CAAC;UACXI,IAAI,EAAE,aAAa;UACnBV,KAAK,EAAE,SAAS;UAChBqC,QAAQ,EAAEV;QACZ,CAAC,CAAC;MACJ;IACF;IACA,IAAIC,MAAM,IAAIA,MAAM,CAACU,gBAAgB,EAAE;MACrC;IACF;IACAxE,SAAS,CAACc,OAAO,EAAEJ,MAAM,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACEkB,MAAM,EAAE,SAASA,MAAMA,CAAC6C,OAAO,EAAE;IAC/B7E,GAAG,CAAC+B,KAAK,CAAC,2CAA2C,CAAC;IACtD,IAAI+C,eAAe,GAAGD,OAAO,CAACR,GAAG,CAAC,UAAUtC,KAAK,EAAE;MACjD,IAAIgD,eAAe,GAAGjF,aAAa,CAAC,OAAO,EAAEiC,KAAK,CAAC;QACjDwC,MAAM,GAAGQ,eAAe,CAACR,MAAM;QAC/BC,IAAI,GAAGO,eAAe,CAACP,IAAI;MAC7B,OAAO,EAAE,CAAC3D,MAAM,CAAC0D,MAAM,EAAE,IAAI,CAAC,CAAC1D,MAAM,CAAClB,SAAS,CAAC6E,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IACFrE,WAAW,CAAC,QAAQ,EAAE2E,eAAe,CAAC;IACtC,KAAK,IAAIhH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgH,eAAe,CAAC9G,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/CkC,GAAG,CAAC+B,KAAK,CAAC+C,eAAe,CAAChH,CAAC,CAAC,CAAC;IAC/B;IACA,IAAIkH,qBAAqB,GAAG,OAAO9D,OAAO,CAACI,OAAO,KAAK,SAAS,GAAGJ,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAACU,MAAM;IAC9H,IAAIgD,qBAAqB,EAAE;MACzB,IAAIC,eAAe,GAAG,OAAOD,qBAAqB,KAAK,UAAU,GAAGH,OAAO,CAACvH,MAAM,CAAC0H,qBAAqB,CAAC,GAAGH,OAAO;MACnH,IAAII,eAAe,CAACjH,MAAM,EAAE;QAC1BsD,OAAO,CAACsB,IAAI,CAAC;UACXI,IAAI,EAAE,aAAa;UACnBV,KAAK,EAAE,OAAO;UACdqC,QAAQ,EAAEE;QACZ,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD;AACF;AACA;EACE9C,KAAK,EAAE,SAASA,KAAKA,CAACmD,MAAM,EAAE;IAC5BlF,GAAG,CAAC+B,KAAK,CAACmD,MAAM,CAAC;EACnB,CAAC;EACDC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;IACtBnF,GAAG,CAAC+C,IAAI,CAAC,eAAe,CAAC;IACzB,IAAI7B,OAAO,CAACI,OAAO,EAAE;MACnBA,OAAO,CAACsB,IAAI,CAAC;QACXI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA7C,WAAW,CAAC,OAAO,CAAC;EACtB;AACF,CAAC;AACD,IAAIiF,SAAS,GAAG/E,eAAe,CAACkB,mBAAmB,CAAC;AACpD1B,MAAM,CAACuF,SAAS,EAAEvC,eAAe,EAAE3B,OAAO,CAACkB,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}