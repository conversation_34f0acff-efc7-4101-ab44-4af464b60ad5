import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Brain, 
  Search, 
  CheckCircle, 
  BarChart3, 
  Users, 
  Zap,
  ArrowRight,
  Play,
  Star,
  Shield,
  Clock,
  Sparkles,
  Target,
  TrendingUp,
  Award,
  Database,
  Globe,
  Cpu
} from 'lucide-react';
import AnimatedBackground from '../components/AnimatedBackground';
import FeatureSection from '../components/FeatureSection';
import WorkflowAnimation from '../components/WorkflowAnimation';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const handleGetStarted = () => {
    navigate('/analysis');
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <AnimatedBackground />
      
      {/* Floating particles effect */}
      <div className="floating-particles">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${8 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
      
      {/* Hero Section */}
      <motion.section 
        className="relative z-10 min-h-screen flex items-center justify-center px-4 section-glow"
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 1.2 }}
      >
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
            className="mb-8"
          >
            <motion.div
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200, delay: 0.1 }}
            >
              <Sparkles className="w-4 h-4 text-cyan-400" />
              <span className="text-sm font-medium text-cyan-300">Next-Generation AI Talent Analysis</span>
            </motion.div>
            
            <h1 className="hero-title text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight">
              <span className="gradient-text">TalentSphere</span>
              <br />
              <span className="text-white">AI</span>
            </h1>
            <p className="hero-subtitle text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
              Revolutionary Multi-Agent AI Platform for Comprehensive Talent Discovery, 
              Deep Analysis, and Strategic Hiring Intelligence with Unparalleled Insights
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
          >
            <button
              onClick={handleGetStarted}
              className="btn-primary text-lg px-10 py-5 group hover-lift"
            >
              <Brain className="w-6 h-6" />
              Discover Talent
              <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
            </button>
            <button className="btn-secondary text-lg px-10 py-5 hover-glow">
              <Play className="w-6 h-6" />
              Watch Demo
            </button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"
          >
            <div className="glass-card p-8 rounded-2xl text-center hover-lift">
              <div className="icon-container mx-auto">
                <Brain className="w-8 h-8 text-blue-400" />
              </div>
              <h3 className="text-xl font-bold mb-3">AI-Powered Intelligence</h3>
              <p className="text-gray-400 text-sm leading-relaxed">Advanced multi-agent system for comprehensive talent evaluation with unprecedented depth and accuracy</p>
            </div>
            <div className="glass-card p-8 rounded-2xl text-center hover-lift">
              <div className="icon-container mx-auto">
                <Search className="w-8 h-8 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold mb-3">Deep Research Engine</h3>
              <p className="text-gray-400 text-sm leading-relaxed">Automated research across LinkedIn, GitHub, and professional platforms for complete talent profiles</p>
            </div>
            <div className="glass-card p-8 rounded-2xl text-center hover-lift">
              <div className="icon-container mx-auto">
                <Target className="w-8 h-8 text-cyan-400" />
              </div>
              <h3 className="text-xl font-bold mb-3">Strategic Insights</h3>
              <p className="text-gray-400 text-sm leading-relaxed">Comprehensive analysis with rankings, insights, and strategic hiring recommendations</p>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Features Section */}
      <FeatureSection />

      {/* Workflow Animation Section */}
      <section className="relative z-10 py-32 px-4 section-glow">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <motion.div
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200 }}
              viewport={{ once: true }}
            >
              <Cpu className="w-4 h-4 text-purple-400" />
              <span className="text-sm font-medium text-purple-300">AI-Powered Workflow</span>
            </motion.div>
            <h2 className="text-4xl md:text-6xl font-bold mb-8">
              <span className="gradient-text">How TalentSphere Works</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Experience our revolutionary multi-agent AI system as it processes talent through 
              advanced intelligence pipelines, research networks, and validation protocols
            </p>
          </motion.div>
          
          <WorkflowAnimation />
        </div>
      </section>

      {/* Performance Stats */}
      <section className="relative z-10 py-32 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="glass-intense p-12 lg:p-16 rounded-3xl hover-glow"
          >
            <div className="text-center mb-16">
              <motion.div
                className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200 }}
                viewport={{ once: true }}
              >
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-sm font-medium text-green-300">Performance Excellence</span>
              </motion.div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="gradient-text">Unmatched Performance</span>
              </h2>
              <p className="text-xl text-gray-300 max-width-3xl mx-auto">
                Built for speed, precision, and scalability with cutting-edge AI optimizations
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { value: "15x", label: "Faster Analysis", icon: Zap, color: "text-yellow-400" },
                { value: "98%", label: "Accuracy Rate", icon: Target, color: "text-green-400" },
                { value: "7", label: "AI Agents", icon: Brain, color: "text-purple-400" },
                { value: "100+", label: "Data Points", icon: Database, color: "text-cyan-400" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.8 }}
                  viewport={{ once: true }}
                  className="stat-card hover-lift"
                >
                  <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-4`} />
                  <div className="stat-value">{stat.value}</div>
                  <div className="stat-label">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Features Grid */}
      <section className="relative z-10 py-32 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <motion.div
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200 }}
              viewport={{ once: true }}
            >
              <Award className="w-4 h-4 text-amber-400" />
              <span className="text-sm font-medium text-amber-300">Advanced Capabilities</span>
            </motion.div>
            <h2 className="text-4xl md:text-6xl font-bold mb-8">
              <span className="gradient-text">Revolutionary Features</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Comprehensive suite of AI-powered tools designed for the future of talent acquisition
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {enhancedFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="feature-card hover-lift"
              >
                <div className="icon-container">
                  <feature.icon className="w-8 h-8 text-blue-400" />
                </div>
                <h3 className="text-2xl font-bold mb-4">{feature.title}</h3>
                <p className="text-gray-400 leading-relaxed mb-6">{feature.description}</p>
                <div className="space-y-3">
                  {feature.benefits.map((benefit, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                      <span className="text-sm text-gray-300">{benefit}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-32 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 60 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="text-center glass-intense p-12 lg:p-20 rounded-3xl hover-glow section-glow"
          >
            <motion.div
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 200 }}
              viewport={{ once: true }}
            >
              <Sparkles className="w-4 h-4 text-cyan-400" />
              <span className="text-sm font-medium text-cyan-300">Ready to Transform?</span>
            </motion.div>
            <h2 className="text-4xl md:text-6xl font-bold mb-8">
              Revolutionize Your <span className="gradient-text">Talent Strategy</span>
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Experience the future of talent discovery with our AI-powered platform. 
              Unlock unprecedented insights and make data-driven hiring decisions with confidence.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button
                onClick={handleGetStarted}
                className="btn-primary text-xl px-12 py-6 group hover-lift"
              >
                <Brain className="w-6 h-6" />
                Start Discovery
                <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="btn-secondary text-xl px-12 py-6 hover-glow">
                <Globe className="w-6 h-6" />
                Schedule Demo
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 py-16 border-t border-white/10 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="mb-8">
              <h3 className="text-3xl font-bold gradient-text mb-3">TalentSphere AI</h3>
              <p className="text-gray-400 text-lg">Revolutionary AI-Powered Talent Analysis Platform</p>
            </div>
            <div className="flex justify-center gap-8 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold gradient-text">15x</div>
                <div className="text-sm text-gray-500">Faster</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold gradient-text">98%</div>
                <div className="text-sm text-gray-500">Accurate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold gradient-text">7</div>
                <div className="text-sm text-gray-500">AI Agents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold gradient-text">100+</div>
                <div className="text-sm text-gray-500">Data Points</div>
              </div>
            </div>
            <div className="text-gray-500 text-sm">
              © 2024 TalentSphere AI. Powered by Advanced Multi-Agent Intelligence.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

const enhancedFeatures = [
  {
    icon: Brain,
    title: "Multi-Agent AI System",
    description: "Seven specialized AI agents working in parallel for comprehensive talent analysis with unprecedented depth and accuracy.",
    benefits: [
      "GPT-4 powered intelligence",
      "Parallel processing architecture",
      "98% accuracy rate",
      "Semantic understanding"
    ]
  },
  {
    icon: Search,
    title: "Deep Research Engine",
    description: "Automated research across LinkedIn, GitHub, and professional platforms to build complete talent profiles with strategic insights.",
    benefits: [
      "Multi-platform intelligence",
      "Real-time data collection",
      "Professional verification",
      "Social proof analysis"
    ]
  },
  {
    icon: Shield,
    title: "Identity Validation",
    description: "Advanced validation algorithms ensure talent authenticity through cross-platform verification and consistency analysis.",
    benefits: [
      "Multi-factor verification",
      "Fraud detection AI",
      "Consistency analysis",
      "Confidence scoring"
    ]
  },
  {
    icon: Target,
    title: "Precision Matching",
    description: "Intelligent scoring and ranking against job requirements with semantic analysis and cultural fit assessment.",
    benefits: [
      "Semantic matching",
      "Cultural fit analysis",
      "Skills gap identification",
      "Growth potential scoring"
    ]
  },
  {
    icon: TrendingUp,
    title: "Predictive Analytics",
    description: "Advanced analytics to predict candidate success, retention probability, and career trajectory patterns.",
    benefits: [
      "Success prediction",
      "Retention modeling",
      "Career trajectory analysis",
      "Performance forecasting"
    ]
  },
  {
    icon: Globe,
    title: "Global Talent Pool",
    description: "Access worldwide talent with localized insights, cultural considerations, and regional expertise mapping.",
    benefits: [
      "Global reach",
      "Cultural intelligence",
      "Regional expertise",
      "Diversity insights"
    ]
  }
];

export default HomePage;