{"ast": null, "code": "import { isZeroValueString } from '../../utils/is-zero-value-string.mjs';\nfunction isNone(value) {\n  if (typeof value === \"number\") {\n    return value === 0;\n  } else if (value !== null) {\n    return value === \"none\" || value === \"0\" || isZeroValueString(value);\n  }\n}\nexport { isNone };", "map": {"version": 3, "names": ["isZeroValueString", "isNone", "value"], "sources": ["D:/Projects/ai-hr-agent/frontend/node_modules/framer-motion/dist/es/animation/utils/is-none.mjs"], "sourcesContent": ["import { isZeroValueString } from '../../utils/is-zero-value-string.mjs';\n\nfunction isNone(value) {\n    if (typeof value === \"number\") {\n        return value === 0;\n    }\n    else if (value !== null) {\n        return value === \"none\" || value === \"0\" || isZeroValueString(value);\n    }\n}\n\nexport { isNone };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,sCAAsC;AAExE,SAASC,MAAMA,CAACC,KAAK,EAAE;EACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOA,KAAK,KAAK,CAAC;EACtB,CAAC,MACI,IAAIA,KAAK,KAAK,IAAI,EAAE;IACrB,OAAOA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,GAAG,IAAIF,iBAAiB,CAACE,KAAK,CAAC;EACxE;AACJ;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}