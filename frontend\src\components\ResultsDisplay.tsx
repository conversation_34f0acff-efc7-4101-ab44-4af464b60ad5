import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Download, 
  Eye, 
  X, 
  BarChart3, 
  <PERSON>, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  Clock,
  Star,
  TrendingUp,
  Award,
  RefreshCw
} from 'lucide-react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface AnalysisResult {
  success: boolean;
  job_id: string;
  message: string;
  processing_time?: number;
  statistics?: {
    total_files_processed: number;
    candidates_parsed: number;
    candidates_matched: number;
    candidates_researched: number;
    candidates_validated: number;
    reports_generated: number;
  };
  final_report?: string;
  candidate_reports?: Record<string, string>;
  report_files?: Record<string, string | string[]>;
  warnings?: string[];
  errors?: string[];
}

interface ResultsDisplayProps {
  result: AnalysisResult;
  onStartNew: () => void;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ result, onStartNew }) => {
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [expandedSection, setExpandedSection] = useState<string | null>('executive');
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const handleDownloadReport = async (reportType: string, content: string, fileName: string) => {
    setIsGeneratingPDF(true);
    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const margin = 20;
      const maxWidth = pageWidth - 2 * margin;
      
      // Add title
      pdf.setFontSize(20);
      pdf.setFont('', 'bold');
      pdf.text('ResumeGPT Pro - Analysis Report', margin, 30);
      
      // Add content
      pdf.setFontSize(12);
      pdf.setFont('', 'normal');
      
      const lines = pdf.splitTextToSize(content, maxWidth);
      let yPosition = 50;
      
      lines.forEach((line: string) => {
        if (yPosition > pdf.internal.pageSize.getHeight() - 30) {
          pdf.addPage();
          yPosition = 30;
        }
        pdf.text(line, margin, yPosition);
        yPosition += 7;
      });
      
      pdf.save(fileName);
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const formatReport = (content: string) => {
    // Basic formatting for display
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n/g, '<br>')
      .replace(/•/g, '&bull;');
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 65) return 'text-yellow-400';
    if (score >= 50) return 'text-orange-400';
    return 'text-red-400';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 65) return 'Good';
    if (score >= 50) return 'Fair';
    return 'Poor';
  };

  return (
    <motion.div
      key="results"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-7xl mx-auto"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200 }}
          className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle className="w-8 h-8 text-green-400" />
        </motion.div>
        <h2 className="text-4xl font-bold mb-4">
          <span className="gradient-text">Analysis Complete!</span>
        </h2>
        <p className="text-xl text-gray-300">
          {result.message} • Processed in {result.processing_time?.toFixed(2)}s
        </p>
      </div>

      {/* Statistics Overview */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
        {[
          { label: 'Files Processed', value: result.statistics?.total_files_processed || 0, icon: FileText, color: 'text-blue-400' },
          { label: 'Candidates Parsed', value: result.statistics?.candidates_parsed || 0, icon: Users, color: 'text-purple-400' },
          { label: 'Top Matches', value: result.statistics?.candidates_matched || 0, icon: Star, color: 'text-yellow-400' },
          { label: 'Researched', value: result.statistics?.candidates_researched || 0, icon: TrendingUp, color: 'text-green-400' },
          { label: 'Validated', value: result.statistics?.candidates_validated || 0, icon: CheckCircle, color: 'text-cyan-400' },
          { label: 'Reports Generated', value: result.statistics?.reports_generated || 0, icon: BarChart3, color: 'text-orange-400' }
        ].map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="glass p-4 rounded-xl text-center"
          >
            <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2`} />
            <div className="text-2xl font-bold text-white">{stat.value}</div>
            <div className="text-xs text-gray-400">{stat.label}</div>
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        {/* Left Column - Reports List */}
        <div className="lg:col-span-1">
          <div className="glass-intense p-6 rounded-2xl sticky top-6">
            <h3 className="text-xl font-bold mb-6 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-cyan-400" />
              Available Reports
            </h3>
            
            <div className="space-y-3">
              {/* Executive Summary */}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setExpandedSection(expandedSection === 'executive' ? null : 'executive')}
                className={`w-full p-4 rounded-xl text-left transition-all ${
                  expandedSection === 'executive' 
                    ? 'bg-blue-500/20 border border-blue-400/30' 
                    : 'bg-white/5 hover:bg-white/10'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Award className="w-5 h-5 text-blue-400" />
                    <div>
                      <div className="font-semibold">Executive Summary</div>
                      <div className="text-sm text-gray-400">Complete analysis overview</div>
                    </div>
                  </div>
                  <Eye className="w-4 h-4" />
                </div>
              </motion.button>

              {/* Individual Candidate Reports */}
              {result.candidate_reports && Object.keys(result.candidate_reports).length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-400 mb-2 px-2">Individual Reports</h4>
                  {Object.entries(result.candidate_reports).map(([candidateId, report], index) => {
                    const candidateName = candidateId.split('_')[0] || `Candidate ${index + 1}`;
                    return (
                      <motion.button
                        key={candidateId}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setSelectedReport(candidateId)}
                        className="w-full p-3 rounded-lg bg-white/5 hover:bg-white/10 text-left transition-all mb-2"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Users className="w-4 h-4 text-green-400" />
                            <span className="text-sm font-medium">{candidateName}</span>
                          </div>
                          <Eye className="w-3 h-3" />
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              )}

              {/* Download Options */}
              <div className="border-t border-gray-700 pt-4 mt-4">
                <h4 className="text-sm font-medium text-gray-400 mb-3">Download Reports</h4>
                <div className="space-y-2">
                  {result.report_files && Object.entries(result.report_files).map(([type, filename]) => (
                    <button
                      key={type}
                      onClick={() => {
                        if (type === 'executive_summary' && result.final_report) {
                          handleDownloadReport('executive', result.final_report, `executive_summary_${result.job_id}.pdf`);
                        } else {
                          // Handle other download types
                          window.open(`http://localhost:8000/download/${result.job_id}/${type}`, '_blank');
                        }
                      }}
                      disabled={isGeneratingPDF}
                      className="w-full p-2 rounded-lg bg-purple-500/20 hover:bg-purple-500/30 text-left transition-all flex items-center gap-2 text-sm"
                    >
                      <Download className="w-4 h-4 text-purple-400" />
                      {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      {isGeneratingPDF && <div className="loading-spinner w-3 h-3 ml-auto" />}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Report Content */}
        <div className="lg:col-span-2">
          <AnimatePresence mode="wait">
            {/* Executive Summary */}
            {expandedSection === 'executive' && result.final_report && (
              <motion.div
                key="executive"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="glass-intense p-8 rounded-2xl"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold flex items-center gap-3">
                    <Award className="w-6 h-6 text-blue-400" />
                    Executive Summary
                  </h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleDownloadReport('executive', result.final_report!, `executive_summary_${result.job_id}.pdf`)}
                      disabled={isGeneratingPDF}
                      className="btn-secondary px-4 py-2 flex items-center gap-2"
                    >
                      <Download className="w-4 h-4" />
                      {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
                      {isGeneratingPDF && <div className="loading-spinner w-3 h-3" />}
                    </button>
                    <button
                      onClick={() => setExpandedSection(null)}
                      className="btn-secondary px-3 py-2"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="prose prose-invert max-w-none">
                  <div 
                    className="text-gray-300 leading-relaxed whitespace-pre-wrap"
                    dangerouslySetInnerHTML={{ __html: formatReport(result.final_report) }}
                  />
                </div>
              </motion.div>
            )}

            {/* Individual Candidate Report */}
            {selectedReport && result.candidate_reports?.[selectedReport] && (
              <motion.div
                key={selectedReport}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="glass-intense p-8 rounded-2xl"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold flex items-center gap-3">
                    <Users className="w-6 h-6 text-green-400" />
                    {selectedReport.split('_')[0] || 'Candidate Report'}
                  </h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleDownloadReport('candidate', result.candidate_reports![selectedReport], `candidate_${selectedReport}_${result.job_id}.pdf`)}
                      disabled={isGeneratingPDF}
                      className="btn-secondary px-4 py-2 flex items-center gap-2"
                    >
                      <Download className="w-4 h-4" />
                      {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
                      {isGeneratingPDF && <div className="loading-spinner w-3 h-3" />}
                    </button>
                    <button
                      onClick={() => setSelectedReport(null)}
                      className="btn-secondary px-3 py-2"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="prose prose-invert max-w-none">
                  <div 
                    className="text-gray-300 leading-relaxed whitespace-pre-wrap"
                    dangerouslySetInnerHTML={{ __html: formatReport(result.candidate_reports[selectedReport]) }}
                  />
                </div>
              </motion.div>
            )}

            {/* Default View */}
            {!expandedSection && !selectedReport && (
              <motion.div
                key="default"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="glass-intense p-8 rounded-2xl text-center"
              >
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold mb-2">Select a Report to View</h3>
                <p className="text-gray-400 mb-6">
                  Choose from the executive summary or individual candidate reports to view detailed analysis
                </p>
                <button
                  onClick={() => setExpandedSection('executive')}
                  className="btn-primary px-6 py-3 flex items-center gap-2 mx-auto"
                >
                  <Award className="w-5 h-5" />
                  View Executive Summary
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Warnings and Errors */}
      {(result.warnings?.length || result.errors?.length) && (
        <div className="mt-8 space-y-4">
          {result.warnings && result.warnings.length > 0 && (
            <div className="glass p-4 rounded-xl border-l-4 border-yellow-400">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="w-5 h-5 text-yellow-400" />
                <h4 className="font-semibold text-yellow-400">Warnings</h4>
              </div>
              <ul className="space-y-1 text-sm text-gray-300">
                {result.warnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}

          {result.errors && result.errors.length > 0 && (
            <div className="glass p-4 rounded-xl border-l-4 border-red-400">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="w-5 h-5 text-red-400" />
                <h4 className="font-semibold text-red-400">Errors</h4>
              </div>
              <ul className="space-y-1 text-sm text-gray-300">
                {result.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="text-center mt-12">
        <button
          onClick={onStartNew}
          className="btn-primary px-8 py-4 flex items-center gap-3 mx-auto"
        >
          <RefreshCw className="w-5 h-5" />
          Start New Analysis
        </button>
      </div>
    </motion.div>
  );
};

export default ResultsDisplay;