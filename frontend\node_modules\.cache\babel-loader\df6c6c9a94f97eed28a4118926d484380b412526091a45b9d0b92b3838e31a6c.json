{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\ai-hr-agent\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport HomePage from './pages/HomePage';\nimport AnalysisPage from './pages/AnalysisPage';\nimport GoogleOAuthHandler from './components/GoogleOAuthHandler';\nimport './styles/global.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/analysis\",\n          element: /*#__PURE__*/_jsxDEV(AnalysisPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/google/callback\",\n          element: /*#__PURE__*/_jsxDEV(GoogleOAuthHandler, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 56\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n          duration: 4000,\n          style: {\n            background: 'rgba(17, 25, 40, 0.8)',\n            color: '#fff',\n            border: '1px solid rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Toaster", "HomePage", "AnalysisPage", "GoogleOAuthHandler", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "toastOptions", "duration", "style", "background", "color", "border", "<PERSON><PERSON>ilter", "_c", "$RefreshReg$"], "sources": ["D:/Projects/ai-hr-agent/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport { Toaster } from 'react-hot-toast';\r\nimport HomePage from './pages/HomePage';\r\nimport AnalysisPage from './pages/AnalysisPage';\r\nimport GoogleOAuthHandler from './components/GoogleOAuthHandler';\r\nimport './styles/global.css';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <div className=\"App\">\r\n        <Routes>\r\n          <Route path=\"/\" element={<HomePage />} />\r\n          <Route path=\"/analysis\" element={<AnalysisPage />} />\r\n          <Route path=\"/auth/google/callback\" element={<GoogleOAuthHandler />} />\r\n        </Routes>\r\n        <Toaster\r\n          position=\"top-right\"\r\n          toastOptions={{\r\n            duration: 4000,\r\n            style: {\r\n              background: 'rgba(17, 25, 40, 0.8)',\r\n              color: '#fff',\r\n              border: '1px solid rgba(255, 255, 255, 0.1)',\r\n              backdropFilter: 'blur(10px)',\r\n            },\r\n          }}\r\n        />\r\n      </div>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACR,MAAM;IAAAU,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA,CAACP,MAAM;QAAAS,QAAA,gBACLF,OAAA,CAACN,KAAK;UAACU,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEL,OAAA,CAACJ,QAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCT,OAAA,CAACN,KAAK;UAACU,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEL,OAAA,CAACH,YAAY;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDT,OAAA,CAACN,KAAK;UAACU,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAEL,OAAA,CAACF,kBAAkB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACTT,OAAA,CAACL,OAAO;QACNe,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAE;UACZC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACLC,UAAU,EAAE,uBAAuB;YACnCC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,oCAAoC;YAC5CC,cAAc,EAAE;UAClB;QACF;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACS,EAAA,GAxBQjB,GAAG;AA0BZ,eAAeA,GAAG;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}