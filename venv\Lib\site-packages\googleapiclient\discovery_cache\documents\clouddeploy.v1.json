{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://clouddeploy.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Deploy", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/deploy/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "clouddeploy:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://clouddeploy.mtls.googleapis.com/", "name": "clouddeploy", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getConfig": {"description": "Gets the configuration for a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/config", "httpMethod": "GET", "id": "clouddeploy.projects.locations.getConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of requested configuration.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/config$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Config"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "clouddeploy.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"customTargetTypes": {"methods": {"create": {"description": "Creates a new CustomTargetType in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes", "httpMethod": "POST", "id": "clouddeploy.projects.locations.customTargetTypes.create", "parameterOrder": ["parent"], "parameters": {"customTargetTypeId": {"description": "Required. ID of the `CustomTargetType`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which the `CustomTargetType` must be created. The format is `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/customTargetTypes", "request": {"$ref": "CustomTargetType"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single CustomTargetType.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}", "httpMethod": "DELETE", "id": "clouddeploy.projects.locations.customTargetTypes.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, then deleting an already deleted or non-existing `CustomTargetType` will succeed.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `CustomTargetType` to delete. Format must be `projects/{project_id}/locations/{location_name}/customTargetTypes/{custom_target_type}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/customTargetTypes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single CustomTargetType.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.customTargetTypes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `CustomTargetType`. Format must be `projects/{project_id}/locations/{location_name}/customTargetTypes/{custom_target_type}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/customTargetTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "CustomTargetType"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}:getIamPolicy", "httpMethod": "GET", "id": "clouddeploy.projects.locations.customTargetTypes.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/customTargetTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists CustomTargetTypes in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes", "httpMethod": "GET", "id": "clouddeploy.projects.locations.customTargetTypes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter custom target types to be returned. See https://google.aip.dev/160 for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of `CustomTargetType` objects to return. The service may return fewer than this value. If unspecified, at most 50 `CustomTargetType` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListCustomTargetTypes` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent that owns this collection of custom target types. Format must be `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/customTargetTypes", "response": {"$ref": "ListCustomTargetTypesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a single CustomTargetType.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}", "httpMethod": "PATCH", "id": "clouddeploy.projects.locations.customTargetTypes.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, updating a `CustomTargetType` that does not exist will result in the creation of a new `CustomTargetType`.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Name of the `CustomTargetType`. Format is `projects/{project}/locations/{location}/customTargetTypes/{customTargetType}`. The `customTargetType` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/customTargetTypes/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the `CustomTargetType` resource. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it's in the mask. If the user doesn't provide a mask then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "CustomTargetType"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/customTargetTypes/{customTargetTypesId}:setIamPolicy", "httpMethod": "POST", "id": "clouddeploy.projects.locations.customTargetTypes.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/customTargetTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "deliveryPipelines": {"methods": {"create": {"description": "Creates a new DeliveryPipeline in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.create", "parameterOrder": ["parent"], "parameters": {"deliveryPipelineId": {"description": "Required. ID of the `DeliveryPipeline`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which the `DeliveryPipeline` must be created. The format is `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/deliveryPipelines", "request": {"$ref": "DeliveryPipeline"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single DeliveryPipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}", "httpMethod": "DELETE", "id": "clouddeploy.projects.locations.deliveryPipelines.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, then deleting an already deleted or non-existing `DeliveryPipeline` will succeed.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "force": {"description": "Optional. If set to true, all child resources under this pipeline will also be deleted. Otherwise, the request will only work if the pipeline has no child resources.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the `DeliveryPipeline` to delete. The format is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single DeliveryPipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `DeliveryPipeline`. Format must be `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DeliveryPipeline"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:getIamPolicy", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists DeliveryPipelines in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter pipelines to be returned. See https://google.aip.dev/160 for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of pipelines to return. The service may return fewer than this value. If unspecified, at most 50 pipelines will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDeliveryPipelines` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of pipelines. Format must be `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/deliveryPipelines", "response": {"$ref": "ListDeliveryPipelinesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single DeliveryPipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}", "httpMethod": "PATCH", "id": "clouddeploy.projects.locations.deliveryPipelines.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, updating a `DeliveryPipeline` that does not exist will result in the creation of a new `DeliveryPipeline`.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Name of the `DeliveryPipeline`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}`. The `deliveryPipeline` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the `DeliveryPipeline` resource. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it's in the mask. If the user doesn't provide a mask then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "DeliveryPipeline"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rollbackTarget": {"description": "Creates a `Rollout` to roll back the specified target.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:rollbackTarget", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.rollbackTarget", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The `DeliveryPipeline` for which the rollback `Rollout` must be created. The format is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:rollbackTarget", "request": {"$ref": "RollbackTargetRequest"}, "response": {"$ref": "RollbackTargetResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:setIamPolicy", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}:testIamPermissions", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"automationRuns": {"methods": {"cancel": {"description": "Cancels an AutomationRun. The `state` of the `AutomationRun` after cancelling is `CANCELLED`. `CancelAutomationRun` can be called on AutomationRun in the state `IN_PROGRESS` and `PENDING`; AutomationRun in a different state returns an `FAILED_PRECONDITION` error.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automationRuns/{automationRunsId}:cancel", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.automationRuns.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `AutomationRun`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/automationRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelAutomationRunRequest"}, "response": {"$ref": "CancelAutomationRunResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single AutomationRun.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automationRuns/{automationRunsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.automationRuns.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `AutomationRun`. Format must be `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/automationRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AutomationRun"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists AutomationRuns in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automationRuns", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.automationRuns.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter automationRuns to be returned. All fields can be used in the filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of automationRuns to return. The service may return fewer than this value. If unspecified, at most 50 automationRuns will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAutomationRuns` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent `Delivery Pipeline`, which owns this collection of automationRuns. Format must be `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/automationRuns", "response": {"$ref": "ListAutomationRunsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "automations": {"methods": {"create": {"description": "Creates a new Automation in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.automations.create", "parameterOrder": ["parent"], "parameters": {"automationId": {"description": "Required. ID of the `Automation`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which the `Automation` must be created. The format is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/automations", "request": {"$ref": "Automation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Automation resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations/{automationsId}", "httpMethod": "DELETE", "id": "clouddeploy.projects.locations.deliveryPipelines.automations.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, then deleting an already deleted or non-existing `Automation` will succeed.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. The weak etag of the request. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `Automation` to delete. The format is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/automations/{automation_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/automations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and verify whether the resource exists, but do not actually post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Automation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations/{automationsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.automations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `Automation`. Format must be `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/automations/{automation_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/automations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Automation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Automations in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.automations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter automations to be returned. All fields can be used in the filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of automations to return. The service may return fewer than this value. If unspecified, at most 50 automations will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAutomations` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent `Delivery Pipeline`, which owns this collection of automations. Format must be `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/automations", "response": {"$ref": "ListAutomationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Automation resource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/automations/{automationsId}", "httpMethod": "PATCH", "id": "clouddeploy.projects.locations.deliveryPipelines.automations.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, updating a `Automation` that does not exist will result in the creation of a new `Automation`.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. Name of the `Automation`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/{automation}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/automations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the `Automation` resource. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it's in the mask. If the user doesn't provide a mask then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Automation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "releases": {"methods": {"abandon": {"description": "Abandons a Release in the Delivery Pipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}:abandon", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.abandon", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the Release. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:abandon", "request": {"$ref": "AbandonReleaseRequest"}, "response": {"$ref": "AbandonReleaseResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new Release in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.create", "parameterOrder": ["parent"], "parameters": {"overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "location": "query", "repeated": true, "type": "string"}, "parent": {"description": "Required. The parent collection in which the `Release` is created. The format is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}, "releaseId": {"description": "Required. ID of the `Release`.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/releases", "request": {"$ref": "Release"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Release.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `Release`. Format must be `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Releases in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter releases to be returned. See https://google.aip.dev/160 for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of `Release` objects to return. The service may return fewer than this value. If unspecified, at most 50 `Release` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListReleases` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The `DeliveryPipeline` which owns this collection of `Release` objects.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/releases", "response": {"$ref": "ListReleasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"rollouts": {"methods": {"advance": {"description": "Advances a Rollout in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:advance", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.advance", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the Rollout. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:advance", "request": {"$ref": "AdvanceRolloutRequest"}, "response": {"$ref": "AdvanceRolloutResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "approve": {"description": "Approves a Rollout.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:approve", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.approve", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the Rollout. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:approve", "request": {"$ref": "ApproveRolloutRequest"}, "response": {"$ref": "ApproveRolloutResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "cancel": {"description": "Cancels a Rollout in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:cancel", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the Rollout. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelRolloutRequest"}, "response": {"$ref": "CancelRolloutResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new Rollout in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.create", "parameterOrder": ["parent"], "parameters": {"overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "location": "query", "repeated": true, "type": "string"}, "parent": {"description": "Required. The parent collection in which the `Rollout` must be created. The format is `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "rolloutId": {"description": "Required. ID of the `Rollout`.", "location": "query", "type": "string"}, "startingPhaseId": {"description": "Optional. The starting phase ID for the `Rollout`. If empty the `Rollout` will start at the first phase.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/rollouts", "request": {"$ref": "Rollout"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Rollout.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `Rollout`. Format must be `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}/rollouts/{rollout_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Rollout"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "ignoreJob": {"description": "Ignores the specified Job in a Rollout.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:ignoreJob", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.ignoreJob", "parameterOrder": ["rollout"], "parameters": {"rollout": {"description": "Required. Name of the Rollout. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+rollout}:ignoreJob", "request": {"$ref": "IgnoreJobRequest"}, "response": {"$ref": "IgnoreJobResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Rollouts in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter rollouts to be returned. See https://google.aip.dev/160 for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of `Rollout` objects to return. The service may return fewer than this value. If unspecified, at most 50 `Rollout` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListRollouts` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The `Release` which owns this collection of `Rollout` objects.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/rollouts", "response": {"$ref": "ListRolloutsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "retryJob": {"description": "Retries the specified Job in a Rollout.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}:retryJob", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.retryJob", "parameterOrder": ["rollout"], "parameters": {"rollout": {"description": "Required. Name of the Rollout. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+rollout}:retryJob", "request": {"$ref": "RetryJobRequest"}, "response": {"$ref": "RetryJobResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"jobRuns": {"methods": {"get": {"description": "Gets details of a single JobRun.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}/jobRuns/{jobRunsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.jobRuns.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `JobRun`. Format must be `projects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{release_name}/rollouts/{rollout_name}/jobRuns/{job_run_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+/jobRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "JobRun"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists JobRuns in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}/jobRuns", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.jobRuns.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter results to be returned. See https://google.aip.dev/160 for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of `JobRun` objects to return. The service may return fewer than this value. If unspecified, at most 50 `JobRun` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListJobRuns` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The `Rollout` which owns this collection of `JobRun` objects.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/jobRuns", "response": {"$ref": "ListJobRunsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "terminate": {"description": "Terminates a Job Run in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deliveryPipelines/{deliveryPipelinesId}/releases/{releasesId}/rollouts/{rolloutsId}/jobRuns/{jobRunsId}:terminate", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deliveryPipelines.releases.rollouts.jobRuns.terminate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `JobRun`. Format must be `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}/jobRuns/{jobRun}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deliveryPipelines/[^/]+/releases/[^/]+/rollouts/[^/]+/jobRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:terminate", "request": {"$ref": "TerminateJobRunRequest"}, "response": {"$ref": "TerminateJobRunResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}, "deployPolicies": {"methods": {"create": {"description": "Creates a new DeployPolicy in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deployPolicies.create", "parameterOrder": ["parent"], "parameters": {"deployPolicyId": {"description": "Required. ID of the `DeployPolicy`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent collection in which the `DeployPolicy` must be created. The format is `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/deployPolicies", "request": {"$ref": "DeployPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single DeployPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}", "httpMethod": "DELETE", "id": "clouddeploy.projects.locations.deployPolicies.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, then deleting an already deleted or non-existing `DeployPolicy` will succeed.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `DeployPolicy` to delete. The format is `projects/{project_id}/locations/{location_name}/deployPolicies/{deploy_policy_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single DeployPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deployPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `DeployPolicy`. Format must be `projects/{project_id}/locations/{location_name}/deployPolicies/{deploy_policy_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DeployPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}:getIamPolicy", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deployPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists DeployPolicies in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies", "httpMethod": "GET", "id": "clouddeploy.projects.locations.deployPolicies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter deploy policies to be returned. See https://google.aip.dev/160 for more details. All fields can be used in the filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of deploy policies to return. The service may return fewer than this value. If unspecified, at most 50 deploy policies will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDeployPolicies` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of deploy policies. Format must be `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/deployPolicies", "response": {"$ref": "ListDeployPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single DeployPolicy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}", "httpMethod": "PATCH", "id": "clouddeploy.projects.locations.deployPolicies.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, updating a `DeployPolicy` that does not exist will result in the creation of a new `DeployPolicy`.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. Name of the `DeployPolicy`. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`. The `deployPolicy` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployPolicies/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the `DeployPolicy` resource. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it's in the mask. If the user doesn't provide a mask then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "DeployPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployPolicies/{deployPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "clouddeploy.projects.locations.deployPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "clouddeploy.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "clouddeploy.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "clouddeploy.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "targets": {"methods": {"create": {"description": "Creates a new Target in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets", "httpMethod": "POST", "id": "clouddeploy.projects.locations.targets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent collection in which the `Target` must be created. The format is `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "targetId": {"description": "Required. ID of the `Target`.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/targets", "request": {"$ref": "Target"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Target.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}", "httpMethod": "DELETE", "id": "clouddeploy.projects.locations.targets.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, then deleting an already deleted or non-existing `Target` will succeed.", "location": "query", "type": "boolean"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `Target` to delete. The format is `projects/{project_id}/locations/{location_name}/targets/{target_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/targets/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validate the request and preview the review, but do not actually post it.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Target.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}", "httpMethod": "GET", "id": "clouddeploy.projects.locations.targets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the `Target`. Format must be `projects/{project_id}/locations/{location_name}/targets/{target_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/targets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Target"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}:getIamPolicy", "httpMethod": "GET", "id": "clouddeploy.projects.locations.targets.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/targets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Targets in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets", "httpMethod": "GET", "id": "clouddeploy.projects.locations.targets.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter targets to be returned. See https://google.aip.dev/160 for more details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of `Target` objects to return. The service may return fewer than this value. If unspecified, at most 50 `Target` objects will be returned. The maximum value is 1000; values above 1000 will be set to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListTargets` call. Provide this to retrieve the subsequent page. When paginating, all other provided parameters match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of targets. Format must be `projects/{project_id}/locations/{location_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/targets", "response": {"$ref": "ListTargetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Target.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}", "httpMethod": "PATCH", "id": "clouddeploy.projects.locations.targets.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, updating a `Target` that does not exist will result in the creation of a new `Target`.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. Name of the `Target`. Format is `projects/{project}/locations/{location}/targets/{target}`. The `target` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/targets/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server knows to ignore the request if it has already been completed. The server guarantees that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the `Target` resource. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it's in the mask. If the user doesn't provide a mask then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with an expected result, but no actual change is made.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Target"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}:setIamPolicy", "httpMethod": "POST", "id": "clouddeploy.projects.locations.targets.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/targets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/targets/{targetsId}:testIamPermissions", "httpMethod": "POST", "id": "clouddeploy.projects.locations.targets.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/targets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250416", "rootUrl": "https://clouddeploy.googleapis.com/", "schemas": {"AbandonReleaseRequest": {"description": "The request object used by `AbandonRelease`.", "id": "AbandonReleaseRequest", "properties": {}, "type": "object"}, "AbandonReleaseResponse": {"description": "The response object for `AbandonRelease`.", "id": "AbandonReleaseResponse", "properties": {}, "type": "object"}, "AdvanceChildRolloutJob": {"description": "An advanceChildRollout Job.", "id": "AdvanceChildRolloutJob", "properties": {}, "type": "object"}, "AdvanceChildRolloutJobRun": {"description": "AdvanceChildRolloutJobRun contains information specific to a advanceChildRollout `JobRun`.", "id": "AdvanceChildRolloutJobRun", "properties": {"rollout": {"description": "Output only. Name of the `ChildRollout`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "readOnly": true, "type": "string"}, "rolloutPhaseId": {"description": "Output only. the ID of the ChildRollout's Phase.", "readOnly": true, "type": "string"}}, "type": "object"}, "AdvanceRolloutOperation": {"description": "Contains the information of an automated advance-rollout operation.", "id": "AdvanceRolloutOperation", "properties": {"destinationPhase": {"description": "Output only. The phase the rollout will be advanced to.", "readOnly": true, "type": "string"}, "rollout": {"description": "Output only. The name of the rollout that initiates the `AutomationRun`.", "readOnly": true, "type": "string"}, "sourcePhase": {"description": "Output only. The phase of a deployment that initiated the operation.", "readOnly": true, "type": "string"}, "wait": {"description": "Output only. How long the operation will be paused.", "format": "google-duration", "readOnly": true, "type": "string"}}, "type": "object"}, "AdvanceRolloutRequest": {"description": "The request object used by `AdvanceRollout`.", "id": "AdvanceRolloutRequest", "properties": {"overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "items": {"type": "string"}, "type": "array"}, "phaseId": {"description": "Required. The phase ID to advance the `Rollout` to.", "type": "string"}}, "type": "object"}, "AdvanceRolloutResponse": {"description": "The response object from `AdvanceRollout`.", "id": "AdvanceRolloutResponse", "properties": {}, "type": "object"}, "AdvanceRolloutRule": {"description": "The `AdvanceRollout` automation rule will automatically advance a successful Rollout to the next phase.", "id": "AdvanceRolloutRule", "properties": {"condition": {"$ref": "AutomationRuleCondition", "description": "Output only. Information around the state of the Automation rule.", "readOnly": true}, "id": {"description": "Required. ID of the rule. This id must be unique in the `Automation` resource to which this rule belongs. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "type": "string"}, "sourcePhases": {"description": "Optional. Proceeds only after phase name matched any one in the list. This value must consist of lower-case letters, numbers, and hyphens, start with a letter and end with a letter or a number, and have a max length of 63 characters. In other words, it must match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "items": {"type": "string"}, "type": "array"}, "wait": {"description": "Optional. How long to wait after a rollout is finished.", "format": "google-duration", "type": "string"}}, "type": "object"}, "AnthosCluster": {"description": "Information specifying an Anthos Cluster.", "id": "AnthosCluster", "properties": {"membership": {"description": "Optional. Membership of the GKE Hub-registered cluster to which to apply the Skaffold configuration. Format is `projects/{project}/locations/{location}/memberships/{membership_name}`.", "type": "string"}}, "type": "object"}, "ApproveRolloutRequest": {"description": "The request object used by `ApproveRollout`.", "id": "ApproveRolloutRequest", "properties": {"approved": {"description": "Required. True = approve; false = reject", "type": "boolean"}, "overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ApproveRolloutResponse": {"description": "The response object from `ApproveRollout`.", "id": "ApproveRolloutResponse", "properties": {}, "type": "object"}, "AssociatedEntities": {"description": "Information about entities associated with a `Target`.", "id": "AssociatedEntities", "properties": {"anthosClusters": {"description": "Optional. Information specifying Anthos clusters as associated entities.", "items": {"$ref": "AnthosCluster"}, "type": "array"}, "gkeClusters": {"description": "Optional. Information specifying GKE clusters as associated entities.", "items": {"$ref": "GkeCluster"}, "type": "array"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Automation": {"description": "An `Automation` resource in the Cloud Deploy API. An `Automation` enables the automation of manually driven actions for a Delivery Pipeline, which includes Release promotion among Targets, Rollout repair and Rollout deployment strategy advancement. The intention of Automation is to reduce manual intervention in the continuous delivery process.", "id": "Automation", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. Annotations must meet the following constraints: * Annotations are key/value pairs. * Valid annotation keys have two segments: an optional prefix and name, separated by a slash (`/`). * The name segment is required and must be 63 characters or less, beginning and ending with an alphanumeric character (`[a-z0-9A-Z]`) with dashes (`-`), underscores (`_`), dots (`.`), and alphanumerics between. * The prefix is optional. If specified, the prefix must be a DNS subdomain: a series of DNS labels separated by dots(`.`), not longer than 253 characters in total, followed by a slash (`/`). See https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/#syntax-and-character-set for more details.", "type": "object"}, "createTime": {"description": "Output only. Time at which the automation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the `Automation`. Max length is 255 characters.", "type": "string"}, "etag": {"description": "Optional. The weak etag of the `Automation` resource. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 63 characters.", "type": "object"}, "name": {"description": "Output only. Name of the `Automation`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/{automation}`.", "readOnly": true, "type": "string"}, "rules": {"description": "Required. List of Automation rules associated with the Automation resource. Must have at least one rule and limited to 250 rules per Delivery Pipeline. Note: the order of the rules here is not the same as the order of execution.", "items": {"$ref": "AutomationRule"}, "type": "array"}, "selector": {"$ref": "AutomationResourceSelector", "description": "Required. Selected resources to which the automation will be applied."}, "serviceAccount": {"description": "Required. Email address of the user-managed IAM service account that creates Cloud Deploy release and rollout resources.", "type": "string"}, "suspended": {"description": "Optional. When Suspended, automation is deactivated from execution.", "type": "boolean"}, "uid": {"description": "Output only. Unique identifier of the `Automation`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time at which the automation was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AutomationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/automation\" Platform Log event that describes the Automation related events.", "id": "AutomationEvent", "properties": {"automation": {"description": "The name of the `AutomationRun`.", "type": "string"}, "message": {"description": "Debug message for when there is an update on the AutomationRun. Provides further details about the resource creation or state change.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "AutomationResourceSelector": {"description": "AutomationResourceSelector contains the information to select the resources to which an Automation is going to be applied.", "id": "AutomationResourceSelector", "properties": {"targets": {"description": "Optional. Contains attributes about a target.", "items": {"$ref": "TargetAttribute"}, "type": "array"}}, "type": "object"}, "AutomationRolloutMetadata": {"description": "AutomationRolloutMetadata contains Automation-related actions that were performed on a rollout.", "id": "AutomationRolloutMetadata", "properties": {"advanceAutomationRuns": {"description": "Output only. The names of the AutomationRuns initiated by an advance rollout rule.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "promoteAutomationRun": {"description": "Output only. The name of the AutomationRun initiated by a promote release rule.", "readOnly": true, "type": "string"}, "repairAutomationRuns": {"description": "Output only. The names of the AutomationRuns initiated by a repair rollout rule.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "AutomationRule": {"description": "`AutomationRule` defines the automation activities.", "id": "AutomationRule", "properties": {"advanceRolloutRule": {"$ref": "AdvanceRolloutRule", "description": "Optional. The `AdvanceRolloutRule` will automatically advance a successful Rollout."}, "promoteReleaseRule": {"$ref": "PromoteReleaseRule", "description": "Optional. `PromoteReleaseRule` will automatically promote a release from the current target to a specified target."}, "repairRolloutRule": {"$ref": "RepairRolloutRule", "description": "Optional. The `RepairRolloutRule` will automatically repair a failed rollout."}, "timedPromoteReleaseRule": {"$ref": "TimedPromoteReleaseRule", "description": "Optional. The `TimedPromoteReleaseRule` will automatically promote a release from the current target(s) to the specified target(s) on a configured schedule."}}, "type": "object"}, "AutomationRuleCondition": {"description": "`AutomationRuleCondition` contains conditions relevant to an `Automation` rule.", "id": "AutomationRuleCondition", "properties": {"targetsPresentCondition": {"$ref": "TargetsPresentCondition", "description": "Optional. Details around targets enumerated in the rule."}, "timedPromoteReleaseCondition": {"$ref": "TimedPromoteReleaseCondition", "description": "Optional. TimedPromoteReleaseCondition contains rule conditions specific to a an Automation with a timed promote release rule defined."}}, "type": "object"}, "AutomationRun": {"description": "An `AutomationRun` resource in the Cloud Deploy API. An `AutomationRun` represents an execution instance of an automation rule.", "id": "AutomationRun", "properties": {"advanceRolloutOperation": {"$ref": "AdvanceRolloutOperation", "description": "Output only. Advances a rollout to the next phase.", "readOnly": true}, "automationId": {"description": "Output only. The ID of the automation that initiated the operation.", "readOnly": true, "type": "string"}, "automationSnapshot": {"$ref": "Automation", "description": "Output only. Snapshot of the Automation taken at AutomationRun creation time.", "readOnly": true}, "createTime": {"description": "Output only. Time at which the `AutomationRun` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. The weak etag of the `AutomationRun` resource. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "expireTime": {"description": "Output only. Time the `AutomationRun` expires. An `AutomationRun` expires after 14 days from its creation date.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Name of the `AutomationRun`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRuns/{automation_run}`.", "readOnly": true, "type": "string"}, "policyViolation": {"$ref": "PolicyViolation", "description": "Output only. Contains information about what policies prevented the `AutomationRun` from proceeding.", "readOnly": true}, "promoteReleaseOperation": {"$ref": "PromoteReleaseOperation", "description": "Output only. Promotes a release to a specified 'Target'.", "readOnly": true}, "repairRolloutOperation": {"$ref": "RepairRolloutOperation", "description": "Output only. Repairs a failed 'Rollout'.", "readOnly": true}, "ruleId": {"description": "Output only. The ID of the automation rule that initiated the operation.", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Output only. Email address of the user-managed IAM service account that performs the operations against Cloud Deploy resources.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the `AutomationRun`.", "enum": ["STATE_UNSPECIFIED", "SUCCEEDED", "CANCELLED", "FAILED", "IN_PROGRESS", "PENDING", "ABORTED"], "enumDescriptions": ["The `AutomationRun` has an unspecified state.", "The `AutomationRun` has succeeded.", "The `AutomationRun` was cancelled.", "The `AutomationRun` has failed.", "The `AutomationRun` is in progress.", "The `AutomationRun` is pending.", "The `AutomationRun` was aborted."], "readOnly": true, "type": "string"}, "stateDescription": {"description": "Output only. Explains the current state of the `AutomationRun`. Present only when an explanation is needed.", "readOnly": true, "type": "string"}, "targetId": {"description": "Output only. The ID of the source target that initiates the `AutomationRun`. The value of this field is the last segment of a target name.", "readOnly": true, "type": "string"}, "timedPromoteReleaseOperation": {"$ref": "TimedPromoteReleaseOperation", "description": "Output only. Promotes a release to a specified 'Target' as defined in a Timed Promote Release rule.", "readOnly": true}, "updateTime": {"description": "Output only. Time at which the automationRun was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "waitUntilTime": {"description": "Output only. Earliest time the `AutomationRun` will attempt to resume. Wait-time is configured by `wait` in automation rule.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AutomationRunEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/automation_run\" Platform Log event that describes the AutomationRun related events.", "id": "AutomationRunEvent", "properties": {"automationId": {"description": "Identifier of the `Automation`.", "type": "string"}, "automationRun": {"description": "The name of the `AutomationRun`.", "type": "string"}, "destinationTargetId": {"description": "ID of the `Target` to which the `AutomationRun` is created.", "type": "string"}, "message": {"description": "Debug message for when there is an update on the AutomationRun. Provides further details about the resource creation or state change.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "ruleId": {"description": "Identifier of the `Automation` rule.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "BuildArtifact": {"description": "Description of an a image to use during Skaffold rendering.", "id": "BuildArtifact", "properties": {"image": {"description": "Optional. Image name in Skaffold configuration.", "type": "string"}, "tag": {"description": "Optional. Image tag to use. This will generally be the full path to an image, such as \"gcr.io/my-project/busybox:1.2.3\" or \"gcr.io/my-project/busybox@sha256:abc123\".", "type": "string"}}, "type": "object"}, "Canary": {"description": "Canary represents the canary deployment strategy.", "id": "Canary", "properties": {"canaryDeployment": {"$ref": "CanaryDeployment", "description": "Optional. Configures the progressive based deployment for a Target."}, "customCanaryDeployment": {"$ref": "CustomCanaryDeployment", "description": "Optional. Configures the progressive based deployment for a Target, but allows customizing at the phase level where a phase represents each of the percentage deployments."}, "runtimeConfig": {"$ref": "RuntimeConfig", "description": "Optional. Runtime specific configurations for the deployment strategy. The runtime configuration is used to determine how Cloud Deploy will split traffic to enable a progressive deployment."}}, "type": "object"}, "CanaryDeployment": {"description": "CanaryDeployment represents the canary deployment configuration", "id": "CanaryDeployment", "properties": {"percentages": {"description": "Required. The percentage based deployments that will occur as a part of a `Rollout`. List is expected in ascending order and each integer n is 0 <= n < 100. If the GatewayServiceMesh is configured for Kubernetes, then the range for n is 0 <= n <= 100.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "postdeploy": {"$ref": "Postdeploy", "description": "Optional. Configuration for the postdeploy job of the last phase. If this is not configured, there will be no postdeploy job for this phase."}, "predeploy": {"$ref": "Predeploy", "description": "Optional. Configuration for the predeploy job of the first phase. If this is not configured, there will be no predeploy job for this phase."}, "verify": {"description": "Optional. Whether to run verify tests after each percentage deployment via `skaffold verify`.", "type": "boolean"}}, "type": "object"}, "CancelAutomationRunRequest": {"description": "The request object used by `CancelAutomationRun`.", "id": "CancelAutomationRunRequest", "properties": {}, "type": "object"}, "CancelAutomationRunResponse": {"description": "The response object from `CancelAutomationRun`.", "id": "CancelAutomationRunResponse", "properties": {}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CancelRolloutRequest": {"description": "The request object used by `CancelRollout`.", "id": "CancelRolloutRequest", "properties": {"overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CancelRolloutResponse": {"description": "The response object from `CancelRollout`.", "id": "CancelRolloutResponse", "properties": {}, "type": "object"}, "ChildRolloutJobs": {"description": "ChildRollouts job composition", "id": "ChildRolloutJobs", "properties": {"advanceRolloutJobs": {"description": "Output only. List of AdvanceChildRolloutJobs", "items": {"$ref": "Job"}, "readOnly": true, "type": "array"}, "createRolloutJobs": {"description": "Output only. List of CreateChildRolloutJobs", "items": {"$ref": "Job"}, "readOnly": true, "type": "array"}}, "type": "object"}, "CloudRunConfig": {"description": "CloudRunConfig contains the Cloud Run runtime configuration.", "id": "CloudRunConfig", "properties": {"automaticTrafficControl": {"description": "Optional. Whether Cloud Deploy should update the traffic stanza in a Cloud Run Service on the user's behalf to facilitate traffic splitting. This is required to be true for CanaryDeployments, but optional for CustomCanaryDeployments.", "type": "boolean"}, "canaryRevisionTags": {"description": "Optional. A list of tags that are added to the canary revision while the canary phase is in progress.", "items": {"type": "string"}, "type": "array"}, "priorRevisionTags": {"description": "Optional. A list of tags that are added to the prior revision while the canary phase is in progress.", "items": {"type": "string"}, "type": "array"}, "stableRevisionTags": {"description": "Optional. A list of tags that are added to the final stable revision when the stable phase is applied.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CloudRunLocation": {"description": "Information specifying where to deploy a Cloud Run Service.", "id": "CloudRunLocation", "properties": {"location": {"description": "Required. The location for the Cloud Run Service. Format must be `projects/{project}/locations/{location}`.", "type": "string"}}, "type": "object"}, "CloudRunMetadata": {"description": "CloudRunMetadata contains information from a Cloud Run deployment.", "id": "CloudRunMetadata", "properties": {"job": {"description": "Output only. The name of the Cloud Run job that is associated with a `Rollout`. Format is `projects/{project}/locations/{location}/jobs/{job_name}`.", "readOnly": true, "type": "string"}, "revision": {"description": "Output only. The Cloud Run Revision id associated with a `Rollout`.", "readOnly": true, "type": "string"}, "service": {"description": "Output only. The name of the Cloud Run Service that is associated with a `Rollout`. Format is `projects/{project}/locations/{location}/services/{service}`.", "readOnly": true, "type": "string"}, "serviceUrls": {"description": "Output only. The Cloud Run Service urls that are associated with a `Rollout`.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "CloudRunRenderMetadata": {"description": "CloudRunRenderMetadata contains Cloud Run information associated with a `Release` render.", "id": "CloudRunRenderMetadata", "properties": {"service": {"description": "Output only. The name of the Cloud Run Service in the rendered manifest. Format is `projects/{project}/locations/{location}/services/{service}`.", "readOnly": true, "type": "string"}}, "type": "object"}, "Config": {"description": "Service-wide configuration.", "id": "Config", "properties": {"defaultSkaffoldVersion": {"description": "Default Skaffold version that is assigned when a Release is created without specifying a Skaffold version.", "type": "string"}, "name": {"description": "Name of the configuration.", "type": "string"}, "supportedVersions": {"description": "All supported versions of Skaffold.", "items": {"$ref": "SkaffoldVersion"}, "type": "array"}}, "type": "object"}, "CreateChildRolloutJob": {"description": "A createChildRollout Job.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {}, "type": "object"}, "CreateChildRolloutJobRun": {"description": "CreateChildRolloutJobRun contains information specific to a createChildRollout `JobRun`.", "id": "CreateChildRolloutJobRun", "properties": {"rollout": {"description": "Output only. Name of the `ChildRollout`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "readOnly": true, "type": "string"}, "rolloutPhaseId": {"description": "Output only. The ID of the childRollout Phase initiated by this JobRun.", "readOnly": true, "type": "string"}}, "type": "object"}, "CustomCanaryDeployment": {"description": "CustomCanaryDeployment represents the custom canary deployment configuration.", "id": "CustomCanaryDeployment", "properties": {"phaseConfigs": {"description": "Required. Configuration for each phase in the canary deployment in the order executed.", "items": {"$ref": "PhaseConfig"}, "type": "array"}}, "type": "object"}, "CustomMetadata": {"description": "CustomMetadata contains information from a user-defined operation.", "id": "CustomMetadata", "properties": {"values": {"additionalProperties": {"type": "string"}, "description": "Output only. Key-value pairs provided by the user-defined operation.", "readOnly": true, "type": "object"}}, "type": "object"}, "CustomTarget": {"description": "Information specifying a Custom Target.", "id": "CustomTarget", "properties": {"customTargetType": {"description": "Required. The name of the CustomTargetType. Format must be `projects/{project}/locations/{location}/customTargetTypes/{custom_target_type}`.", "type": "string"}}, "type": "object"}, "CustomTargetDeployMetadata": {"description": "CustomTargetDeployMetadata contains information from a Custom Target deploy operation.", "id": "CustomTargetDeployMetadata", "properties": {"skipMessage": {"description": "Output only. Skip message provided in the results of a custom deploy operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "CustomTargetSkaffoldActions": {"description": "CustomTargetSkaffoldActions represents the `CustomTargetType` configuration using Skaffold custom actions.", "id": "CustomTargetSkaffoldActions", "properties": {"deployAction": {"description": "Required. The Skaffold custom action responsible for deploy operations.", "type": "string"}, "includeSkaffoldModules": {"description": "Optional. List of Skaffold modules Cloud Deploy will include in the Skaffold Config as required before performing diagnose.", "items": {"$ref": "SkaffoldModules"}, "type": "array"}, "renderAction": {"description": "Optional. The Skaffold custom action responsible for render operations. If not provided then Cloud Deploy will perform the render operations via `skaffold render`.", "type": "string"}}, "type": "object"}, "CustomTargetType": {"description": "A `CustomTargetType` resource in the Cloud Deploy API. A `CustomTargetType` defines a type of custom target that can be referenced in a `Target` in order to facilitate deploying to other systems besides the supported runtimes.", "id": "CustomTargetType", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.", "type": "object"}, "createTime": {"description": "Output only. Time at which the `CustomTargetType` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customActions": {"$ref": "CustomTargetSkaffoldActions", "description": "Optional. Configures render and deploy for the `CustomTargetType` using Skaffold custom actions."}, "customTargetTypeId": {"description": "Output only. Resource id of the `CustomTargetType`.", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the `CustomTargetType`. Max length is 255 characters.", "type": "string"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.", "type": "object"}, "name": {"description": "Identifier. Name of the `CustomTargetType`. Format is `projects/{project}/locations/{location}/customTargetTypes/{customTargetType}`. The `customTargetType` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "type": "string"}, "uid": {"description": "Output only. Unique identifier of the `CustomTargetType`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Most recent time at which the `CustomTargetType` was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CustomTargetTypeNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/customtargettype_notification\" Platform Log event that describes the failure to send a custom target type status change Pub/Sub notification.", "id": "CustomTargetTypeNotificationEvent", "properties": {"customTargetType": {"description": "The name of the `CustomTargetType`.", "type": "string"}, "customTargetTypeUid": {"description": "Unique identifier of the `CustomTargetType`.", "type": "string"}, "message": {"description": "Debug message for when a notification fails to send.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "DefaultPool": {"description": "Execution using the default Cloud Build pool.", "id": "DefaultPool", "properties": {"artifactStorage": {"description": "Optional. Cloud Storage location where execution outputs should be stored. This can either be a bucket (\"gs://my-bucket\") or a path within a bucket (\"gs://my-bucket/my-dir\"). If unspecified, a default bucket located in the same region will be used.", "type": "string"}, "serviceAccount": {"description": "Optional. Google service account to use for execution. If unspecified, the project execution service account (-<EMAIL>) will be used.", "type": "string"}}, "type": "object"}, "DeliveryPipeline": {"description": "A `DeliveryPipeline` resource in the Cloud Deploy API. A `DeliveryPipeline` defines a pipeline through which a Skaffold configuration can progress.", "id": "DeliveryPipeline", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy.", "type": "object"}, "condition": {"$ref": "PipelineCondition", "description": "Output only. Information around the state of the Delivery Pipeline.", "readOnly": true}, "createTime": {"description": "Output only. Time at which the pipeline was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the `DeliveryPipeline`. Max length is 255 characters.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.", "type": "object"}, "name": {"description": "Identifier. Name of the `DeliveryPipeline`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}`. The `deliveryPipeline` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "type": "string"}, "serialPipeline": {"$ref": "Ser<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. SerialPipeline defines a sequential set of stages for a `DeliveryPipeline`."}, "suspended": {"description": "Optional. When suspended, no new releases or rollouts can be created, but in-progress ones will complete.", "type": "boolean"}, "uid": {"description": "Output only. Unique identifier of the `DeliveryPipeline`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Most recent time at which the pipeline was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DeliveryPipelineAttribute": {"description": "Contains criteria for selecting DeliveryPipelines.", "id": "DeliveryPipelineAttribute", "properties": {"id": {"description": "Optional. ID of the `DeliveryPipeline`. The value of this field could be one of the following: * The last segment of a pipeline name * \"*\", all delivery pipelines in a location", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "DeliveryPipeline labels.", "type": "object"}}, "type": "object"}, "DeliveryPipelineNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/deliverypipeline_notification\" Platform Log event that describes the failure to send delivery pipeline status change Pub/Sub notification.", "id": "DeliveryPipelineNotificationEvent", "properties": {"deliveryPipeline": {"description": "The name of the `Delivery Pipeline`.", "type": "string"}, "message": {"description": "Debug message for when a notification fails to send.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "DeployArtifact": {"description": "The artifacts produced by a deploy operation.", "id": "DeployArtifact", "properties": {"artifactUri": {"description": "Output only. URI of a directory containing the artifacts. All paths are relative to this location.", "readOnly": true, "type": "string"}, "manifestPaths": {"description": "Output only. File paths of the manifests applied during the deploy operation relative to the URI.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "DeployJob": {"description": "A deploy Job.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {}, "type": "object"}, "DeployJobRun": {"description": "DeployJobRun contains information specific to a deploy `JobRun`.", "id": "DeployJobRun", "properties": {"artifact": {"$ref": "DeployArtifact", "description": "Output only. The artifact of a deploy job run, if available.", "readOnly": true}, "build": {"description": "Output only. The resource name of the Cloud Build `Build` object that is used to deploy. Format is `projects/{project}/locations/{location}/builds/{build}`.", "readOnly": true, "type": "string"}, "failureCause": {"description": "Output only. The reason the deploy failed. This will always be unspecified while the deploy is in progress or if it succeeded.", "enum": ["FAILURE_CAUSE_UNSPECIFIED", "CLOUD_BUILD_UNAVAILABLE", "EXECUTION_FAILED", "DEADLINE_EXCEEDED", "MISSING_RESOURCES_FOR_CANARY", "CLOUD_BUILD_REQUEST_FAILED", "DEPLOY_FEATURE_NOT_SUPPORTED"], "enumDescriptions": ["No reason for failure is specified.", "Cloud Build is not available, either because it is not enabled or because Cloud Deploy has insufficient permissions. See [Required permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).", "The deploy operation did not complete successfully; check Cloud Build logs.", "The deploy job run did not complete within the allotted time.", "There were missing resources in the runtime environment required for a canary deployment. Check the Cloud Build logs for more information.", "Cloud Build failed to fulfill Cloud Deploy's request. See failure_message for additional details.", "The deploy operation had a feature configured that is not supported."], "readOnly": true, "type": "string"}, "failureMessage": {"description": "Output only. Additional information about the deploy failure, if available.", "readOnly": true, "type": "string"}, "metadata": {"$ref": "DeployJobRunMetadata", "description": "Output only. Metadata containing information about the deploy job run.", "readOnly": true}}, "type": "object"}, "DeployJobRunMetadata": {"description": "DeployJobRunMetadata surfaces information associated with a `DeployJobRun` to the user.", "id": "DeployJobRunMetadata", "properties": {"cloudRun": {"$ref": "CloudRunMetadata", "description": "Output only. The name of the Cloud Run Service that is associated with a `DeployJobRun`.", "readOnly": true}, "custom": {"$ref": "CustomMetadata", "description": "Output only. Custom metadata provided by user-defined deploy operation.", "readOnly": true}, "customTarget": {"$ref": "CustomTargetDeployMetadata", "description": "Output only. Custom Target metadata associated with a `DeployJobRun`.", "readOnly": true}}, "type": "object"}, "DeployParameters": {"description": "DeployParameters contains deploy parameters information.", "id": "DeployParameters", "properties": {"matchTargetLabels": {"additionalProperties": {"type": "string"}, "description": "Optional. Deploy parameters are applied to targets with match labels. If unspecified, deploy parameters are applied to all targets (including child targets of a multi-target).", "type": "object"}, "values": {"additionalProperties": {"type": "string"}, "description": "Required. Values are deploy parameters in key-value pairs.", "type": "object"}}, "type": "object"}, "DeployPolicy": {"description": "A `DeployPolicy` resource in the Cloud Deploy API. A `DeployPolicy` inhibits manual or automation-driven actions within a Delivery Pipeline or Target.", "id": "DeployPolicy", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. Annotations must meet the following constraints: * Annotations are key/value pairs. * Valid annotation keys have two segments: an optional prefix and name, separated by a slash (`/`). * The name segment is required and must be 63 characters or less, beginning and ending with an alphanumeric character (`[a-z0-9A-Z]`) with dashes (`-`), underscores (`_`), dots (`.`), and alphanumerics between. * The prefix is optional. If specified, the prefix must be a DNS subdomain: a series of DNS labels separated by dots(`.`), not longer than 253 characters in total, followed by a slash (`/`). See https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/#syntax-and-character-set for more details.", "type": "object"}, "createTime": {"description": "Output only. Time at which the deploy policy was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the `DeployPolicy`. Max length is 255 characters.", "type": "string"}, "etag": {"description": "The weak etag of the `DeployPolicy` resource. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.", "type": "object"}, "name": {"description": "Output only. Name of the `DeployPolicy`. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`. The `deployPolicy` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "readOnly": true, "type": "string"}, "rules": {"description": "Required. Rules to apply. At least one rule must be present.", "items": {"$ref": "PolicyRule"}, "type": "array"}, "selectors": {"description": "Required. Selected resources to which the policy will be applied. At least one selector is required. If one selector matches the resource the policy applies. For example, if there are two selectors and the action being attempted matches one of them, the policy will apply to that action.", "items": {"$ref": "DeployPolicyResourceSelector"}, "type": "array"}, "suspended": {"description": "Optional. When suspended, the policy will not prevent actions from occurring, even if the action violates the policy.", "type": "boolean"}, "uid": {"description": "Output only. Unique identifier of the `DeployPolicy`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Most recent time at which the deploy policy was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DeployPolicyEvaluationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/deploypolicy_evaluation\" Platform Log event that describes the deploy policy evaluation event.", "id": "DeployPolicyEvaluationEvent", "properties": {"allowed": {"description": "Whether the request is allowed. Allowed is set as true if: (1) the request complies with the policy; or (2) the request doesn't comply with the policy but the policy was overridden; or (3) the request doesn't comply with the policy but the policy was suspended", "type": "boolean"}, "deliveryPipeline": {"description": "The name of the `Delivery Pipeline`.", "type": "string"}, "deployPolicy": {"description": "The name of the `DeployPolicy`.", "type": "string"}, "deployPolicyUid": {"description": "Unique identifier of the `DeployPolicy`.", "type": "string"}, "invoker": {"description": "What invoked the action (e.g. a user or automation).", "enum": ["INVOKER_UNSPECIFIED", "USER", "DEPLOY_AUTOMATION"], "enumDescriptions": ["Unspecified.", "The action is user-driven. For example, creating a rollout manually via a gcloud create command.", "Automated action by Cloud Deploy."], "type": "string"}, "message": {"description": "Debug message for when a deploy policy event occurs.", "type": "string"}, "overrides": {"description": "Things that could have overridden the policy verdict. Overrides together with verdict decide whether the request is allowed.", "items": {"enum": ["POLICY_VERDICT_OVERRIDE_UNSPECIFIED", "POLICY_OVERRIDDEN", "POLICY_SUSPENDED"], "enumDescriptions": ["This should never happen.", "The policy was overridden.", "The policy was suspended."], "type": "string"}, "type": "array"}, "pipelineUid": {"description": "Unique identifier of the `Delivery Pipeline`.", "type": "string"}, "rule": {"description": "Rule id.", "type": "string"}, "ruleType": {"description": "Rule type (e.g. Restrict Rollouts).", "type": "string"}, "target": {"description": "The name of the `Target`. This is an optional field, as a `Target` may not always be applicable to a policy.", "type": "string"}, "targetUid": {"description": "Unique identifier of the `Target`. This is an optional field, as a `Target` may not always be applicable to a policy.", "type": "string"}, "verdict": {"description": "The policy verdict of the request.", "enum": ["POLICY_VERDICT_UNSPECIFIED", "ALLOWED_BY_POLICY", "DENIED_BY_POLICY"], "enumDescriptions": ["This should never happen.", "Allowed by policy. This enum value is not currently used but may be used in the future. Currently logs are only generated when a request is denied by policy.", "Denied by policy."], "type": "string"}}, "type": "object"}, "DeployPolicyNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/deploypolicy_notification\". Platform Log event that describes the failure to send a pub/sub notification when there is a DeployPolicy status change.", "id": "DeployPolicyNotificationEvent", "properties": {"deployPolicy": {"description": "The name of the `DeployPolicy`.", "type": "string"}, "deployPolicyUid": {"description": "Unique identifier of the deploy policy.", "type": "string"}, "message": {"description": "Debug message for when a deploy policy fails to send a pub/sub notification.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "DeployPolicyResourceSelector": {"description": "Contains information on the resources to select for a deploy policy. Attributes provided must all match the resource in order for policy restrictions to apply. For example, if delivery pipelines attributes given are an id \"prod\" and labels \"foo: bar\", a delivery pipeline resource must match both that id and have that label in order to be subject to the policy.", "id": "DeployPolicyResourceSelector", "properties": {"deliveryPipeline": {"$ref": "DeliveryPipelineAttribute", "description": "Optional. Contains attributes about a delivery pipeline."}, "target": {"$ref": "TargetAttribute", "description": "Optional. Contains attributes about a target."}}, "type": "object"}, "DeploymentJobs": {"description": "Deployment job composition.", "id": "DeploymentJobs", "properties": {"deployJob": {"$ref": "Job", "description": "Output only. The deploy Job. This is the deploy job in the phase.", "readOnly": true}, "postdeployJob": {"$ref": "Job", "description": "Output only. The postdeploy Job, which is the last job on the phase.", "readOnly": true}, "predeployJob": {"$ref": "Job", "description": "Output only. The predeploy Job, which is the first job on the phase.", "readOnly": true}, "verifyJob": {"$ref": "Job", "description": "Output only. The verify Job. Runs after a deploy if the deploy succeeds.", "readOnly": true}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ExecutionConfig": {"description": "Configuration of the environment to use when calling Skaffold.", "id": "ExecutionConfig", "properties": {"artifactStorage": {"description": "Optional. Cloud Storage location in which to store execution outputs. This can either be a bucket (\"gs://my-bucket\") or a path within a bucket (\"gs://my-bucket/my-dir\"). If unspecified, a default bucket located in the same region will be used.", "type": "string"}, "defaultPool": {"$ref": "DefaultPool", "description": "Optional. Use default Cloud Build pool."}, "executionTimeout": {"description": "Optional. Execution timeout for a Cloud Build Execution. This must be between 10m and 24h in seconds format. If unspecified, a default timeout of 1h is used.", "format": "google-duration", "type": "string"}, "privatePool": {"$ref": "PrivatePool", "description": "Optional. Use private Cloud Build pool."}, "serviceAccount": {"description": "Optional. Google service account to use for execution. If unspecified, the project execution service account (-<EMAIL>) is used.", "type": "string"}, "usages": {"description": "Required. Usages when this configuration should be applied.", "items": {"enum": ["EXECUTION_ENVIRONMENT_USAGE_UNSPECIFIED", "RENDER", "DEPLOY", "VERIFY", "PREDEPLOY", "POSTDEPLOY"], "enumDescriptions": ["Default value. This value is unused.", "Use for rendering.", "Use for deploying and deployment hooks.", "Use for deployment verification.", "Use for predeploy job execution.", "Use for postdeploy job execution."], "type": "string"}, "type": "array"}, "verbose": {"description": "Optional. If true, additional logging will be enabled when running builds in this execution environment.", "type": "boolean"}, "workerPool": {"description": "Optional. The resource name of the `WorkerPool`, with the format `projects/{project}/locations/{location}/workerPools/{worker_pool}`. If this optional field is unspecified, the default Cloud Build pool will be used.", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GatewayServiceMesh": {"description": "Information about the Kubernetes Gateway API service mesh configuration.", "id": "GatewayServiceMesh", "properties": {"deployment": {"description": "Required. Name of the Kubernetes Deployment whose traffic is managed by the specified HTTPRoute and Service.", "type": "string"}, "httpRoute": {"description": "Required. Name of the Gateway API HTTPRoute.", "type": "string"}, "podSelectorLabel": {"description": "Optional. The label to use when selecting Pods for the Deployment and Service resources. This label must already be present in both resources.", "type": "string"}, "routeDestinations": {"$ref": "RouteDestinations", "description": "Optional. Route destinations allow configuring the Gateway API HTTPRoute to be deployed to additional clusters. This option is available for multi-cluster service mesh set ups that require the route to exist in the clusters that call the service. If unspecified, the HTTPRoute will only be deployed to the Target cluster."}, "routeUpdateWaitTime": {"description": "Optional. The time to wait for route updates to propagate. The maximum configurable time is 3 hours, in seconds format. If unspecified, there is no wait time.", "format": "google-duration", "type": "string"}, "service": {"description": "Required. Name of the Kubernetes Service.", "type": "string"}, "stableCutbackDuration": {"description": "Optional. The amount of time to migrate traffic back from the canary Service to the original Service during the stable phase deployment. If specified, must be between 15s and 3600s. If unspecified, there is no cutback time.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GkeCluster": {"description": "Information specifying a GKE Cluster.", "id": "GkeCluster", "properties": {"cluster": {"description": "Optional. Information specifying a GKE Cluster. Format is `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`.", "type": "string"}, "dnsEndpoint": {"description": "Optional. If set, the cluster will be accessed using the DNS endpoint. Note that both `dns_endpoint` and `internal_ip` cannot be set to true.", "type": "boolean"}, "internalIp": {"description": "Optional. If true, `cluster` is accessed using the private IP address of the control plane endpoint. Otherwise, the default IP address of the control plane endpoint is used. The default IP address is the private IP address for clusters with private control-plane endpoints and the public IP address otherwise. Only specify this option when `cluster` is a [private GKE cluster](https://cloud.google.com/kubernetes-engine/docs/concepts/private-cluster-concept). Note that `internal_ip` and `dns_endpoint` cannot both be set to true.", "type": "boolean"}, "proxyUrl": {"description": "Optional. If set, used to configure a [proxy](https://kubernetes.io/docs/concepts/configuration/organize-cluster-access-kubeconfig/#proxy) to the Kubernetes server.", "type": "string"}}, "type": "object"}, "IgnoreJobRequest": {"description": "The request object used by `IgnoreJob`.", "id": "IgnoreJobRequest", "properties": {"jobId": {"description": "Required. The job ID for the Job to ignore.", "type": "string"}, "overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "items": {"type": "string"}, "type": "array"}, "phaseId": {"description": "Required. The phase ID the Job to ignore belongs to.", "type": "string"}}, "type": "object"}, "IgnoreJobResponse": {"description": "The response object from `IgnoreJob`.", "id": "IgnoreJobResponse", "properties": {}, "type": "object"}, "Job": {"description": "Job represents an operation for a `Rollout`.", "id": "Job", "properties": {"advanceChildRolloutJob": {"$ref": "AdvanceChildRolloutJob", "description": "Output only. An advanceChildRollout Job.", "readOnly": true}, "createChildRolloutJob": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Output only. A createChildRollout Job.", "readOnly": true}, "deployJob": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Output only. A deploy Job.", "readOnly": true}, "id": {"description": "Output only. The ID of the Job.", "readOnly": true, "type": "string"}, "jobRun": {"description": "Output only. The name of the `<PERSON><PERSON><PERSON>` responsible for the most recent invocation of this Job.", "readOnly": true, "type": "string"}, "postdeployJob": {"$ref": "PostdeployJob", "description": "Output only. A postdeploy Job.", "readOnly": true}, "predeployJob": {"$ref": "PredeployJob", "description": "Output only. A predeploy Job.", "readOnly": true}, "skipMessage": {"description": "Output only. Additional information on why the Job was skipped, if available.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the Job.", "enum": ["STATE_UNSPECIFIED", "PENDING", "DISABLED", "IN_PROGRESS", "SUCCEEDED", "FAILED", "ABORTED", "SKIPPED", "IGNORED"], "enumDescriptions": ["The Job has an unspecified state.", "The Job is waiting for an earlier Phase(s) or Job(s) to complete.", "The <PERSON> is disabled.", "The Job is in progress.", "The <PERSON> succeeded.", "The Job failed.", "The Job was aborted.", "The Job was skipped.", "The Job was ignored."], "readOnly": true, "type": "string"}, "verifyJob": {"$ref": "VerifyJob", "description": "Output only. A verify Job.", "readOnly": true}}, "type": "object"}, "JobRun": {"description": "A `JobRun` resource in the Cloud Deploy API. A `JobRun` contains information of a single `Rollout` job evaluation.", "id": "JobRun", "properties": {"advanceChildRolloutJobRun": {"$ref": "AdvanceChildRolloutJobRun", "description": "Output only. Information specific to an advanceChildRollout `JobRun`", "readOnly": true}, "createChildRolloutJobRun": {"$ref": "CreateChildRolloutJobRun", "description": "Output only. Information specific to a createChildRollout `JobRun`.", "readOnly": true}, "createTime": {"description": "Output only. Time at which the `JobRun` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deployJobRun": {"$ref": "DeployJobRun", "description": "Output only. Information specific to a deploy `JobRun`.", "readOnly": true}, "endTime": {"description": "Output only. Time at which the `JobRun` ended.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "readOnly": true, "type": "string"}, "jobId": {"description": "Output only. ID of the `Rollout` job this `JobRun` corresponds to.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Name of the `JobRun`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{releases}/rollouts/{rollouts}/jobRuns/{uuid}`.", "readOnly": true, "type": "string"}, "phaseId": {"description": "Output only. ID of the `Rollout` phase this `JobRun` belongs in.", "readOnly": true, "type": "string"}, "postdeployJobRun": {"$ref": "PostdeployJobRun", "description": "Output only. Information specific to a postdeploy `JobRun`.", "readOnly": true}, "predeployJobRun": {"$ref": "PredeployJobRun", "description": "Output only. Information specific to a predeploy `JobRun`.", "readOnly": true}, "startTime": {"description": "Output only. Time at which the `JobRun` was started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the `JobRun`.", "enum": ["STATE_UNSPECIFIED", "IN_PROGRESS", "SUCCEEDED", "FAILED", "TERMINATING", "TERMINATED"], "enumDescriptions": ["The `JobRun` has an unspecified state.", "The `JobRun` is in progress.", "The `<PERSON><PERSON><PERSON>` has succeeded.", "The `JobRun` has failed.", "The `JobRun` is terminating.", "The `JobRun` was terminated."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. Unique identifier of the `JobRun`.", "readOnly": true, "type": "string"}, "verifyJobRun": {"$ref": "VerifyJobRun", "description": "Output only. Information specific to a verify `JobRun`.", "readOnly": true}}, "type": "object"}, "JobRunNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/jobrun_notification\" Platform Log event that describes the failure to send JobRun resource update Pub/Sub notification.", "id": "JobRunNotificationEvent", "properties": {"jobRun": {"description": "The name of the `<PERSON><PERSON><PERSON>`.", "type": "string"}, "message": {"description": "Debug message for when a notification fails to send.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "release": {"description": "The name of the `Release`.", "type": "string"}, "releaseUid": {"description": "Unique identifier of the `Release`.", "type": "string"}, "rollout": {"description": "The name of the `Rollout`.", "type": "string"}, "rolloutUid": {"description": "Unique identifier of the `Rollout`.", "type": "string"}, "targetId": {"description": "ID of the `Target`.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "KubernetesConfig": {"description": "KubernetesConfig contains the Kubernetes runtime configuration.", "id": "KubernetesConfig", "properties": {"gatewayServiceMesh": {"$ref": "GatewayServiceMesh", "description": "Optional. Kubernetes Gateway API service mesh configuration."}, "serviceNetworking": {"$ref": "ServiceNetworking", "description": "Optional. Kubernetes Service networking configuration."}}, "type": "object"}, "ListAutomationRunsResponse": {"description": "The response object from `ListAutomationRuns`.", "id": "ListAutomationRunsResponse", "properties": {"automationRuns": {"description": "The `AutomationRuns` objects.", "items": {"$ref": "AutomationRun"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListAutomationsResponse": {"description": "The response object from `ListAutomations`.", "id": "ListAutomationsResponse", "properties": {"automations": {"description": "The `Automation` objects.", "items": {"$ref": "Automation"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListCustomTargetTypesResponse": {"description": "The response object from `ListCustomTargetTypes.`", "id": "ListCustomTargetTypesResponse", "properties": {"customTargetTypes": {"description": "The `CustomTargetType` objects.", "items": {"$ref": "CustomTargetType"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDeliveryPipelinesResponse": {"description": "The response object from `ListDeliveryPipelines`.", "id": "ListDeliveryPipelinesResponse", "properties": {"deliveryPipelines": {"description": "The `DeliveryPipeline` objects.", "items": {"$ref": "DeliveryPipeline"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDeployPoliciesResponse": {"description": "The response object from `ListDeployPolicies`.", "id": "ListDeployPoliciesResponse", "properties": {"deployPolicies": {"description": "The `DeployPolicy` objects.", "items": {"$ref": "DeployPolicy"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListJobRunsResponse": {"description": "ListJobRunsResponse is the response object returned by `ListJobRuns`.", "id": "ListJobRunsResponse", "properties": {"jobRuns": {"description": "The `JobRun` objects.", "items": {"$ref": "JobRun"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListReleasesResponse": {"description": "The response object from `ListReleases`.", "id": "ListReleasesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "releases": {"description": "The `Release` objects.", "items": {"$ref": "Release"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRolloutsResponse": {"description": "ListRolloutsResponse is the response object returned by `ListRollouts`.", "id": "ListRolloutsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "rollouts": {"description": "The `Rollout` objects.", "items": {"$ref": "Rollout"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTargetsResponse": {"description": "The response object from `ListTargets`.", "id": "ListTargetsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "targets": {"description": "The `Target` objects.", "items": {"$ref": "Target"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "Metadata": {"description": "Metadata includes information associated with a `Rollout`.", "id": "<PERSON><PERSON><PERSON>", "properties": {"automation": {"$ref": "AutomationRolloutMetadata", "description": "Output only. AutomationRolloutMetadata contains the information about the interactions between Automation service and this rollout.", "readOnly": true}, "cloudRun": {"$ref": "CloudRunMetadata", "description": "Output only. The name of the Cloud Run Service that is associated with a `Rollout`.", "readOnly": true}, "custom": {"$ref": "CustomMetadata", "description": "Output only. Custom metadata provided by user-defined `Rollout` operations.", "readOnly": true}}, "type": "object"}, "MultiTarget": {"description": "Information specifying a multiTarget.", "id": "MultiTarget", "properties": {"targetIds": {"description": "Required. The target_ids of this multiTarget.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "OneTimeWindow": {"description": "One-time window within which actions are restricted. For example, blocking actions over New Year's Eve from December 31st at 5pm to January 1st at 9am.", "id": "OneTimeWindow", "properties": {"endDate": {"$ref": "Date", "description": "Required. End date."}, "endTime": {"$ref": "TimeOfDay", "description": "Required. End time (exclusive). You may use 24:00 for the end of the day."}, "startDate": {"$ref": "Date", "description": "Required. Start date."}, "startTime": {"$ref": "TimeOfDay", "description": "Required. Start time (inclusive). Use 00:00 for the beginning of the day."}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Phase": {"description": "Phase represents a collection of jobs that are logically grouped together for a `Rollout`.", "id": "Phase", "properties": {"childRolloutJobs": {"$ref": "ChildRolloutJobs", "description": "Output only. ChildRollout job composition.", "readOnly": true}, "deploymentJobs": {"$ref": "DeploymentJobs", "description": "Output only. Deployment job composition.", "readOnly": true}, "id": {"description": "Output only. The ID of the Phase.", "readOnly": true, "type": "string"}, "skipMessage": {"description": "Output only. Additional information on why the Phase was skipped, if available.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the Phase.", "enum": ["STATE_UNSPECIFIED", "PENDING", "IN_PROGRESS", "SUCCEEDED", "FAILED", "ABORTED", "SKIPPED"], "enumDescriptions": ["The Phase has an unspecified state.", "The Phase is waiting for an earlier Phase(s) to complete.", "The Phase is in progress.", "The Phase has succeeded.", "The Phase has failed.", "The Phase was aborted.", "The Phase was skipped."], "readOnly": true, "type": "string"}}, "type": "object"}, "PhaseArtifact": {"description": "Contains the paths to the artifacts, relative to the URI, for a phase.", "id": "PhaseArtifact", "properties": {"jobManifestsPath": {"description": "Output only. File path of the directory of rendered job manifests relative to the URI. This is only set if it is applicable.", "readOnly": true, "type": "string"}, "manifestPath": {"description": "Output only. File path of the rendered manifest relative to the URI.", "readOnly": true, "type": "string"}, "skaffoldConfigPath": {"description": "Output only. File path of the resolved Skaffold configuration relative to the URI.", "readOnly": true, "type": "string"}}, "type": "object"}, "PhaseConfig": {"description": "PhaseConfig represents the configuration for a phase in the custom canary deployment.", "id": "PhaseConfig", "properties": {"percentage": {"description": "Required. Percentage deployment for the phase.", "format": "int32", "type": "integer"}, "phaseId": {"description": "Required. The ID to assign to the `Rollout` phase. This value must consist of lower-case letters, numbers, and hyphens, start with a letter and end with a letter or a number, and have a max length of 63 characters. In other words, it must match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "type": "string"}, "postdeploy": {"$ref": "Postdeploy", "description": "Optional. Configuration for the postdeploy job of this phase. If this is not configured, there will be no postdeploy job for this phase."}, "predeploy": {"$ref": "Predeploy", "description": "Optional. Configuration for the predeploy job of this phase. If this is not configured, there will be no predeploy job for this phase."}, "profiles": {"description": "Optional. Skaffold profiles to use when rendering the manifest for this phase. These are in addition to the profiles list specified in the `DeliveryPipeline` stage.", "items": {"type": "string"}, "type": "array"}, "verify": {"description": "Optional. Whether to run verify tests after the deployment via `skaffold verify`.", "type": "boolean"}}, "type": "object"}, "PipelineCondition": {"description": "PipelineCondition contains all conditions relevant to a Delivery Pipeline.", "id": "PipelineCondition", "properties": {"pipelineReadyCondition": {"$ref": "PipelineReadyCondition", "description": "Details around the Pipeline's overall status."}, "targetsPresentCondition": {"$ref": "TargetsPresentCondition", "description": "Details around targets enumerated in the pipeline."}, "targetsTypeCondition": {"$ref": "TargetsTypeCondition", "description": "Details on the whether the targets enumerated in the pipeline are of the same type."}}, "type": "object"}, "PipelineReadyCondition": {"description": "PipelineReadyCondition contains information around the status of the Pipeline.", "id": "PipelineReadyCondition", "properties": {"status": {"description": "True if the Pipeline is in a valid state. Otherwise at least one condition in `PipelineCondition` is in an invalid state. Iterate over those conditions and see which condition(s) has status = false to find out what is wrong with the Pipeline.", "type": "boolean"}, "updateTime": {"description": "Last time the condition was updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PolicyRule": {"description": "Deploy Policy rule.", "id": "PolicyRule", "properties": {"rolloutRestriction": {"$ref": "RolloutRestriction", "description": "Optional. Rollout restrictions."}}, "type": "object"}, "PolicyViolation": {"description": "Returned from an action if one or more policies were violated, and therefore the action was prevented. Contains information about what policies were violated and why.", "id": "PolicyViolation", "properties": {"policyViolationDetails": {"description": "Policy violation details.", "items": {"$ref": "PolicyViolationDetails"}, "type": "array"}}, "type": "object"}, "PolicyViolationDetails": {"description": "Policy violation details.", "id": "PolicyViolationDetails", "properties": {"failureMessage": {"description": "User readable message about why the request violated a policy. This is not intended for machine parsing.", "type": "string"}, "policy": {"description": "Name of the policy that was violated. Policy resource will be in the format of `projects/{project}/locations/{location}/policies/{policy}`.", "type": "string"}, "ruleId": {"description": "Id of the rule that triggered the policy violation.", "type": "string"}}, "type": "object"}, "Postdeploy": {"description": "Postdeploy contains the postdeploy job configuration information.", "id": "Postdeploy", "properties": {"actions": {"description": "Optional. A sequence of Skaffold custom actions to invoke during execution of the postdeploy job.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PostdeployJob": {"description": "A postdeploy Job.", "id": "PostdeployJob", "properties": {"actions": {"description": "Output only. The custom actions that the postdeploy Job executes.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "PostdeployJobRun": {"description": "PostdeployJobRun contains information specific to a postdeploy `JobRun`.", "id": "PostdeployJobRun", "properties": {"build": {"description": "Output only. The resource name of the Cloud Build `Build` object that is used to execute the custom actions associated with the postdeploy Job. Format is `projects/{project}/locations/{location}/builds/{build}`.", "readOnly": true, "type": "string"}, "failureCause": {"description": "Output only. The reason the postdeploy failed. This will always be unspecified while the postdeploy is in progress or if it succeeded.", "enum": ["FAILURE_CAUSE_UNSPECIFIED", "CLOUD_BUILD_UNAVAILABLE", "EXECUTION_FAILED", "DEADLINE_EXCEEDED", "CLOUD_BUILD_REQUEST_FAILED"], "enumDescriptions": ["No reason for failure is specified.", "Cloud Build is not available, either because it is not enabled or because Cloud Deploy has insufficient permissions. See [required permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).", "The postdeploy operation did not complete successfully; check Cloud Build logs.", "The postdeploy job run did not complete within the allotted time.", "Cloud Build failed to fulfill Cloud Deploy's request. See failure_message for additional details."], "readOnly": true, "type": "string"}, "failureMessage": {"description": "Output only. Additional information about the postdeploy failure, if available.", "readOnly": true, "type": "string"}}, "type": "object"}, "Predeploy": {"description": "Predeploy contains the predeploy job configuration information.", "id": "Predeploy", "properties": {"actions": {"description": "Optional. A sequence of Skaffold custom actions to invoke during execution of the predeploy job.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PredeployJob": {"description": "A predeploy Job.", "id": "PredeployJob", "properties": {"actions": {"description": "Output only. The custom actions that the predeploy Job executes.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "PredeployJobRun": {"description": "PredeployJobRun contains information specific to a predeploy `JobRun`.", "id": "PredeployJobRun", "properties": {"build": {"description": "Output only. The resource name of the Cloud Build `Build` object that is used to execute the custom actions associated with the predeploy Job. Format is `projects/{project}/locations/{location}/builds/{build}`.", "readOnly": true, "type": "string"}, "failureCause": {"description": "Output only. The reason the predeploy failed. This will always be unspecified while the predeploy is in progress or if it succeeded.", "enum": ["FAILURE_CAUSE_UNSPECIFIED", "CLOUD_BUILD_UNAVAILABLE", "EXECUTION_FAILED", "DEADLINE_EXCEEDED", "CLOUD_BUILD_REQUEST_FAILED"], "enumDescriptions": ["No reason for failure is specified.", "Cloud Build is not available, either because it is not enabled or because Cloud Deploy has insufficient permissions. See [required permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).", "The predeploy operation did not complete successfully; check Cloud Build logs.", "The predeploy job run did not complete within the allotted time.", "Cloud Build failed to fulfill Cloud Deploy's request. See failure_message for additional details."], "readOnly": true, "type": "string"}, "failureMessage": {"description": "Output only. Additional information about the predeploy failure, if available.", "readOnly": true, "type": "string"}}, "type": "object"}, "PrivatePool": {"description": "Execution using a private Cloud Build pool.", "id": "PrivatePool", "properties": {"artifactStorage": {"description": "Optional. Cloud Storage location where execution outputs should be stored. This can either be a bucket (\"gs://my-bucket\") or a path within a bucket (\"gs://my-bucket/my-dir\"). If unspecified, a default bucket located in the same region will be used.", "type": "string"}, "serviceAccount": {"description": "Optional. Google service account to use for execution. If unspecified, the project execution service account (-<EMAIL>) will be used.", "type": "string"}, "workerPool": {"description": "Required. Resource name of the Cloud Build worker pool to use. The format is `projects/{project}/locations/{location}/workerPools/{pool}`.", "type": "string"}}, "type": "object"}, "PromoteReleaseOperation": {"description": "Contains the information of an automated promote-release operation.", "id": "PromoteReleaseOperation", "properties": {"phase": {"description": "Output only. The starting phase of the rollout created by this operation.", "readOnly": true, "type": "string"}, "rollout": {"description": "Output only. The name of the rollout that initiates the `AutomationRun`.", "readOnly": true, "type": "string"}, "targetId": {"description": "Output only. The ID of the target that represents the promotion stage to which the release will be promoted. The value of this field is the last segment of a target name.", "readOnly": true, "type": "string"}, "wait": {"description": "Output only. How long the operation will be paused.", "format": "google-duration", "readOnly": true, "type": "string"}}, "type": "object"}, "PromoteReleaseRule": {"description": "The `PromoteRelease` rule will automatically promote a release from the current target to a specified target.", "id": "PromoteReleaseRule", "properties": {"condition": {"$ref": "AutomationRuleCondition", "description": "Output only. Information around the state of the Automation rule.", "readOnly": true}, "destinationPhase": {"description": "Optional. The starting phase of the rollout created by this operation. Default to the first phase.", "type": "string"}, "destinationTargetId": {"description": "Optional. The ID of the stage in the pipeline to which this `Release` is deploying. If unspecified, default it to the next stage in the promotion flow. The value of this field could be one of the following: * The last segment of a target name * \"@next\", the next target in the promotion sequence", "type": "string"}, "id": {"description": "Required. ID of the rule. This id must be unique in the `Automation` resource to which this rule belongs. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "type": "string"}, "wait": {"description": "Optional. How long the release need to be paused until being promoted to the next target.", "format": "google-duration", "type": "string"}}, "type": "object"}, "Release": {"description": "A `Release` resource in the Cloud Deploy API. A `Release` defines a specific Skaffold configuration instance that can be deployed.", "id": "Release", "properties": {"abandoned": {"description": "Output only. Indicates whether this is an abandoned release.", "readOnly": true, "type": "boolean"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.", "type": "object"}, "buildArtifacts": {"description": "Optional. List of artifacts to pass through to Skaffold command.", "items": {"$ref": "BuildArtifact"}, "type": "array"}, "condition": {"$ref": "ReleaseCondition", "description": "Output only. Information around the state of the Release.", "readOnly": true}, "createTime": {"description": "Output only. Time at which the `Release` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customTargetTypeSnapshots": {"description": "Output only. Snapshot of the custom target types referenced by the targets taken at release creation time.", "items": {"$ref": "CustomTargetType"}, "readOnly": true, "type": "array"}, "deliveryPipelineSnapshot": {"$ref": "DeliveryPipeline", "description": "Output only. Snapshot of the parent pipeline taken at release creation time.", "readOnly": true}, "deployParameters": {"additionalProperties": {"type": "string"}, "description": "Optional. The deploy parameters to use for all targets in this release.", "type": "object"}, "description": {"description": "Optional. Description of the `Release`. Max length is 255 characters.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.", "type": "object"}, "name": {"description": "Identifier. Name of the `Release`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}`. The `release` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "type": "string"}, "renderEndTime": {"description": "Output only. Time at which the render completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "renderStartTime": {"description": "Output only. Time at which the render began.", "format": "google-datetime", "readOnly": true, "type": "string"}, "renderState": {"description": "Output only. Current state of the render operation.", "enum": ["RENDER_STATE_UNSPECIFIED", "SUCCEEDED", "FAILED", "IN_PROGRESS"], "enumDescriptions": ["The render state is unspecified.", "All rendering operations have completed successfully.", "All rendering operations have completed, and one or more have failed.", "Rendering has started and is not complete."], "readOnly": true, "type": "string"}, "skaffoldConfigPath": {"description": "Optional. Filepath of the Skaffold config inside of the config URI.", "type": "string"}, "skaffoldConfigUri": {"description": "Optional. Cloud Storage URI of tar.gz archive containing Skaffold configuration.", "type": "string"}, "skaffoldVersion": {"description": "Optional. The Skaffold version to use when operating on this release, such as \"1.20.0\". Not all versions are valid; Cloud Deploy supports a specific set of versions. If unset, the most recent supported Skaffold version will be used.", "type": "string"}, "targetArtifacts": {"additionalProperties": {"$ref": "TargetArtifact"}, "description": "Output only. Map from target ID to the target artifacts created during the render operation.", "readOnly": true, "type": "object"}, "targetRenders": {"additionalProperties": {"$ref": "TargetRender"}, "description": "Output only. Map from target ID to details of the render operation for that target.", "readOnly": true, "type": "object"}, "targetSnapshots": {"description": "Output only. Snapshot of the targets taken at release creation time.", "items": {"$ref": "Target"}, "readOnly": true, "type": "array"}, "uid": {"description": "Output only. Unique identifier of the `Release`.", "readOnly": true, "type": "string"}}, "type": "object"}, "ReleaseCondition": {"description": "ReleaseCondition contains all conditions relevant to a Release.", "id": "ReleaseCondition", "properties": {"releaseReadyCondition": {"$ref": "ReleaseReadyCondition", "description": "Details around the Releases's overall status."}, "skaffoldSupportedCondition": {"$ref": "SkaffoldSupportedCondition", "description": "Details around the support state of the release's Skaffold version."}}, "type": "object"}, "ReleaseNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/release_notification\" Platform Log event that describes the failure to send release status change Pub/Sub notification.", "id": "ReleaseNotificationEvent", "properties": {"message": {"description": "Debug message for when a notification fails to send.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "release": {"description": "The name of the `Release`.", "type": "string"}, "releaseUid": {"description": "Unique identifier of the `Release`.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "ReleaseReadyCondition": {"description": "ReleaseReadyCondition contains information around the status of the Release. If a release is not ready, you cannot create a rollout with the release.", "id": "ReleaseReadyCondition", "properties": {"status": {"description": "True if the Release is in a valid state. Otherwise at least one condition in `ReleaseCondition` is in an invalid state. Iterate over those conditions and see which condition(s) has status = false to find out what is wrong with the Release.", "type": "boolean"}}, "type": "object"}, "ReleaseRenderEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/release_render\" Platform Log event that describes the render status change.", "id": "ReleaseRenderEvent", "properties": {"message": {"description": "Debug message for when a render transition occurs. Provides further details as rendering progresses through render states.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "release": {"description": "The name of the release. release_uid is not in this log message because we write some of these log messages at release creation time, before we've generated the uid.", "type": "string"}, "releaseRenderState": {"description": "The state of the release render.", "enum": ["RENDER_STATE_UNSPECIFIED", "SUCCEEDED", "FAILED", "IN_PROGRESS"], "enumDescriptions": ["The render state is unspecified.", "All rendering operations have completed successfully.", "All rendering operations have completed, and one or more have failed.", "Rendering has started and is not complete."], "type": "string"}, "type": {"description": "Type of this notification, e.g. for a release render state change event.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "RenderMetadata": {"description": "RenderMetadata includes information associated with a `Release` render.", "id": "RenderMetadata", "properties": {"cloudRun": {"$ref": "CloudRunRenderMetadata", "description": "Output only. Metadata associated with rendering for Cloud Run.", "readOnly": true}, "custom": {"$ref": "CustomMetadata", "description": "Output only. Custom metadata provided by user-defined render operation.", "readOnly": true}}, "type": "object"}, "RepairPhase": {"description": "RepairPhase tracks the repair attempts that have been made for each `RepairPhaseConfig` specified in the `Automation` resource.", "id": "RepairPhase", "properties": {"retry": {"$ref": "RetryPhase", "description": "Output only. Records of the retry attempts for retry repair mode.", "readOnly": true}, "rollback": {"$ref": "Roll<PERSON><PERSON><PERSON><PERSON>", "description": "Output only. Rollback attempt for rollback repair mode .", "readOnly": true}}, "type": "object"}, "RepairPhaseConfig": {"description": "Configuration of the repair phase.", "id": "RepairPhaseConfig", "properties": {"retry": {"$ref": "Retry", "description": "Optional. Retries a failed job."}, "rollback": {"$ref": "Rollback", "description": "Optional. Rolls back a `Rollout`."}}, "type": "object"}, "RepairRolloutOperation": {"description": "Contains the information for an automated `repair rollout` operation.", "id": "RepairRolloutOperation", "properties": {"currentRepairPhaseIndex": {"description": "Output only. The index of the current repair action in the repair sequence.", "format": "int64", "readOnly": true, "type": "string"}, "jobId": {"description": "Output only. The job ID for the Job to repair.", "readOnly": true, "type": "string"}, "phaseId": {"description": "Output only. The phase ID of the phase that includes the job being repaired.", "readOnly": true, "type": "string"}, "repairPhases": {"description": "Output only. Records of the repair attempts. Each repair phase may have multiple retry attempts or single rollback attempt.", "items": {"$ref": "RepairPhase"}, "readOnly": true, "type": "array"}, "rollout": {"description": "Output only. The name of the rollout that initiates the `AutomationRun`.", "readOnly": true, "type": "string"}}, "type": "object"}, "RepairRolloutRule": {"description": "The `RepairRolloutRule` automation rule will automatically repair a failed `Rollout`.", "id": "RepairRolloutRule", "properties": {"condition": {"$ref": "AutomationRuleCondition", "description": "Output only. Information around the state of the 'Automation' rule.", "readOnly": true}, "id": {"description": "Required. ID of the rule. This id must be unique in the `Automation` resource to which this rule belongs. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "type": "string"}, "jobs": {"description": "Optional. Jobs to repair. Proceeds only after job name matched any one in the list, or for all jobs if unspecified or empty. The phase that includes the job must match the phase ID specified in `source_phase`. This value must consist of lower-case letters, numbers, and hyphens, start with a letter and end with a letter or a number, and have a max length of 63 characters. In other words, it must match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "items": {"type": "string"}, "type": "array"}, "phases": {"description": "Optional. Phases within which jobs are subject to automatic repair actions on failure. Proceeds only after phase name matched any one in the list, or for all phases if unspecified. This value must consist of lower-case letters, numbers, and hyphens, start with a letter and end with a letter or a number, and have a max length of 63 characters. In other words, it must match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "items": {"type": "string"}, "type": "array"}, "repairPhases": {"description": "Required. Defines the types of automatic repair phases for failed jobs.", "items": {"$ref": "RepairPhaseConfig"}, "type": "array"}}, "type": "object"}, "Retry": {"description": "Retries the failed job.", "id": "Retry", "properties": {"attempts": {"description": "Required. Total number of retries. Retry is skipped if set to 0; The minimum value is 1, and the maximum value is 10.", "format": "int64", "type": "string"}, "backoffMode": {"description": "Optional. The pattern of how wait time will be increased. Default is linear. Backoff mode will be ignored if `wait` is 0.", "enum": ["BACKOFF_MODE_UNSPECIFIED", "BACKOFF_MODE_LINEAR", "BACKOFF_MODE_EXPONENTIAL"], "enumDescriptions": ["No WaitMode is specified.", "Increases the wait time linearly.", "Increases the wait time exponentially."], "type": "string"}, "wait": {"description": "Optional. How long to wait for the first retry. Default is 0, and the maximum value is 14d.", "format": "google-duration", "type": "string"}}, "type": "object"}, "RetryAttempt": {"description": "RetryAttempt represents an action of retrying the failed Cloud Deploy job.", "id": "RetryAttempt", "properties": {"attempt": {"description": "Output only. The index of this retry attempt.", "format": "int64", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Valid state of this retry action.", "enum": ["REPAIR_STATE_UNSPECIFIED", "REPAIR_STATE_SUCCEEDED", "REPAIR_STATE_CANCELLED", "REPAIR_STATE_FAILED", "REPAIR_STATE_IN_PROGRESS", "REPAIR_STATE_PENDING", "REPAIR_STATE_ABORTED"], "enumDescriptions": ["The `repair` has an unspecified state.", "The `repair` action has succeeded.", "The `repair` action was cancelled.", "The `repair` action has failed.", "The `repair` action is in progress.", "The `repair` action is pending.", "The `repair` action was aborted."], "readOnly": true, "type": "string"}, "stateDesc": {"description": "Output only. Description of the state of the Retry.", "readOnly": true, "type": "string"}, "wait": {"description": "Output only. How long the operation will be paused.", "format": "google-duration", "readOnly": true, "type": "string"}}, "type": "object"}, "RetryJobRequest": {"description": "RetryJobRequest is the request object used by `RetryJob`.", "id": "RetryJobRequest", "properties": {"jobId": {"description": "Required. The job ID for the Job to retry.", "type": "string"}, "overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "items": {"type": "string"}, "type": "array"}, "phaseId": {"description": "Required. The phase ID the Job to retry belongs to.", "type": "string"}}, "type": "object"}, "RetryJobResponse": {"description": "The response object from 'Re<PERSON><PERSON>ob'.", "id": "RetryJobResponse", "properties": {}, "type": "object"}, "RetryPhase": {"description": "RetryPhase contains the retry attempts and the metadata for initiating a new attempt.", "id": "RetryPhase", "properties": {"attempts": {"description": "Output only. Detail of a retry action.", "items": {"$ref": "RetryAttempt"}, "readOnly": true, "type": "array"}, "backoffMode": {"description": "Output only. The pattern of how the wait time of the retry attempt is calculated.", "enum": ["BACKOFF_MODE_UNSPECIFIED", "BACKOFF_MODE_LINEAR", "BACKOFF_MODE_EXPONENTIAL"], "enumDescriptions": ["No WaitMode is specified.", "Increases the wait time linearly.", "Increases the wait time exponentially."], "readOnly": true, "type": "string"}, "totalAttempts": {"description": "Output only. The number of attempts that have been made.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "Rollback": {"description": "Rolls back a `Rollout`.", "id": "Rollback", "properties": {"destinationPhase": {"description": "Optional. The starting phase ID for the `Rollout`. If unspecified, the `Rollout` will start in the stable phase.", "type": "string"}, "disableRollbackIfRolloutPending": {"description": "Optional. If pending rollout exists on the target, the rollback operation will be aborted.", "type": "boolean"}}, "type": "object"}, "RollbackAttempt": {"description": "RollbackAttempt represents an action of rolling back a Cloud Deploy 'Target'.", "id": "Roll<PERSON><PERSON><PERSON><PERSON>", "properties": {"destinationPhase": {"description": "Output only. The phase to which the rollout will be rolled back to.", "readOnly": true, "type": "string"}, "disableRollbackIfRolloutPending": {"description": "Output only. If active rollout exists on the target, abort this rollback.", "readOnly": true, "type": "boolean"}, "rolloutId": {"description": "Output only. ID of the rollback `Rollout` to create.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Valid state of this rollback action.", "enum": ["REPAIR_STATE_UNSPECIFIED", "REPAIR_STATE_SUCCEEDED", "REPAIR_STATE_CANCELLED", "REPAIR_STATE_FAILED", "REPAIR_STATE_IN_PROGRESS", "REPAIR_STATE_PENDING", "REPAIR_STATE_ABORTED"], "enumDescriptions": ["The `repair` has an unspecified state.", "The `repair` action has succeeded.", "The `repair` action was cancelled.", "The `repair` action has failed.", "The `repair` action is in progress.", "The `repair` action is pending.", "The `repair` action was aborted."], "readOnly": true, "type": "string"}, "stateDesc": {"description": "Output only. Description of the state of the Rollback.", "readOnly": true, "type": "string"}}, "type": "object"}, "RollbackTargetConfig": {"description": "Configs for the Rollback rollout.", "id": "RollbackTargetConfig", "properties": {"rollout": {"$ref": "Rollout", "description": "Optional. The rollback `Rollout` to create."}, "startingPhaseId": {"description": "Optional. The starting phase ID for the `Rollout`. If unspecified, the `Rollout` will start in the stable phase.", "type": "string"}}, "type": "object"}, "RollbackTargetRequest": {"description": "The request object for `RollbackTarget`.", "id": "RollbackTargetRequest", "properties": {"overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deploy_policy}`.", "items": {"type": "string"}, "type": "array"}, "releaseId": {"description": "Optional. ID of the `Release` to roll back to. If this isn't specified, the previous successful `Rollout` to the specified target will be used to determine the `Release`.", "type": "string"}, "rollbackConfig": {"$ref": "RollbackTargetConfig", "description": "Optional. Configs for the rollback `Rollout`."}, "rolloutId": {"description": "Required. ID of the rollback `Rollout` to create.", "type": "string"}, "rolloutToRollBack": {"description": "Optional. If provided, this must be the latest `Rollout` that is on the `Target`.", "type": "string"}, "targetId": {"description": "Required. ID of the `Target` that is being rolled back.", "type": "string"}, "validateOnly": {"description": "Optional. If set to true, the request is validated and the user is provided with a `RollbackTargetResponse`.", "type": "boolean"}}, "type": "object"}, "RollbackTargetResponse": {"description": "The response object from `RollbackTarget`.", "id": "RollbackTargetResponse", "properties": {"rollbackConfig": {"$ref": "RollbackTargetConfig", "description": "The config of the rollback `Rollout` created or will be created."}}, "type": "object"}, "Rollout": {"description": "A `Rollout` resource in the Cloud Deploy API. A `Rollout` contains information around a specific deployment to a `Target`.", "id": "Rollout", "properties": {"activeRepairAutomationRun": {"description": "Output only. The AutomationRun actively repairing the rollout.", "readOnly": true, "type": "string"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.", "type": "object"}, "approvalState": {"description": "Output only. Approval state of the `Rollout`.", "enum": ["APPROVAL_STATE_UNSPECIFIED", "NEEDS_APPROVAL", "DOES_NOT_NEED_APPROVAL", "APPROVED", "REJECTED"], "enumDescriptions": ["The `Rollout` has an unspecified approval state.", "The `Rollout` requires approval.", "The `Rollout` does not require approval.", "The `Rollout` has been approved.", "The `Rollout` has been rejected."], "readOnly": true, "type": "string"}, "approveTime": {"description": "Output only. Time at which the `Rollout` was approved.", "format": "google-datetime", "readOnly": true, "type": "string"}, "controllerRollout": {"description": "Output only. Name of the `ControllerRollout`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time at which the `Rollout` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deployEndTime": {"description": "Output only. Time at which the `Rollout` finished deploying.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deployFailureCause": {"description": "Output only. The reason this rollout failed. This will always be unspecified while the rollout is in progress.", "enum": ["FAILURE_CAUSE_UNSPECIFIED", "CLOUD_BUILD_UNAVAILABLE", "EXECUTION_FAILED", "DEADLINE_EXCEEDED", "RELEASE_FAILED", "RELEASE_ABANDONED", "VERIFICATION_CONFIG_NOT_FOUND", "CLOUD_BUILD_REQUEST_FAILED", "OPERATION_FEATURE_NOT_SUPPORTED"], "enumDescriptions": ["No reason for failure is specified.", "Cloud Build is not available, either because it is not enabled or because Cloud Deploy has insufficient permissions. See [required permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).", "The deploy operation did not complete successfully; check Cloud Build logs.", "Deployment did not complete within the allotted time.", "Release is in a failed state.", "Release is abandoned.", "No Skaffold verify configuration was found.", "Cloud Build failed to fulfill Cloud Deploy's request. See failure_message for additional details.", "A Rollout operation had a feature configured that is not supported."], "readOnly": true, "type": "string"}, "deployStartTime": {"description": "Output only. Time at which the `Rollout` started deploying.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deployingBuild": {"description": "Output only. The resource name of the Cloud Build `Build` object that is used to deploy the Rollout. Format is `projects/{project}/locations/{location}/builds/{build}`.", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the `Rollout` for user purposes. Max length is 255 characters.", "type": "string"}, "enqueueTime": {"description": "Output only. Time at which the `Rollout` was enqueued.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "failureReason": {"description": "Output only. Additional information about the rollout failure, if available.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.", "type": "object"}, "metadata": {"$ref": "<PERSON><PERSON><PERSON>", "description": "Output only. Metadata contains information about the rollout.", "readOnly": true}, "name": {"description": "Identifier. Name of the `Rollout`. Format is `projects/{project}/locations/{location}/deliveryPipelines/{deliveryPipeline}/releases/{release}/rollouts/{rollout}`. The `rollout` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "type": "string"}, "phases": {"description": "Output only. The phases that represent the workflows of this `Rollout`.", "items": {"$ref": "Phase"}, "readOnly": true, "type": "array"}, "rollbackOfRollout": {"description": "Output only. Name of the `Rollout` that is rolled back by this `Rollout`. Empty if this `Rollout` wasn't created as a rollback.", "readOnly": true, "type": "string"}, "rolledBackByRollouts": {"description": "Output only. Names of `Rollouts` that rolled back this `Rollout`.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. Current state of the `Rollout`.", "enum": ["STATE_UNSPECIFIED", "SUCCEEDED", "FAILED", "IN_PROGRESS", "PENDING_APPROVAL", "APPROVAL_REJECTED", "PENDING", "PENDING_RELEASE", "CANCELLING", "CANCELLED", "HALTED"], "enumDescriptions": ["The `Rollout` has an unspecified state.", "The `Rollout` has completed successfully.", "The `Rollout` has failed.", "The `Rollout` is being deployed.", "The `Rollout` needs approval.", "An approver rejected the `Rollout`.", "The `Rollout` is waiting for an earlier Rollout(s) to complete on this `Target`.", "The `Rollout` is waiting for the `Release` to be fully rendered.", "The `Rollout` is in the process of being cancelled.", "The `Rollout` has been cancelled.", "The `Rollout` is halted."], "readOnly": true, "type": "string"}, "targetId": {"description": "Required. The ID of Target to which this `Rollout` is deploying.", "type": "string"}, "uid": {"description": "Output only. Unique identifier of the `Rollout`.", "readOnly": true, "type": "string"}}, "type": "object"}, "RolloutNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/rollout_notification\" Platform Log event that describes the failure to send rollout status change Pub/Sub notification.", "id": "RolloutNotificationEvent", "properties": {"message": {"description": "Debug message for when a notification fails to send.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the `DeliveryPipeline`.", "type": "string"}, "release": {"description": "The name of the `Release`.", "type": "string"}, "releaseUid": {"description": "Unique identifier of the `Release`.", "type": "string"}, "rollout": {"description": "The name of the `Rollout`.", "type": "string"}, "rolloutUid": {"description": "Unique identifier of the `Rollout`.", "type": "string"}, "targetId": {"description": "ID of the `Target` that the rollout is deployed to.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "RolloutRestriction": {"description": "Rollout restrictions.", "id": "RolloutRestriction", "properties": {"actions": {"description": "Optional. Rollout actions to be restricted as part of the policy. If left empty, all actions will be restricted.", "items": {"enum": ["ROLLOUT_ACTIONS_UNSPECIFIED", "ADVANCE", "APPROVE", "CANCEL", "CREATE", "IGNORE_JOB", "RETRY_JOB", "ROLLBACK", "TERMINATE_JOBRUN"], "enumDescriptions": ["Unspecified.", "Advance the rollout to the next phase.", "Approve the rollout.", "Cancel the rollout.", "Create a rollout.", "Ignore a job result on the rollout.", "Retry a job for a rollout.", "Rollback a rollout.", "Terminate a jobrun."], "type": "string"}, "type": "array"}, "id": {"description": "Required. Restriction rule ID. Required and must be unique within a DeployPolicy. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "type": "string"}, "invokers": {"description": "Optional. What invoked the action. If left empty, all invoker types will be restricted.", "items": {"enum": ["INVOKER_UNSPECIFIED", "USER", "DEPLOY_AUTOMATION"], "enumDescriptions": ["Unspecified.", "The action is user-driven. For example, creating a rollout manually via a gcloud create command.", "Automated action by Cloud Deploy."], "type": "string"}, "type": "array"}, "timeWindows": {"$ref": "TimeWindows", "description": "Required. Time window within which actions are restricted."}}, "type": "object"}, "RolloutUpdateEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/rollout_update\" Platform Log event that describes the rollout update event.", "id": "RolloutUpdateEvent", "properties": {"message": {"description": "Debug message for when a rollout update event occurs.", "type": "string"}, "pipelineUid": {"description": "Unique identifier of the pipeline.", "type": "string"}, "release": {"description": "The name of the `Release`.", "type": "string"}, "releaseUid": {"description": "Unique identifier of the release.", "type": "string"}, "rollout": {"description": "The name of the rollout. rollout_uid is not in this log message because we write some of these log messages at rollout creation time, before we've generated the uid.", "type": "string"}, "rolloutUpdateType": {"description": "The type of the rollout update.", "enum": ["ROLLOUT_UPDATE_TYPE_UNSPECIFIED", "PENDING", "PENDING_RELEASE", "IN_PROGRESS", "CANCELLING", "CANCELLED", "HALTED", "SUCCEEDED", "FAILED", "APPROVAL_REQUIRED", "APPROVED", "REJECTED", "ADVANCE_REQUIRED", "ADVANCED"], "enumDescriptions": ["Rollout update type unspecified.", "rollout state updated to pending.", "Rollout state updated to pending release.", "Rollout state updated to in progress.", "Rollout state updated to cancelling.", "Rollout state updated to cancelled.", "Rollout state updated to halted.", "Rollout state updated to succeeded.", "Rollout state updated to failed.", "Rollout requires approval.", "Rollout has been approved.", "Rollout has been rejected.", "Rollout requires advance to the next phase.", "Rollout has been advanced."], "type": "string"}, "targetId": {"description": "ID of the target.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a rollout update event.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "RouteDestinations": {"description": "Information about route destinations for the Gateway API service mesh.", "id": "RouteDestinations", "properties": {"destinationIds": {"description": "Required. The clusters where the Gateway API HTTPRoute resource will be deployed to. Valid entries include the associated entities IDs configured in the Target resource and \"@self\" to include the Target cluster.", "items": {"type": "string"}, "type": "array"}, "propagateService": {"description": "Optional. Whether to propagate the Kubernetes Service to the route destination clusters. The Service will always be deployed to the Target cluster even if the HTTPRoute is not. This option may be used to facilitate successful DNS lookup in the route destination clusters. Can only be set to true if destinations are specified.", "type": "boolean"}}, "type": "object"}, "RuntimeConfig": {"description": "RuntimeConfig contains the runtime specific configurations for a deployment strategy.", "id": "RuntimeConfig", "properties": {"cloudRun": {"$ref": "CloudRunConfig", "description": "Optional. Cloud Run runtime configuration."}, "kubernetes": {"$ref": "KubernetesConfig", "description": "Optional. Kubernetes runtime configuration."}}, "type": "object"}, "SerialPipeline": {"description": "SerialPipeline defines a sequential set of stages for a `DeliveryPipeline`.", "id": "Ser<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"stages": {"description": "Optional. Each stage specifies configuration for a `Target`. The ordering of this list defines the promotion flow.", "items": {"$ref": "Stage"}, "type": "array"}}, "type": "object"}, "ServiceNetworking": {"description": "Information about the Kubernetes Service networking configuration.", "id": "ServiceNetworking", "properties": {"deployment": {"description": "Required. Name of the Kubernetes Deployment whose traffic is managed by the specified Service.", "type": "string"}, "disablePodOverprovisioning": {"description": "Optional. Whether to disable Pod overprovisioning. If Pod overprovisioning is disabled then Cloud Deploy will limit the number of total Pods used for the deployment strategy to the number of Pods the Deployment has on the cluster.", "type": "boolean"}, "podSelectorLabel": {"description": "Optional. The label to use when selecting Pods for the Deployment resource. This label must already be present in the Deployment.", "type": "string"}, "service": {"description": "Required. Name of the Kubernetes Service.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SkaffoldGCBRepoSource": {"description": "Cloud Build V2 Repository containing Skaffold Configs.", "id": "SkaffoldGCBRepoSource", "properties": {"path": {"description": "Optional. Relative path from the repository root to the Skaffold Config file.", "type": "string"}, "ref": {"description": "Optional. Branch or tag to use when cloning the repository.", "type": "string"}, "repository": {"description": "Required. Name of the Cloud Build V2 Repository. Format is projects/{project}/locations/{location}/connections/{connection}/repositories/{repository}.", "type": "string"}}, "type": "object"}, "SkaffoldGCSSource": {"description": "Cloud Storage bucket containing Skaffold Config modules.", "id": "SkaffoldGCSSource", "properties": {"path": {"description": "Optional. Relative path from the source to the Skaffold file.", "type": "string"}, "source": {"description": "Required. Cloud Storage source paths to copy recursively. For example, providing \"gs://my-bucket/dir/configs/*\" will result in S<PERSON>ffold copying all files within the \"dir/configs\" directory in the bucket \"my-bucket\".", "type": "string"}}, "type": "object"}, "SkaffoldGitSource": {"description": "Git repository containing Skaffold Config modules.", "id": "SkaffoldGitSource", "properties": {"path": {"description": "Optional. Relative path from the repository root to the Skaffold file.", "type": "string"}, "ref": {"description": "Optional. Git branch or tag to use when cloning the repository.", "type": "string"}, "repo": {"description": "Required. Git repository the package should be cloned from.", "type": "string"}}, "type": "object"}, "SkaffoldModules": {"description": "Skaffold Config modules and their remote source.", "id": "SkaffoldModules", "properties": {"configs": {"description": "Optional. The Skaffold Config modules to use from the specified source.", "items": {"type": "string"}, "type": "array"}, "git": {"$ref": "SkaffoldGitSource", "description": "Optional. Remote git repository containing the Skaffold Config modules."}, "googleCloudBuildRepo": {"$ref": "SkaffoldGCBRepoSource", "description": "Optional. Cloud Build V2 repository containing the Skaffold Config modules."}, "googleCloudStorage": {"$ref": "SkaffoldGCSSource", "description": "Optional. Cloud Storage bucket containing the Skaffold Config modules."}}, "type": "object"}, "SkaffoldSupportedCondition": {"description": "SkaffoldSupportedCondition contains information about when support for the release's version of Skaffold ends.", "id": "SkaffoldSupportedCondition", "properties": {"maintenanceModeTime": {"description": "The time at which this release's version of Skaffold will enter maintenance mode.", "format": "google-datetime", "type": "string"}, "skaffoldSupportState": {"description": "The Skaffold support state for this release's version of Skaffold.", "enum": ["SKAFFOLD_SUPPORT_STATE_UNSPECIFIED", "SKAFFOLD_SUPPORT_STATE_SUPPORTED", "SKAFFOLD_SUPPORT_STATE_MAINTENANCE_MODE", "SKAFFOLD_SUPPORT_STATE_UNSUPPORTED"], "enumDescriptions": ["Default value. This value is unused.", "This Skaffold version is currently supported.", "This Skaffold version is in maintenance mode.", "This Skaffold version is no longer supported."], "type": "string"}, "status": {"description": "True if the version of <PERSON><PERSON><PERSON><PERSON> used by this release is supported.", "type": "boolean"}, "supportExpirationTime": {"description": "The time at which this release's version of S<PERSON>ffold will no longer be supported.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "SkaffoldVersion": {"description": "Details of a supported Skaffold version.", "id": "SkaffoldVersion", "properties": {"maintenanceModeTime": {"description": "The time at which this version of Skaffold will enter maintenance mode.", "format": "google-datetime", "type": "string"}, "supportEndDate": {"$ref": "Date", "description": "Date when this version is expected to no longer be supported."}, "supportExpirationTime": {"description": "The time at which this version of Skaffold will no longer be supported.", "format": "google-datetime", "type": "string"}, "version": {"description": "Release version number. For example, \"1.20.3\".", "type": "string"}}, "type": "object"}, "Stage": {"description": "Stage specifies a location to which to deploy.", "id": "Stage", "properties": {"deployParameters": {"description": "Optional. The deploy parameters to use for the target in this stage.", "items": {"$ref": "DeployParameters"}, "type": "array"}, "profiles": {"description": "Optional. Skaffold profiles to use when rendering the manifest for this stage's `Target`.", "items": {"type": "string"}, "type": "array"}, "strategy": {"$ref": "Strategy", "description": "Optional. The strategy to use for a `Rollout` to this stage."}, "targetId": {"description": "Optional. The target_id to which this stage points. This field refers exclusively to the last segment of a target name. For example, this field would just be `my-target` (rather than `projects/project/locations/location/targets/my-target`). The location of the `Target` is inferred to be the same as the location of the `DeliveryPipeline` that contains this `Stage`.", "type": "string"}}, "type": "object"}, "Standard": {"description": "Standard represents the standard deployment strategy.", "id": "Standard", "properties": {"postdeploy": {"$ref": "Postdeploy", "description": "Optional. Configuration for the postdeploy job. If this is not configured, the postdeploy job will not be present."}, "predeploy": {"$ref": "Predeploy", "description": "Optional. Configuration for the predeploy job. If this is not configured, the predeploy job will not be present."}, "verify": {"description": "Optional. Whether to verify a deployment via `skaffold verify`.", "type": "boolean"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Strategy": {"description": "Strategy contains deployment strategy information.", "id": "Strategy", "properties": {"canary": {"$ref": "Canary", "description": "Optional. Canary deployment strategy provides progressive percentage based deployments to a Target."}, "standard": {"$ref": "Standard", "description": "Optional. Standard deployment strategy executes a single deploy and allows verifying the deployment."}}, "type": "object"}, "Target": {"description": "A `Target` resource in the Cloud Deploy API. A `Target` defines a location to which a Skaffold configuration can be deployed.", "id": "Target", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. These attributes can only be set and used by the user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations for more details such as format and size limitations.", "type": "object"}, "anthosCluster": {"$ref": "AnthosCluster", "description": "Optional. Information specifying an Anthos Cluster."}, "associatedEntities": {"additionalProperties": {"$ref": "AssociatedEntities"}, "description": "Optional. Map of entity IDs to their associated entities. Associated entities allows specifying places other than the deployment target for specific features. For example, the Gateway API canary can be configured to deploy the HTTPRoute to a different cluster(s) than the deployment cluster using associated entities. An entity ID must consist of lower-case letters, numbers, and hyphens, start with a letter and end with a letter or a number, and have a max length of 63 characters. In other words, it must match the following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.", "type": "object"}, "createTime": {"description": "Output only. Time at which the `Target` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customTarget": {"$ref": "CustomTarget", "description": "Optional. Information specifying a Custom Target."}, "deployParameters": {"additionalProperties": {"type": "string"}, "description": "Optional. The deploy parameters to use for this target.", "type": "object"}, "description": {"description": "Optional. Description of the `Target`. Max length is 255 characters.", "type": "string"}, "etag": {"description": "Optional. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.", "type": "string"}, "executionConfigs": {"description": "Optional. Configurations for all execution that relates to this `Target`. Each `ExecutionEnvironmentUsage` value may only be used in a single configuration; using the same value multiple times is an error. When one or more configurations are specified, they must include the `RENDER` and `DEPLOY` `ExecutionEnvironmentUsage` values. When no configurations are specified, execution will use the default specified in `DefaultPool`.", "items": {"$ref": "ExecutionConfig"}, "type": "array"}, "gke": {"$ref": "GkeCluster", "description": "Optional. Information specifying a GKE Cluster."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels are attributes that can be set and used by both the user and by Cloud Deploy. Labels must meet the following constraints: * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. * All characters must use UTF-8 encoding, and international characters are allowed. * Keys must start with a lowercase letter or international character. * Each resource is limited to a maximum of 64 labels. Both keys and values are additionally constrained to be <= 128 bytes.", "type": "object"}, "multiTarget": {"$ref": "MultiTarget", "description": "Optional. Information specifying a multiTarget."}, "name": {"description": "Identifier. Name of the `Target`. Format is `projects/{project}/locations/{location}/targets/{target}`. The `target` component must match `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`", "type": "string"}, "requireApproval": {"description": "Optional. Whether or not the `Target` requires approval.", "type": "boolean"}, "run": {"$ref": "CloudRunLocation", "description": "Optional. Information specifying a Cloud Run deployment target."}, "targetId": {"description": "Output only. Resource id of the `Target`.", "readOnly": true, "type": "string"}, "uid": {"description": "Output only. Unique identifier of the `Target`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Most recent time at which the `Target` was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "TargetArtifact": {"description": "The artifacts produced by a target render operation.", "id": "TargetArtifact", "properties": {"artifactUri": {"description": "Output only. URI of a directory containing the artifacts. This contains deployment configuration used by Skaffold during a rollout, and all paths are relative to this location.", "readOnly": true, "type": "string"}, "manifestPath": {"description": "Output only. File path of the rendered manifest relative to the URI for the stable phase.", "readOnly": true, "type": "string"}, "phaseArtifacts": {"additionalProperties": {"$ref": "PhaseArtifact"}, "description": "Output only. Map from the phase ID to the phase artifacts for the `Target`.", "readOnly": true, "type": "object"}, "skaffoldConfigPath": {"description": "Output only. File path of the resolved Skaffold configuration for the stable phase, relative to the URI.", "readOnly": true, "type": "string"}}, "type": "object"}, "TargetAttribute": {"description": "Contains criteria for selecting Targets. This could be used to select targets for a Deploy Policy or for an Automation.", "id": "TargetAttribute", "properties": {"id": {"description": "Optional. ID of the `Target`. The value of this field could be one of the following: * The last segment of a target name * \"*\", all targets in a location", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Target labels.", "type": "object"}}, "type": "object"}, "TargetNotificationEvent": {"description": "Payload proto for \"clouddeploy.googleapis.com/target_notification\" Platform Log event that describes the failure to send target status change Pub/Sub notification.", "id": "TargetNotificationEvent", "properties": {"message": {"description": "Debug message for when a notification fails to send.", "type": "string"}, "target": {"description": "The name of the `Target`.", "type": "string"}, "type": {"description": "Type of this notification, e.g. for a Pub/Sub failure.", "enum": ["TYPE_UNSPECIFIED", "TYPE_PUBSUB_NOTIFICATION_FAILURE", "TYPE_RESOURCE_STATE_CHANGE", "TYPE_PROCESS_ABORTED", "TYPE_RESTRICTION_VIOLATED", "TYPE_RESOURCE_DELETED", "TYPE_ROLLOUT_UPDATE", "TYPE_DEPLOY_POLICY_EVALUATION", "TYPE_RENDER_STATUES_CHANGE"], "enumDeprecated": [false, false, false, false, false, false, false, false, true], "enumDescriptions": ["Type is unspecified.", "A Pub/Sub notification failed to be sent.", "Resource state changed.", "A process aborted.", "Restriction check failed.", "Resource deleted.", "Rollout updated.", "Deploy Policy evaluation.", "Deprecated: This field is never used. Use release_render log type instead."], "type": "string"}}, "type": "object"}, "TargetRender": {"description": "Details of rendering for a single target.", "id": "TargetRender", "properties": {"failureCause": {"description": "Output only. Reason this render failed. This will always be unspecified while the render in progress.", "enum": ["FAILURE_CAUSE_UNSPECIFIED", "CLOUD_BUILD_UNAVAILABLE", "EXECUTION_FAILED", "CLOUD_BUILD_REQUEST_FAILED", "VERIFICATION_CONFIG_NOT_FOUND", "CUSTOM_ACTION_NOT_FOUND", "DEPLOYMENT_STRATEGY_NOT_SUPPORTED", "RENDER_FEATURE_NOT_SUPPORTED"], "enumDescriptions": ["No reason for failure is specified.", "Cloud Build is not available, either because it is not enabled or because Cloud Deploy has insufficient permissions. See [required permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).", "The render operation did not complete successfully; check Cloud Build logs.", "Cloud Build failed to fulfill Cloud Deploy's request. See failure_message for additional details.", "The render operation did not complete successfully because the verification stanza required for verify was not found on the Skaffold configuration.", "The render operation did not complete successfully because the custom action(s) required for Rollout jobs were not found in the Skaffold configuration. See failure_message for additional details.", "Release failed during rendering because the release configuration is not supported with the specified deployment strategy.", "The render operation had a feature configured that is not supported."], "readOnly": true, "type": "string"}, "failureMessage": {"description": "Output only. Additional information about the render failure, if available.", "readOnly": true, "type": "string"}, "metadata": {"$ref": "RenderMetadata", "description": "Output only. Metadata related to the `Release` render for this Target.", "readOnly": true}, "renderingBuild": {"description": "Output only. The resource name of the Cloud Build `Build` object that is used to render the manifest for this target. Format is `projects/{project}/locations/{location}/builds/{build}`.", "readOnly": true, "type": "string"}, "renderingState": {"description": "Output only. Current state of the render operation for this Target.", "enum": ["TARGET_RENDER_STATE_UNSPECIFIED", "SUCCEEDED", "FAILED", "IN_PROGRESS"], "enumDescriptions": ["The render operation state is unspecified.", "The render operation has completed successfully.", "The render operation has failed.", "The render operation is in progress."], "readOnly": true, "type": "string"}}, "type": "object"}, "Targets": {"description": "The targets involved in a single timed promotion.", "id": "Targets", "properties": {"destinationTargetId": {"description": "Optional. The destination target ID.", "type": "string"}, "sourceTargetId": {"description": "Optional. The source target ID.", "type": "string"}}, "type": "object"}, "TargetsPresentCondition": {"description": "`TargetsPresentCondition` contains information on any Targets referenced in the Delivery Pipeline that do not actually exist.", "id": "TargetsPresentCondition", "properties": {"missingTargets": {"description": "The list of Target names that do not exist. For example, `projects/{project_id}/locations/{location_name}/targets/{target_name}`.", "items": {"type": "string"}, "type": "array"}, "status": {"description": "True if there aren't any missing Targets.", "type": "boolean"}, "updateTime": {"description": "Last time the condition was updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "TargetsTypeCondition": {"description": "TargetsTypeCondition contains information on whether the Targets defined in the Delivery Pipeline are of the same type.", "id": "TargetsTypeCondition", "properties": {"errorDetails": {"description": "Human readable error message.", "type": "string"}, "status": {"description": "True if the targets are all a comparable type. For example this is true if all targets are GKE clusters. This is false if some targets are Cloud Run targets and others are GKE clusters.", "type": "boolean"}}, "type": "object"}, "TerminateJobRunRequest": {"description": "The request object used by `TerminateJobRun`.", "id": "TerminateJobRunRequest", "properties": {"overrideDeployPolicy": {"description": "Optional. Deploy policies to override. Format is `projects/{project}/locations/{location}/deployPolicies/{deployPolicy}`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TerminateJobRunResponse": {"description": "The response object from `TerminateJobRun`.", "id": "TerminateJobRunResponse", "properties": {}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "TimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "TimeWindows": {"description": "Time windows within which actions are restricted. See the [documentation](https://cloud.google.com/deploy/docs/deploy-policy#dates_times) for more information on how to configure dates/times.", "id": "TimeWindows", "properties": {"oneTimeWindows": {"description": "Optional. One-time windows within which actions are restricted.", "items": {"$ref": "OneTimeWindow"}, "type": "array"}, "timeZone": {"description": "Required. The time zone in IANA format [IANA Time Zone Database](https://www.iana.org/time-zones) (e.g. America/New_York).", "type": "string"}, "weeklyWindows": {"description": "Optional. Recurring weekly windows within which actions are restricted.", "items": {"$ref": "WeeklyWindow"}, "type": "array"}}, "type": "object"}, "TimedPromoteReleaseCondition": {"description": "`TimedPromoteReleaseCondition` contains conditions specific to an Automation with a Timed Promote Release rule defined.", "id": "TimedPromoteReleaseCondition", "properties": {"nextPromotionTime": {"description": "Output only. When the next scheduled promotion(s) will occur.", "format": "google-datetime", "readOnly": true, "type": "string"}, "targetsList": {"description": "Output only. A list of targets involved in the upcoming timed promotion(s).", "items": {"$ref": "Targets"}, "readOnly": true, "type": "array"}}, "type": "object"}, "TimedPromoteReleaseOperation": {"description": "Contains the information of an automated timed promote-release operation.", "id": "TimedPromoteReleaseOperation", "properties": {"phase": {"description": "Output only. The starting phase of the rollout created by this operation.", "readOnly": true, "type": "string"}, "release": {"description": "Output only. The name of the release to be promoted.", "readOnly": true, "type": "string"}, "targetId": {"description": "Output only. The ID of the target that represents the promotion stage to which the release will be promoted. The value of this field is the last segment of a target name.", "readOnly": true, "type": "string"}}, "type": "object"}, "TimedPromoteReleaseRule": {"description": "The `TimedPromoteReleaseRule` will automatically promote a release from the current target(s) to the specified target(s) on a configured schedule.", "id": "TimedPromoteReleaseRule", "properties": {"condition": {"$ref": "AutomationRuleCondition", "description": "Output only. Information around the state of the Automation rule.", "readOnly": true}, "destinationPhase": {"description": "Optional. The starting phase of the rollout created by this rule. Default to the first phase.", "type": "string"}, "destinationTargetId": {"description": "Optional. The ID of the stage in the pipeline to which this `Release` is deploying. If unspecified, default it to the next stage in the promotion flow. The value of this field could be one of the following: * The last segment of a target name * \"@next\", the next target in the promotion sequence", "type": "string"}, "id": {"description": "Required. ID of the rule. This ID must be unique in the `Automation` resource to which this rule belongs. The format is `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "type": "string"}, "schedule": {"description": "Required. Schedule in crontab format. e.g. \"0 9 * * 1\" for every Monday at 9am.", "type": "string"}, "timeZone": {"description": "Required. The time zone in IANA format [IANA Time Zone Database](https://www.iana.org/time-zones) (e.g. America/New_York).", "type": "string"}}, "type": "object"}, "VerifyJob": {"description": "A verify Job.", "id": "VerifyJob", "properties": {}, "type": "object"}, "VerifyJobRun": {"description": "VerifyJobRun contains information specific to a verify `JobRun`.", "id": "VerifyJobRun", "properties": {"artifactUri": {"description": "Output only. URI of a directory containing the verify artifacts. This contains the Skaffold event log.", "readOnly": true, "type": "string"}, "build": {"description": "Output only. The resource name of the Cloud Build `Build` object that is used to verify. Format is `projects/{project}/locations/{location}/builds/{build}`.", "readOnly": true, "type": "string"}, "eventLogPath": {"description": "Output only. File path of the Skaffold event log relative to the artifact URI.", "readOnly": true, "type": "string"}, "failureCause": {"description": "Output only. The reason the verify failed. This will always be unspecified while the verify is in progress or if it succeeded.", "enum": ["FAILURE_CAUSE_UNSPECIFIED", "CLOUD_BUILD_UNAVAILABLE", "EXECUTION_FAILED", "DEADLINE_EXCEEDED", "VERIFICATION_CONFIG_NOT_FOUND", "CLOUD_BUILD_REQUEST_FAILED"], "enumDescriptions": ["No reason for failure is specified.", "Cloud Build is not available, either because it is not enabled or because Cloud Deploy has insufficient permissions. See [required permission](https://cloud.google.com/deploy/docs/cloud-deploy-service-account#required_permissions).", "The verify operation did not complete successfully; check Cloud Build logs.", "The verify job run did not complete within the allotted time.", "No Skaffold verify configuration was found.", "Cloud Build failed to fulfill Cloud Deploy's request. See failure_message for additional details."], "readOnly": true, "type": "string"}, "failureMessage": {"description": "Output only. Additional information about the verify failure, if available.", "readOnly": true, "type": "string"}}, "type": "object"}, "WeeklyWindow": {"description": "Weekly windows. For example, blocking actions every Saturday and Sunday. Another example would be blocking actions every weekday from 5pm to midnight.", "id": "WeeklyWindow", "properties": {"daysOfWeek": {"description": "Optional. Days of week. If left empty, all days of the week will be included.", "items": {"enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "type": "array"}, "endTime": {"$ref": "TimeOfDay", "description": "Optional. End time (exclusive). Use 24:00 to indicate midnight. If you specify end_time you must also specify start_time. If left empty, this will block for the entire day for the days specified in days_of_week."}, "startTime": {"$ref": "TimeOfDay", "description": "Optional. Start time (inclusive). Use 00:00 for the beginning of the day. If you specify start_time you must also specify end_time. If left empty, this will block for the entire day for the days specified in days_of_week."}}, "type": "object"}}, "servicePath": "", "title": "Cloud Deploy API", "version": "v1", "version_module": true}