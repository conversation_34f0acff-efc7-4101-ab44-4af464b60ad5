{"ast": null, "code": "import React__default, { useContext } from 'react';\nimport { usePresence } from '../../../components/AnimatePresence/use-presence.mjs';\nimport { LayoutGroupContext } from '../../../context/LayoutGroupContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../../context/SwitchLayoutGroupContext.mjs';\nimport { globalProjectionState } from '../../../projection/node/state.mjs';\nimport { correctBorderRadius } from '../../../projection/styles/scale-border-radius.mjs';\nimport { correctBoxShadow } from '../../../projection/styles/scale-box-shadow.mjs';\nimport { addScaleCorrector } from '../../../projection/styles/scale-correction.mjs';\nimport { frame } from '../../../frameloop/frame.mjs';\nclass MeasureLayoutWithContext extends React__default.Component {\n  /**\n   * This only mounts projection nodes for components that\n   * need measuring, we might want to do it for all components\n   * in order to incorporate transforms\n   */\n  componentDidMount() {\n    const {\n      visualElement,\n      layoutGroup,\n      switchLayoutGroup,\n      layoutId\n    } = this.props;\n    const {\n      projection\n    } = visualElement;\n    addScaleCorrector(defaultScaleCorrectors);\n    if (projection) {\n      if (layoutGroup.group) layoutGroup.group.add(projection);\n      if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n        switchLayoutGroup.register(projection);\n      }\n      projection.root.didUpdate();\n      projection.addEventListener(\"animationComplete\", () => {\n        this.safeToRemove();\n      });\n      projection.setOptions({\n        ...projection.options,\n        onExitComplete: () => this.safeToRemove()\n      });\n    }\n    globalProjectionState.hasEverUpdated = true;\n  }\n  getSnapshotBeforeUpdate(prevProps) {\n    const {\n      layoutDependency,\n      visualElement,\n      drag,\n      isPresent\n    } = this.props;\n    const projection = visualElement.projection;\n    if (!projection) return null;\n    /**\n     * TODO: We use this data in relegate to determine whether to\n     * promote a previous element. There's no guarantee its presence data\n     * will have updated by this point - if a bug like this arises it will\n     * have to be that we markForRelegation and then find a new lead some other way,\n     * perhaps in didUpdate\n     */\n    projection.isPresent = isPresent;\n    if (drag || prevProps.layoutDependency !== layoutDependency || layoutDependency === undefined) {\n      projection.willUpdate();\n    } else {\n      this.safeToRemove();\n    }\n    if (prevProps.isPresent !== isPresent) {\n      if (isPresent) {\n        projection.promote();\n      } else if (!projection.relegate()) {\n        /**\n         * If there's another stack member taking over from this one,\n         * it's in charge of the exit animation and therefore should\n         * be in charge of the safe to remove. Otherwise we call it here.\n         */\n        frame.postRender(() => {\n          const stack = projection.getStack();\n          if (!stack || !stack.members.length) {\n            this.safeToRemove();\n          }\n        });\n      }\n    }\n    return null;\n  }\n  componentDidUpdate() {\n    const {\n      projection\n    } = this.props.visualElement;\n    if (projection) {\n      projection.root.didUpdate();\n      queueMicrotask(() => {\n        if (!projection.currentAnimation && projection.isLead()) {\n          this.safeToRemove();\n        }\n      });\n    }\n  }\n  componentWillUnmount() {\n    const {\n      visualElement,\n      layoutGroup,\n      switchLayoutGroup: promoteContext\n    } = this.props;\n    const {\n      projection\n    } = visualElement;\n    if (projection) {\n      projection.scheduleCheckAfterUnmount();\n      if (layoutGroup && layoutGroup.group) layoutGroup.group.remove(projection);\n      if (promoteContext && promoteContext.deregister) promoteContext.deregister(projection);\n    }\n  }\n  safeToRemove() {\n    const {\n      safeToRemove\n    } = this.props;\n    safeToRemove && safeToRemove();\n  }\n  render() {\n    return null;\n  }\n}\nfunction MeasureLayout(props) {\n  const [isPresent, safeToRemove] = usePresence();\n  const layoutGroup = useContext(LayoutGroupContext);\n  return React__default.createElement(MeasureLayoutWithContext, {\n    ...props,\n    layoutGroup: layoutGroup,\n    switchLayoutGroup: useContext(SwitchLayoutGroupContext),\n    isPresent: isPresent,\n    safeToRemove: safeToRemove\n  });\n}\nconst defaultScaleCorrectors = {\n  borderRadius: {\n    ...correctBorderRadius,\n    applyTo: [\"borderTopLeftRadius\", \"borderTopRightRadius\", \"borderBottomLeftRadius\", \"borderBottomRightRadius\"]\n  },\n  borderTopLeftRadius: correctBorderRadius,\n  borderTopRightRadius: correctBorderRadius,\n  borderBottomLeftRadius: correctBorderRadius,\n  borderBottomRightRadius: correctBorderRadius,\n  boxShadow: correctBoxShadow\n};\nexport { MeasureLayout };", "map": {"version": 3, "names": ["React__default", "useContext", "usePresence", "LayoutGroupContext", "SwitchLayoutGroupContext", "globalProjectionState", "correctBorderRadius", "correctBoxShadow", "addScaleCorrector", "frame", "MeasureLayoutWithContext", "Component", "componentDidMount", "visualElement", "layoutGroup", "switchLayoutGroup", "layoutId", "props", "projection", "defaultScaleCorrectors", "group", "add", "register", "root", "didUpdate", "addEventListener", "safeToRemove", "setOptions", "options", "onExitComplete", "hasEverUpdated", "getSnapshotBeforeUpdate", "prevProps", "layoutDependency", "drag", "isPresent", "undefined", "willUpdate", "promote", "relegate", "postRender", "stack", "getStack", "members", "length", "componentDidUpdate", "queueMicrotask", "currentAnimation", "isLead", "componentWillUnmount", "promoteContext", "scheduleCheckAfterUnmount", "remove", "deregister", "render", "MeasureLayout", "createElement", "borderRadius", "applyTo", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "boxShadow"], "sources": ["D:/Projects/ai-hr-agent/frontend/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs"], "sourcesContent": ["import React__default, { useContext } from 'react';\nimport { usePresence } from '../../../components/AnimatePresence/use-presence.mjs';\nimport { LayoutGroupContext } from '../../../context/LayoutGroupContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../../context/SwitchLayoutGroupContext.mjs';\nimport { globalProjectionState } from '../../../projection/node/state.mjs';\nimport { correctBorderRadius } from '../../../projection/styles/scale-border-radius.mjs';\nimport { correctBoxShadow } from '../../../projection/styles/scale-box-shadow.mjs';\nimport { addScaleCorrector } from '../../../projection/styles/scale-correction.mjs';\nimport { frame } from '../../../frameloop/frame.mjs';\n\nclass MeasureLayoutWithContext extends React__default.Component {\n    /**\n     * This only mounts projection nodes for components that\n     * need measuring, we might want to do it for all components\n     * in order to incorporate transforms\n     */\n    componentDidMount() {\n        const { visualElement, layoutGroup, switchLayoutGroup, layoutId } = this.props;\n        const { projection } = visualElement;\n        addScaleCorrector(defaultScaleCorrectors);\n        if (projection) {\n            if (layoutGroup.group)\n                layoutGroup.group.add(projection);\n            if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n                switchLayoutGroup.register(projection);\n            }\n            projection.root.didUpdate();\n            projection.addEventListener(\"animationComplete\", () => {\n                this.safeToRemove();\n            });\n            projection.setOptions({\n                ...projection.options,\n                onExitComplete: () => this.safeToRemove(),\n            });\n        }\n        globalProjectionState.hasEverUpdated = true;\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        const { layoutDependency, visualElement, drag, isPresent } = this.props;\n        const projection = visualElement.projection;\n        if (!projection)\n            return null;\n        /**\n         * TODO: We use this data in relegate to determine whether to\n         * promote a previous element. There's no guarantee its presence data\n         * will have updated by this point - if a bug like this arises it will\n         * have to be that we markForRelegation and then find a new lead some other way,\n         * perhaps in didUpdate\n         */\n        projection.isPresent = isPresent;\n        if (drag ||\n            prevProps.layoutDependency !== layoutDependency ||\n            layoutDependency === undefined) {\n            projection.willUpdate();\n        }\n        else {\n            this.safeToRemove();\n        }\n        if (prevProps.isPresent !== isPresent) {\n            if (isPresent) {\n                projection.promote();\n            }\n            else if (!projection.relegate()) {\n                /**\n                 * If there's another stack member taking over from this one,\n                 * it's in charge of the exit animation and therefore should\n                 * be in charge of the safe to remove. Otherwise we call it here.\n                 */\n                frame.postRender(() => {\n                    const stack = projection.getStack();\n                    if (!stack || !stack.members.length) {\n                        this.safeToRemove();\n                    }\n                });\n            }\n        }\n        return null;\n    }\n    componentDidUpdate() {\n        const { projection } = this.props.visualElement;\n        if (projection) {\n            projection.root.didUpdate();\n            queueMicrotask(() => {\n                if (!projection.currentAnimation && projection.isLead()) {\n                    this.safeToRemove();\n                }\n            });\n        }\n    }\n    componentWillUnmount() {\n        const { visualElement, layoutGroup, switchLayoutGroup: promoteContext, } = this.props;\n        const { projection } = visualElement;\n        if (projection) {\n            projection.scheduleCheckAfterUnmount();\n            if (layoutGroup && layoutGroup.group)\n                layoutGroup.group.remove(projection);\n            if (promoteContext && promoteContext.deregister)\n                promoteContext.deregister(projection);\n        }\n    }\n    safeToRemove() {\n        const { safeToRemove } = this.props;\n        safeToRemove && safeToRemove();\n    }\n    render() {\n        return null;\n    }\n}\nfunction MeasureLayout(props) {\n    const [isPresent, safeToRemove] = usePresence();\n    const layoutGroup = useContext(LayoutGroupContext);\n    return (React__default.createElement(MeasureLayoutWithContext, { ...props, layoutGroup: layoutGroup, switchLayoutGroup: useContext(SwitchLayoutGroupContext), isPresent: isPresent, safeToRemove: safeToRemove }));\n}\nconst defaultScaleCorrectors = {\n    borderRadius: {\n        ...correctBorderRadius,\n        applyTo: [\n            \"borderTopLeftRadius\",\n            \"borderTopRightRadius\",\n            \"borderBottomLeftRadius\",\n            \"borderBottomRightRadius\",\n        ],\n    },\n    borderTopLeftRadius: correctBorderRadius,\n    borderTopRightRadius: correctBorderRadius,\n    borderBottomLeftRadius: correctBorderRadius,\n    borderBottomRightRadius: correctBorderRadius,\n    boxShadow: correctBoxShadow,\n};\n\nexport { MeasureLayout };\n"], "mappings": "AAAA,OAAOA,cAAc,IAAIC,UAAU,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,sDAAsD;AAClF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,mBAAmB,QAAQ,oDAAoD;AACxF,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,iBAAiB,QAAQ,iDAAiD;AACnF,SAASC,KAAK,QAAQ,8BAA8B;AAEpD,MAAMC,wBAAwB,SAASV,cAAc,CAACW,SAAS,CAAC;EAC5D;AACJ;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEC,aAAa;MAAEC,WAAW;MAAEC,iBAAiB;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACC,KAAK;IAC9E,MAAM;MAAEC;IAAW,CAAC,GAAGL,aAAa;IACpCL,iBAAiB,CAACW,sBAAsB,CAAC;IACzC,IAAID,UAAU,EAAE;MACZ,IAAIJ,WAAW,CAACM,KAAK,EACjBN,WAAW,CAACM,KAAK,CAACC,GAAG,CAACH,UAAU,CAAC;MACrC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACO,QAAQ,IAAIN,QAAQ,EAAE;QAC7DD,iBAAiB,CAACO,QAAQ,CAACJ,UAAU,CAAC;MAC1C;MACAA,UAAU,CAACK,IAAI,CAACC,SAAS,CAAC,CAAC;MAC3BN,UAAU,CAACO,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;QACnD,IAAI,CAACC,YAAY,CAAC,CAAC;MACvB,CAAC,CAAC;MACFR,UAAU,CAACS,UAAU,CAAC;QAClB,GAAGT,UAAU,CAACU,OAAO;QACrBC,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACH,YAAY,CAAC;MAC5C,CAAC,CAAC;IACN;IACArB,qBAAqB,CAACyB,cAAc,GAAG,IAAI;EAC/C;EACAC,uBAAuBA,CAACC,SAAS,EAAE;IAC/B,MAAM;MAAEC,gBAAgB;MAAEpB,aAAa;MAAEqB,IAAI;MAAEC;IAAU,CAAC,GAAG,IAAI,CAAClB,KAAK;IACvE,MAAMC,UAAU,GAAGL,aAAa,CAACK,UAAU;IAC3C,IAAI,CAACA,UAAU,EACX,OAAO,IAAI;IACf;AACR;AACA;AACA;AACA;AACA;AACA;IACQA,UAAU,CAACiB,SAAS,GAAGA,SAAS;IAChC,IAAID,IAAI,IACJF,SAAS,CAACC,gBAAgB,KAAKA,gBAAgB,IAC/CA,gBAAgB,KAAKG,SAAS,EAAE;MAChClB,UAAU,CAACmB,UAAU,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACX,YAAY,CAAC,CAAC;IACvB;IACA,IAAIM,SAAS,CAACG,SAAS,KAAKA,SAAS,EAAE;MACnC,IAAIA,SAAS,EAAE;QACXjB,UAAU,CAACoB,OAAO,CAAC,CAAC;MACxB,CAAC,MACI,IAAI,CAACpB,UAAU,CAACqB,QAAQ,CAAC,CAAC,EAAE;QAC7B;AAChB;AACA;AACA;AACA;QACgB9B,KAAK,CAAC+B,UAAU,CAAC,MAAM;UACnB,MAAMC,KAAK,GAAGvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;UACnC,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACE,OAAO,CAACC,MAAM,EAAE;YACjC,IAAI,CAAClB,YAAY,CAAC,CAAC;UACvB;QACJ,CAAC,CAAC;MACN;IACJ;IACA,OAAO,IAAI;EACf;EACAmB,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAE3B;IAAW,CAAC,GAAG,IAAI,CAACD,KAAK,CAACJ,aAAa;IAC/C,IAAIK,UAAU,EAAE;MACZA,UAAU,CAACK,IAAI,CAACC,SAAS,CAAC,CAAC;MAC3BsB,cAAc,CAAC,MAAM;QACjB,IAAI,CAAC5B,UAAU,CAAC6B,gBAAgB,IAAI7B,UAAU,CAAC8B,MAAM,CAAC,CAAC,EAAE;UACrD,IAAI,CAACtB,YAAY,CAAC,CAAC;QACvB;MACJ,CAAC,CAAC;IACN;EACJ;EACAuB,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAEpC,aAAa;MAAEC,WAAW;MAAEC,iBAAiB,EAAEmC;IAAgB,CAAC,GAAG,IAAI,CAACjC,KAAK;IACrF,MAAM;MAAEC;IAAW,CAAC,GAAGL,aAAa;IACpC,IAAIK,UAAU,EAAE;MACZA,UAAU,CAACiC,yBAAyB,CAAC,CAAC;MACtC,IAAIrC,WAAW,IAAIA,WAAW,CAACM,KAAK,EAChCN,WAAW,CAACM,KAAK,CAACgC,MAAM,CAAClC,UAAU,CAAC;MACxC,IAAIgC,cAAc,IAAIA,cAAc,CAACG,UAAU,EAC3CH,cAAc,CAACG,UAAU,CAACnC,UAAU,CAAC;IAC7C;EACJ;EACAQ,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEA;IAAa,CAAC,GAAG,IAAI,CAACT,KAAK;IACnCS,YAAY,IAAIA,YAAY,CAAC,CAAC;EAClC;EACA4B,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI;EACf;AACJ;AACA,SAASC,aAAaA,CAACtC,KAAK,EAAE;EAC1B,MAAM,CAACkB,SAAS,EAAET,YAAY,CAAC,GAAGxB,WAAW,CAAC,CAAC;EAC/C,MAAMY,WAAW,GAAGb,UAAU,CAACE,kBAAkB,CAAC;EAClD,OAAQH,cAAc,CAACwD,aAAa,CAAC9C,wBAAwB,EAAE;IAAE,GAAGO,KAAK;IAAEH,WAAW,EAAEA,WAAW;IAAEC,iBAAiB,EAAEd,UAAU,CAACG,wBAAwB,CAAC;IAAE+B,SAAS,EAAEA,SAAS;IAAET,YAAY,EAAEA;EAAa,CAAC,CAAC;AACrN;AACA,MAAMP,sBAAsB,GAAG;EAC3BsC,YAAY,EAAE;IACV,GAAGnD,mBAAmB;IACtBoD,OAAO,EAAE,CACL,qBAAqB,EACrB,sBAAsB,EACtB,wBAAwB,EACxB,yBAAyB;EAEjC,CAAC;EACDC,mBAAmB,EAAErD,mBAAmB;EACxCsD,oBAAoB,EAAEtD,mBAAmB;EACzCuD,sBAAsB,EAAEvD,mBAAmB;EAC3CwD,uBAAuB,EAAExD,mBAAmB;EAC5CyD,SAAS,EAAExD;AACf,CAAC;AAED,SAASgD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}