{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ArrowUpAZ = createLucideIcon(\"ArrowUpAZ\", [[\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}], [\"path\", {\n  d: \"M20 8h-5\",\n  key: \"1vsyxs\"\n}], [\"path\", {\n  d: \"M15 10V6.5a2.5 2.5 0 0 1 5 0V10\",\n  key: \"ag13bf\"\n}], [\"path\", {\n  d: \"M15 14h5l-5 6h5\",\n  key: \"ur5jdg\"\n}]]);\nexport { ArrowUpAZ as default };", "map": {"version": 3, "names": ["ArrowUpAZ", "createLucideIcon", "d", "key"], "sources": ["D:\\Projects\\ai-hr-agent\\frontend\\node_modules\\lucide-react\\src\\icons\\arrow-up-a-z.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowUpAZ\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyA4IDQtNCA0IDQiIC8+CiAgPHBhdGggZD0iTTcgNHYxNiIgLz4KICA8cGF0aCBkPSJNMjAgOGgtNSIgLz4KICA8cGF0aCBkPSJNMTUgMTBWNi41YTIuNSAyLjUgMCAwIDEgNSAwVjEwIiAvPgogIDxwYXRoIGQ9Ik0xNSAxNGg1bC01IDZoNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-up-a-z\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUpAZ = createLucideIcon('ArrowUpAZ', [\n  ['path', { d: 'm3 8 4-4 4 4', key: '11wl7u' }],\n  ['path', { d: 'M7 4v16', key: '1glfcx' }],\n  ['path', { d: 'M20 8h-5', key: '1vsyxs' }],\n  ['path', { d: 'M15 10V6.5a2.5 2.5 0 0 1 5 0V10', key: 'ag13bf' }],\n  ['path', { d: 'M15 14h5l-5 6h5', key: 'ur5jdg' }],\n]);\n\nexport default ArrowUpAZ;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}