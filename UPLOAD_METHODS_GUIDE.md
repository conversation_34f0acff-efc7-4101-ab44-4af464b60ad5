# ResumeGPT Pro - Multiple Upload Methods Guide

## Overview

ResumeGPT Pro now supports three different methods for uploading and processing resumes:

1. **Direct File Upload** - Traditional file upload from your computer
2. **Google Drive Folder Integration** - Connect to Google Drive and process entire folders
3. **Excel Bulk Upload** - Upload Excel files with candidate names and Google Drive links

## 🚀 Features Implemented

### ✅ Google Drive Folder Integration
- **OAuth2 Authentication**: Secure Google Drive account connection
- **Folder Browsing**: Select specific folders containing resume files
- **Automatic PDF Detection**: Automatically finds and processes all PDF files in the folder
- **Bulk Processing**: Downloads and processes multiple files simultaneously
- **Progress Tracking**: Real-time status updates during folder scanning and processing
- **Error Handling**: Comprehensive error handling for authentication and API failures

### ✅ Excel Sheet Bulk Upload
- **Excel File Support**: Supports both .xlsx and .xls formats
- **Two-Column Format**: Validates exact column structure (Name, Resume Link)
- **Google Drive Link Processing**: Handles various Google Drive URL formats
- **Bulk Download**: Automatically downloads resumes from provided links
- **Validation Results**: Shows detailed validation with error reporting
- **Name Association**: Associates downloaded resumes with candidate names

### ✅ Enhanced Backend API
- **Multi-Method Support**: Single `/analyze` endpoint handles all upload methods
- **Google Drive API Integration**: Full Google Drive API v3 implementation
- **Excel Processing**: Advanced Excel parsing with pandas and openpyxl
- **Error Handling**: Comprehensive error handling and validation
- **Security**: Secure token storage and OAuth flow management

## 📋 Setup Instructions

### 1. Google Drive Integration Setup

#### Prerequisites
- Google Cloud Project
- Google Drive API enabled
- OAuth 2.0 credentials configured

#### Step-by-Step Setup

1. **Create Google Cloud Project**
   ```
   1. Go to https://console.cloud.google.com/
   2. Create new project or select existing one
   3. Note the project ID
   ```

2. **Enable Google Drive API**
   ```
   1. Navigate to "APIs & Services" > "Library"
   2. Search for "Google Drive API"
   3. Click "Enable"
   ```

3. **Create OAuth 2.0 Credentials**
   ```
   1. Go to "APIs & Services" > "Credentials"
   2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
   3. Choose "Web application"
   4. Add authorized redirect URIs:
      - http://localhost:8000/auth/google/callback
      - http://localhost:3000
   ```

4. **Download and Configure Credentials**
   ```
   1. Download the JSON credentials file
   2. Rename to `client_secrets.json`
   3. Place in `backend/google_credentials/` directory
   ```

### 2. Install Dependencies

#### Backend Dependencies
```bash
cd backend
pip install -r requirements.txt
```

#### Frontend Dependencies
```bash
cd frontend
npm install
```

### 3. Start the Application

#### Start Backend Server
```bash
cd backend
python api.py
```

#### Start Frontend Server
```bash
cd frontend
npm start
```

## 🎯 How to Use Each Upload Method

### Method 1: Direct File Upload
1. Navigate to the Analysis page
2. Select "Direct File Upload"
3. Drag and drop files or click to browse
4. Supported formats: PDF, DOC, DOCX, TXT
5. Fill in job requirements
6. Click "Start Analysis"

### Method 2: Google Drive Folder
1. Navigate to the Analysis page
2. Select "Google Drive Folder"
3. Click "Connect Google Drive"
4. Complete OAuth authentication in popup
5. Enter Google Drive folder ID
6. Click "Load Files" to scan folder
7. Review found PDF files
8. Fill in job requirements
9. Click "Start Analysis"

#### How to Get Google Drive Folder ID
1. Open Google Drive in browser
2. Navigate to desired folder
3. Copy folder ID from URL: `https://drive.google.com/drive/folders/[FOLDER_ID]`

### Method 3: Excel with Links
1. Navigate to the Analysis page
2. Select "Excel with Links"
3. Upload Excel file with correct format
4. Click "Validate" to check file structure
5. Review validation results
6. Fill in job requirements
7. Click "Start Analysis"

#### Excel File Format Requirements
- **Column 1**: `name` - Candidate's full name
- **Column 2**: `resume_link` - Google Drive shareable link

#### Supported Google Drive Link Formats
- `https://drive.google.com/file/d/FILE_ID/view`
- `https://drive.google.com/open?id=FILE_ID`
- `https://docs.google.com/document/d/FILE_ID/edit`

## 🔧 API Endpoints

### Google Drive Authentication
- `GET /auth/google/url` - Get OAuth authorization URL
- `GET /auth/google/callback` - Handle OAuth callback

### File Processing
- `POST /google-drive/folders/{folder_id}/files` - List folder files
- `POST /process-excel` - Validate Excel file
- `POST /analyze` - Enhanced analysis endpoint (supports all methods)

### Health Check
- `GET /health` - Check API status and enabled features
- `GET /api-status` - Detailed configuration status

## 🛡️ Security Features

### Google Drive Integration
- Secure OAuth 2.0 flow
- Token-based authentication
- Read-only access to Drive files
- Automatic token cleanup

### File Processing
- Secure file validation
- Temporary file cleanup
- Input sanitization
- Error boundary protection

## 🚨 Error Handling

### Common Issues and Solutions

#### Google Drive Connection Failed
- **Cause**: Invalid credentials or API not enabled
- **Solution**: Verify `client_secrets.json` and API enablement

#### Folder Not Found
- **Cause**: Invalid folder ID or insufficient permissions
- **Solution**: Check folder ID and sharing permissions

#### Excel Validation Failed
- **Cause**: Incorrect column structure or invalid links
- **Solution**: Verify Excel format and Google Drive link accessibility

#### File Download Failed
- **Cause**: Network issues or invalid file permissions
- **Solution**: Check internet connection and file sharing settings

## 📊 Performance Considerations

### Optimization Features
- **Parallel Processing**: Multiple files processed simultaneously
- **Async Operations**: Non-blocking file downloads and processing
- **Progress Tracking**: Real-time status updates
- **Memory Management**: Efficient file handling and cleanup
- **Caching**: Response caching for improved performance

### Recommended Limits
- **Direct Upload**: Up to 50 files per batch
- **Google Drive**: Up to 100 files per folder
- **Excel Upload**: Up to 200 rows per file

## 🔍 Testing

### Test Google Drive Integration
1. Create test folder in Google Drive
2. Add sample PDF files
3. Test connection and file listing
4. Verify file download and processing

### Test Excel Upload
1. Create test Excel file with sample data
2. Include valid and invalid Google Drive links
3. Test validation and error reporting
4. Verify bulk download functionality

## 📝 Troubleshooting

### Backend Issues
- Check server logs for detailed error messages
- Verify all environment variables are set
- Ensure Google credentials are properly configured

### Frontend Issues
- Check browser console for JavaScript errors
- Verify API endpoints are accessible
- Test OAuth popup functionality

### Google Drive Issues
- Verify API quotas and limits
- Check file permissions and sharing settings
- Ensure folder IDs are correct

## 🎉 Success Indicators

When everything is working correctly, you should see:
- ✅ Google Drive connection successful
- ✅ Files listed from Google Drive folder
- ✅ Excel validation completed with valid rows
- ✅ Analysis processing with progress updates
- ✅ Comprehensive results with candidate insights

## 📞 Support

For additional support or questions:
1. Check the console logs for detailed error messages
2. Verify all setup steps have been completed
3. Test with sample data before processing real files
4. Ensure all dependencies are properly installed
