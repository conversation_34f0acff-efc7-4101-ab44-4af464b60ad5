#!/usr/bin/env python3
"""
Test script for ResumeGPT Pro upload methods
Tests all three upload methods: files, Google Drive, and Excel
"""

import requests
import json
import os
import time
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 30

def test_api_health():
    """Test if the API is running and healthy"""
    print("🔍 Testing API health...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API is healthy")
            print(f"   Version: {data.get('version')}")
            print(f"   Features: {list(data.get('features', {}).keys())}")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API connection failed: {e}")
        return False

def test_api_status():
    """Test API configuration status"""
    print("\n🔍 Testing API configuration...")
    try:
        response = requests.get(f"{BASE_URL}/api-status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API configuration status:")
            for key, value in data.items():
                status = "✅" if value else "❌"
                print(f"   {status} {key}: {value}")
            return data
        else:
            print(f"❌ API status check failed: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ API status check failed: {e}")
        return None

def test_google_auth_url():
    """Test Google OAuth URL generation"""
    print("\n🔍 Testing Google OAuth URL generation...")
    try:
        response = requests.get(f"{BASE_URL}/auth/google/url", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('auth_url'):
                print("✅ Google OAuth URL generated successfully")
                print(f"   URL: {data['auth_url'][:50]}...")
                return True
            else:
                print("❌ No auth URL in response")
                return False
        else:
            print(f"❌ Google OAuth URL generation failed: {response.status_code}")
            if response.status_code == 500:
                print("   💡 This likely means client_secrets.json is missing")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Google OAuth URL test failed: {e}")
        return False

def test_excel_processing():
    """Test Excel file processing with sample data"""
    print("\n🔍 Testing Excel file processing...")
    
    # Create a sample Excel file content
    sample_excel_data = """name,resume_link
John Doe,https://drive.google.com/file/d/1ABC123/view
Jane Smith,https://drive.google.com/open?id=1XYZ789
Invalid Row,not-a-valid-url
Bob Johnson,https://docs.google.com/document/d/1QWE456/edit"""
    
    try:
        # Create temporary CSV file (will be converted to Excel by pandas)
        import pandas as pd
        import io
        
        # Convert CSV to Excel format
        df = pd.read_csv(io.StringIO(sample_excel_data))
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False)
        excel_buffer.seek(0)
        
        # Test the Excel processing endpoint
        files = {'file': ('test_resumes.xlsx', excel_buffer.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
        response = requests.post(f"{BASE_URL}/process-excel", files=files, timeout=TEST_TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                result_data = data.get('data', {})
                print("✅ Excel processing successful")
                print(f"   Total rows: {result_data.get('total_rows', 0)}")
                print(f"   Valid rows: {result_data.get('valid_rows', 0)}")
                print(f"   Invalid rows: {len(result_data.get('invalid_rows', []))}")
                return True
            else:
                print(f"❌ Excel processing failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Excel processing request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}")
            return False
            
    except ImportError:
        print("❌ pandas not available for Excel testing")
        return False
    except Exception as e:
        print(f"❌ Excel processing test failed: {e}")
        return False

def test_file_upload_endpoint():
    """Test the main analyze endpoint with file upload method"""
    print("\n🔍 Testing file upload endpoint...")
    
    # Create a sample text file
    sample_resume = """
John Doe
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE:
- 5 years of Python development
- Full-stack web development
- Machine learning projects

EDUCATION:
- Bachelor's in Computer Science
- Master's in Data Science

SKILLS:
- Python, JavaScript, React
- Machine Learning, AI
- Database design
"""
    
    try:
        # Prepare form data
        form_data = {
            'position_title': 'Senior Software Engineer',
            'job_description': 'Looking for an experienced software engineer with Python and ML skills',
            'upload_method': 'files',
            'required_skills': 'Python, Machine Learning',
            'experience_level': 'Senior',
            'company_name': 'Test Company'
        }
        
        # Prepare file
        files = {'files': ('test_resume.txt', sample_resume, 'text/plain')}
        
        # Note: This will likely fail without proper API keys, but we can test the endpoint
        response = requests.post(f"{BASE_URL}/analyze", data=form_data, files=files, timeout=TEST_TIMEOUT)
        
        if response.status_code == 200:
            print("✅ File upload endpoint accessible")
            return True
        elif response.status_code == 500:
            # Expected if API keys are not configured
            try:
                error_data = response.json()
                if "API key not configured" in error_data.get('detail', ''):
                    print("✅ File upload endpoint accessible (API keys needed for full processing)")
                    return True
                else:
                    print(f"❌ File upload failed: {error_data.get('detail', 'Unknown error')}")
                    return False
            except:
                print("❌ File upload failed with server error")
                return False
        else:
            print(f"❌ File upload request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ File upload test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("🚀 Starting ResumeGPT Pro Upload Methods Test Suite")
    print("=" * 60)
    
    tests = [
        ("API Health", test_api_health),
        ("API Configuration", test_api_status),
        ("Google OAuth URL", test_google_auth_url),
        ("Excel Processing", test_excel_processing),
        ("File Upload Endpoint", test_file_upload_endpoint),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The upload methods are ready to use.")
    else:
        print("⚠️  Some tests failed. Check the configuration and setup.")
        
        # Provide specific guidance
        if not results.get("API Health"):
            print("\n💡 Make sure the backend server is running: python api.py")
        
        if not results.get("Google OAuth URL"):
            print("\n💡 For Google Drive integration:")
            print("   1. Add client_secrets.json to backend/google_credentials/")
            print("   2. Follow the setup guide in UPLOAD_METHODS_GUIDE.md")
        
        if not results.get("Excel Processing"):
            print("\n💡 For Excel processing:")
            print("   1. Install pandas: pip install pandas openpyxl")
            print("   2. Check the requirements.txt file")

if __name__ == "__main__":
    run_all_tests()
