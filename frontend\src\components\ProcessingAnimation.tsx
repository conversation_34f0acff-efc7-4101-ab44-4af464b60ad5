import React from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Search, 
  CheckCircle, 
  BarChart3, 
  FileText, 
  Shield,
  Zap,
  Clock
} from 'lucide-react';

interface ProcessingAnimationProps {
  progress: number;
  currentStep: string;
  filesCount: number;
}

const ProcessingAnimation: React.FC<ProcessingAnimationProps> = ({
  progress,
  currentStep,
  filesCount
}) => {
  const steps = [
    {
      id: 'upload',
      title: 'File Upload',
      description: 'Uploading and processing resume files',
      icon: FileText,
      color: 'text-blue-400',
      bgColor: 'bg-blue-400/20',
      progress: 10
    },
    {
      id: 'extraction',
      title: 'Data Extraction',
      description: 'AI-powered resume parsing and data extraction',
      icon: Brain,
      color: 'text-purple-400',
      bgColor: 'bg-purple-400/20',
      progress: 25
    },
    {
      id: 'analysis',
      title: 'Candidate Analysis',
      description: 'Analyzing candidate profiles and skills',
      icon: Zap,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-400/20',
      progress: 40
    },
    {
      id: 'matching',
      title: 'Smart Matching',
      description: 'Matching candidates against job requirements',
      icon: CheckCircle,
      color: 'text-green-400',
      bgColor: 'bg-green-400/20',
      progress: 55
    },
    {
      id: 'research',
      title: 'Deep Research',
      description: 'Researching candidates across platforms',
      icon: Search,
      color: 'text-orange-400',
      bgColor: 'bg-orange-400/20',
      progress: 75
    },
    {
      id: 'validation',
      title: 'Validation',
      description: 'Validating candidate information',
      icon: Shield,
      color: 'text-red-400',
      bgColor: 'bg-red-400/20',
      progress: 90
    },
    {
      id: 'reporting',
      title: 'Report Generation',
      description: 'Generating comprehensive reports',
      icon: BarChart3,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-400/20',
      progress: 100
    }
  ];

  const currentStepIndex = steps.findIndex(step => step.progress >= progress) || 0;

  return (
    <motion.div
      key="processing"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-4xl mx-auto"
    >
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-4">
          <span className="gradient-text">Processing Your Request</span>
        </h2>
        <p className="text-xl text-gray-300">
          Our AI agents are analyzing {filesCount} resume{filesCount !== 1 ? 's' : ''} with advanced algorithms
        </p>
      </div>

      {/* Main Processing Card */}
      <div className="glass-intense p-8 rounded-3xl mb-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="text-lg font-semibold text-gray-300">Overall Progress</span>
            <span className="text-2xl font-bold gradient-text">{Math.round(progress)}%</span>
          </div>
          <div className="progress-bar h-3 mb-2">
            <motion.div
              className="progress-fill h-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Clock className="w-4 h-4" />
            <span>{currentStep}</span>
          </div>
        </div>

        {/* Central Animation */}
        <div className="flex items-center justify-center mb-8">
          <div className="relative">
            {/* Main processing circle */}
            <motion.div
              className="w-32 h-32 rounded-full glass flex items-center justify-center relative overflow-hidden"
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <motion.div
                className={`w-16 h-16 rounded-2xl ${steps[currentStepIndex]?.bgColor} flex items-center justify-center`}
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {steps[currentStepIndex]?.icon && (() => {
                  const Icon = steps[currentStepIndex].icon;
                  return (
                    <Icon 
                      className={`w-8 h-8 ${steps[currentStepIndex].color}`} 
                    />
                  );
                })()}
              </motion.div>

              {/* Animated border */}
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-gradient-to-r from-blue-400 to-purple-400"
                animate={{ rotate: -360 }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                style={{
                  background: `conic-gradient(from 0deg, transparent 0deg, rgba(102, 126, 234, 0.5) ${progress * 3.6}deg, transparent ${progress * 3.6}deg)`
                }}
              />
            </motion.div>

            {/* Orbiting elements */}
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-4 h-4 bg-blue-400/30 rounded-full"
                style={{
                  left: '50%',
                  top: '50%',
                  transformOrigin: '0 80px'
                }}
                animate={{ rotate: 360 }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "linear",
                  delay: i * 0.5
                }}
              />
            ))}
          </div>
        </div>

        {/* Current Step Details */}
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-2">
            {steps[currentStepIndex]?.title || 'Processing'}
          </h3>
          <p className="text-gray-400">
            {steps[currentStepIndex]?.description || currentStep}
          </p>
        </div>
      </div>

      {/* Steps Timeline */}
      <div className="glass p-6 rounded-2xl">
        <h3 className="text-xl font-bold mb-6 text-center">Processing Pipeline</h3>
        <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
          {steps.map((step, index) => {
            const isCompleted = progress >= step.progress;
            const isActive = index === currentStepIndex;
            
            return (
              <motion.div
                key={step.id}
                className={`text-center transition-all duration-500 ${
                  isCompleted ? 'opacity-100' : isActive ? 'opacity-80' : 'opacity-40'
                }`}
                animate={isActive ? { scale: [1, 1.05, 1] } : { scale: 1 }}
                transition={{ duration: 2, repeat: isActive ? Infinity : 0 }}
              >
                <div className={`w-12 h-12 rounded-xl ${step.bgColor} flex items-center justify-center mx-auto mb-3 relative`}>
                  <step.icon className={`w-6 h-6 ${step.color}`} />
                  {isCompleted && (
                    <motion.div
                      className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <CheckCircle className="w-3 h-3 text-white" />
                    </motion.div>
                  )}
                </div>
                <p className="text-sm font-medium">{step.title}</p>
                <div className="mt-2 h-1 bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-blue-400 to-purple-400"
                    initial={{ width: 0 }}
                    animate={{ width: isCompleted ? '100%' : isActive ? '50%' : '0%' }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-8">
        <div className="glass p-4 rounded-xl text-center">
          <FileText className="w-8 h-8 text-blue-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">{filesCount}</div>
          <div className="text-sm text-gray-400">Files Processing</div>
        </div>
        <div className="glass p-4 rounded-xl text-center">
          <Brain className="w-8 h-8 text-purple-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">5</div>
          <div className="text-sm text-gray-400">AI Agents Active</div>
        </div>
        <div className="glass p-4 rounded-xl text-center">
          <Zap className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">50+</div>
          <div className="text-sm text-gray-400">Data Points</div>
        </div>
        <div className="glass p-4 rounded-xl text-center">
          <Clock className="w-8 h-8 text-green-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">
            {Math.round((100 - progress) * 0.3)}s
          </div>
          <div className="text-sm text-gray-400">Est. Remaining</div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProcessingAnimation;