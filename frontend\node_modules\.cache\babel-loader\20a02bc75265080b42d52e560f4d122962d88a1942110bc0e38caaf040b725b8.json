{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\ai-hr-agent\\\\frontend\\\\src\\\\pages\\\\AnalysisPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { useDropzone } from 'react-dropzone';\nimport toast from 'react-hot-toast';\nimport { ArrowLeft, Upload, FileText, X, Brain, CheckCircle, Sparkles, Target, Users } from 'lucide-react';\nimport AnimatedBackground from '../components/AnimatedBackground';\nimport ProcessingAnimation from '../components/ProcessingAnimation';\nimport ResultsDisplay from '../components/ResultsDisplay';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [step, setStep] = useState('form');\n  const [files, setFiles] = useState([]);\n  const [jobRequirements, setJobRequirements] = useState({\n    position_title: '',\n    job_description: '',\n    required_skills: '',\n    preferred_skills: '',\n    experience_level: '',\n    education_requirements: '',\n    location: '',\n    employment_type: '',\n    company_name: '',\n    keywords: ''\n  });\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [processingProgress, setProcessingProgress] = useState(0);\n  const [currentProcessingStep, setCurrentProcessingStep] = useState('');\n  const onDrop = useCallback(acceptedFiles => {\n    setFiles(prev => [...prev, ...acceptedFiles]);\n    toast.success(`${acceptedFiles.length} file(s) added successfully`);\n  }, []);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf'],\n      'application/msword': ['.doc'],\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\n      'text/plain': ['.txt']\n    },\n    multiple: true\n  });\n  const removeFile = index => {\n    setFiles(prev => prev.filter((_, i) => i !== index));\n    toast.success('File removed');\n  };\n  const handleInputChange = (field, value) => {\n    setJobRequirements(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!jobRequirements.position_title || !jobRequirements.job_description) {\n      toast.error('Please fill in position title and job description');\n      return;\n    }\n    if (files.length === 0) {\n      toast.error('Please upload at least one resume file');\n      return;\n    }\n    setStep('processing');\n    setProcessingProgress(0);\n    setCurrentProcessingStep('Initializing AI agents...');\n    try {\n      const formData = new FormData();\n\n      // Add job requirements\n      Object.entries(jobRequirements).forEach(([key, value]) => {\n        formData.append(key, value);\n      });\n\n      // Add files\n      files.forEach(file => {\n        formData.append('files', file);\n      });\n\n      // Simulate processing steps with TalentSphere AI branding\n      const steps = ['Initializing AI agents...', 'Extracting talent data...', 'Analyzing candidate profiles...', 'Matching against requirements...', 'Deep research across platforms...', 'Validating talent information...', 'Generating strategic insights...'];\n\n      // Simulate progress\n      for (let i = 0; i < steps.length; i++) {\n        setCurrentProcessingStep(steps[i]);\n        setProcessingProgress((i + 1) / steps.length * 90); // Stop at 90%\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      }\n\n      // Make API call\n      const response = await fetch('http://localhost:8000/analyze', {\n        method: 'POST',\n        body: formData\n      });\n      const result = await response.json();\n      setProcessingProgress(100);\n      setCurrentProcessingStep('Analysis complete!');\n      if (result.success) {\n        setAnalysisResult(result);\n        setStep('results');\n        toast.success('Talent analysis completed successfully!');\n      } else {\n        toast.error(result.message || 'Analysis failed');\n        setStep('form');\n      }\n    } catch (error) {\n      console.error('Analysis error:', error);\n      toast.error('Failed to analyze talent profiles. Please try again.');\n      setStep('form');\n    }\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  const handleStartNew = () => {\n    setStep('form');\n    setFiles([]);\n    setJobRequirements({\n      position_title: '',\n      job_description: '',\n      required_skills: '',\n      preferred_skills: '',\n      experience_level: '',\n      education_requirements: '',\n      location: '',\n      employment_type: '',\n      company_name: '',\n      keywords: ''\n    });\n    setAnalysisResult(null);\n    setProcessingProgress(0);\n    setCurrentProcessingStep('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(AnimatedBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"floating-particles\",\n      children: [...Array(8)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 8}s`,\n          animationDuration: `${8 + Math.random() * 4}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"relative z-10 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"btn-secondary px-6 py-3 flex items-center gap-3 hover-lift\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n          className: \"text-2xl md:text-3xl font-bold gradient-text\",\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.6\n          },\n          children: \"TalentSphere AI Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-32\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: [step === 'form' && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -30\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"max-w-6xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 200,\n                delay: 0.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                className: \"w-4 h-4 text-cyan-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-cyan-300\",\n                children: \"AI-Powered Talent Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-4xl md:text-5xl font-bold mb-6\",\n              children: [\"Begin Your \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"gradient-text\",\n                children: \"AI Analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 30\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n              children: \"Upload talent profiles and define position requirements for comprehensive AI-powered analysis and strategic insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-10\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"glass-intense p-8 lg:p-12 rounded-3xl hover-glow\",\n              initial: {\n                opacity: 0,\n                y: 40\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold mb-8 flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"icon-container\",\n                  children: /*#__PURE__*/_jsxDEV(Target, {\n                    className: \"w-8 h-8 text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this), \"Position Requirements\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Position Title *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.position_title,\n                    onChange: e => handleInputChange('position_title', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"Senior Software Engineer\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Company Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.company_name,\n                    onChange: e => handleInputChange('company_name', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"Your Company Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                  children: \"Position Description *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: jobRequirements.job_description,\n                  onChange: e => handleInputChange('job_description', e.target.value),\n                  className: \"form-textarea w-full\",\n                  rows: 6,\n                  placeholder: \"Enter detailed position description including responsibilities, requirements, and qualifications...\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Required Skills (comma-separated)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.required_skills,\n                    onChange: e => handleInputChange('required_skills', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"Python, React, AWS, Machine Learning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Preferred Skills (comma-separated)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.preferred_skills,\n                    onChange: e => handleInputChange('preferred_skills', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"Docker, Kubernetes, GraphQL\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Experience Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: jobRequirements.experience_level,\n                    onChange: e => handleInputChange('experience_level', e.target.value),\n                    className: \"form-input w-full\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select experience level\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Entry Level\",\n                      children: \"Entry Level (0-2 years)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Mid Level\",\n                      children: \"Mid Level (3-5 years)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Senior Level\",\n                      children: \"Senior Level (6-10 years)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Executive Level\",\n                      children: \"Executive Level (10+ years)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Location\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.location,\n                    onChange: e => handleInputChange('location', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"San Francisco, CA or Remote\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Education Requirements\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.education_requirements,\n                    onChange: e => handleInputChange('education_requirements', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"Bachelor's degree in Computer Science\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-semibold mb-3 text-gray-300\",\n                    children: \"Additional Keywords (comma-separated)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: jobRequirements.keywords,\n                    onChange: e => handleInputChange('keywords', e.target.value),\n                    className: \"form-input w-full\",\n                    placeholder: \"Agile, Scrum, Leadership\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"glass-intense p-8 lg:p-12 rounded-3xl hover-glow\",\n              initial: {\n                opacity: 0,\n                y: 40\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.4\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold mb-8 flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"icon-container\",\n                  children: /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-8 h-8 text-purple-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this), \"Talent Profiles\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ...getRootProps(),\n                className: `border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-400 ${isDragActive ? 'border-blue-400 bg-blue-400/10 scale-105' : 'border-white/20 hover:border-white/40 hover:bg-white/5'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  ...getInputProps()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: isDragActive ? {\n                    scale: 1.1\n                  } : {\n                    scale: 1\n                  },\n                  transition: {\n                    duration: 0.2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Upload, {\n                    className: \"w-16 h-16 mx-auto mb-6 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xl mb-3\",\n                    children: isDragActive ? 'Drop the talent profiles here...' : 'Drag & drop talent profiles here, or click to select'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Supports PDF, DOC, DOCX, and TXT files\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"mt-8\",\n                initial: {\n                  opacity: 0,\n                  height: 0\n                },\n                animate: {\n                  opacity: 1,\n                  height: 'auto'\n                },\n                transition: {\n                  duration: 0.4\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-xl font-bold mb-6 flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-6 h-6 text-green-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 25\n                  }, this), \"Selected Files (\", files.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3\",\n                  children: files.map((file, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      x: -20\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: index * 0.1\n                    },\n                    className: \"flex items-center justify-between p-4 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl hover:bg-white/10 transition-all\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(FileText, {\n                          className: \"w-6 h-6 text-blue-400\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 483,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 486,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-500\",\n                          children: [(file.size / 1024 / 1024).toFixed(2), \" MB\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 487,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => removeFile(index),\n                      className: \"w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded-lg flex items-center justify-center text-red-400 hover:text-red-300 transition-all\",\n                      children: /*#__PURE__*/_jsxDEV(X, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 497,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-center\",\n              initial: {\n                opacity: 0,\n                y: 40\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.8,\n                delay: 0.6\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn-primary text-xl px-12 py-6 mx-auto hover-lift\",\n                disabled: !jobRequirements.position_title || !jobRequirements.job_description || files.length === 0,\n                children: [/*#__PURE__*/_jsxDEV(Brain, {\n                  className: \"w-7 h-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this), \"Begin AI Analysis\", /*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-7 h-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400 mt-4\",\n                children: [\"Our AI agents will analyze \", files.length, \" profile\", files.length !== 1 ? 's' : '', \" with advanced intelligence\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, \"form\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), step === 'processing' && /*#__PURE__*/_jsxDEV(ProcessingAnimation, {\n          progress: processingProgress,\n          currentStep: currentProcessingStep,\n          filesCount: files.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this), step === 'results' && analysisResult && /*#__PURE__*/_jsxDEV(ResultsDisplay, {\n          result: analysisResult,\n          onStartNew: handleStartNew\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisPage, \"N72S/LnbzjZEtsi5ybDW7kw+uOQ=\", false, function () {\n  return [useNavigate, useDropzone];\n});\n_c = AnalysisPage;\nexport default AnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"AnalysisPage\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "motion", "AnimatePresence", "useNavigate", "useDropzone", "toast", "ArrowLeft", "Upload", "FileText", "X", "Brain", "CheckCircle", "<PERSON><PERSON><PERSON>", "Target", "Users", "AnimatedBackground", "ProcessingAnimation", "ResultsDisplay", "jsxDEV", "_jsxDEV", "AnalysisPage", "_s", "navigate", "step", "setStep", "files", "setFiles", "jobRequirements", "setJobRequirements", "position_title", "job_description", "required_skills", "preferred_skills", "experience_level", "education_requirements", "location", "employment_type", "company_name", "keywords", "analysisResult", "setAnalysisResult", "processingProgress", "setProcessingProgress", "currentProcessingStep", "setCurrentProcessingStep", "onDrop", "acceptedFiles", "prev", "success", "length", "getRootProps", "getInputProps", "isDragActive", "accept", "multiple", "removeFile", "index", "filter", "_", "i", "handleInputChange", "field", "value", "handleSubmit", "e", "preventDefault", "error", "formData", "FormData", "Object", "entries", "for<PERSON>ach", "key", "append", "file", "steps", "Promise", "resolve", "setTimeout", "response", "fetch", "method", "body", "result", "json", "message", "console", "handleBackToHome", "handleStartNew", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "style", "left", "Math", "random", "animationDelay", "animationDuration", "onClick", "h1", "initial", "opacity", "scale", "animate", "transition", "duration", "mode", "div", "y", "exit", "type", "stiffness", "delay", "onSubmit", "onChange", "target", "placeholder", "required", "rows", "height", "x", "name", "size", "toFixed", "disabled", "progress", "currentStep", "filesCount", "onStartNew", "_c", "$RefreshReg$"], "sources": ["D:/Projects/ai-hr-agent/frontend/src/pages/AnalysisPage.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport toast from 'react-hot-toast';\r\nimport { \r\n  ArrowLeft, \r\n  Upload, \r\n  FileText, \r\n  X, \r\n  Zap,\r\n  Brain,\r\n  Search,\r\n  Shield,\r\n  BarChart3,\r\n  CheckCircle,\r\n  AlertCircle,\r\n  Download,\r\n  Eye,\r\n  Clock,\r\n  Sparkles,\r\n  Target,\r\n  Users,\r\n  Cpu\r\n} from 'lucide-react';\r\nimport AnimatedBackground from '../components/AnimatedBackground';\r\nimport ProcessingAnimation from '../components/ProcessingAnimation';\r\nimport ResultsDisplay from '../components/ResultsDisplay';\r\n\r\ninterface JobRequirements {\r\n  position_title: string;\r\n  job_description: string;\r\n  required_skills: string;\r\n  preferred_skills: string;\r\n  experience_level: string;\r\n  education_requirements: string;\r\n  location: string;\r\n  employment_type: string;\r\n  company_name: string;\r\n  keywords: string;\r\n}\r\n\r\ninterface AnalysisResult {\r\n  success: boolean;\r\n  job_id: string;\r\n  message: string;\r\n  processing_time?: number;\r\n  statistics?: any;\r\n  final_report?: string;\r\n  candidate_reports?: Record<string, string>;\r\n  report_files?: Record<string, string | string[]>;\r\n  warnings?: string[];\r\n  errors?: string[];\r\n}\r\n\r\nconst AnalysisPage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const [step, setStep] = useState<'form' | 'processing' | 'results'>('form');\r\n  const [files, setFiles] = useState<File[]>([]);\r\n  const [jobRequirements, setJobRequirements] = useState<JobRequirements>({\r\n    position_title: '',\r\n    job_description: '',\r\n    required_skills: '',\r\n    preferred_skills: '',\r\n    experience_level: '',\r\n    education_requirements: '',\r\n    location: '',\r\n    employment_type: '',\r\n    company_name: '',\r\n    keywords: ''\r\n  });\r\n  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);\r\n  const [processingProgress, setProcessingProgress] = useState(0);\r\n  const [currentProcessingStep, setCurrentProcessingStep] = useState('');\r\n\r\n  const onDrop = useCallback((acceptedFiles: File[]) => {\r\n    setFiles(prev => [...prev, ...acceptedFiles]);\r\n    toast.success(`${acceptedFiles.length} file(s) added successfully`);\r\n  }, []);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'application/pdf': ['.pdf'],\r\n      'application/msword': ['.doc'],\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\r\n      'text/plain': ['.txt']\r\n    },\r\n    multiple: true\r\n  });\r\n\r\n  const removeFile = (index: number) => {\r\n    setFiles(prev => prev.filter((_, i) => i !== index));\r\n    toast.success('File removed');\r\n  };\r\n\r\n  const handleInputChange = (field: keyof JobRequirements, value: string) => {\r\n    setJobRequirements(prev => ({ ...prev, [field]: value }));\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!jobRequirements.position_title || !jobRequirements.job_description) {\r\n      toast.error('Please fill in position title and job description');\r\n      return;\r\n    }\r\n\r\n    if (files.length === 0) {\r\n      toast.error('Please upload at least one resume file');\r\n      return;\r\n    }\r\n\r\n    setStep('processing');\r\n    setProcessingProgress(0);\r\n    setCurrentProcessingStep('Initializing AI agents...');\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      \r\n      // Add job requirements\r\n      Object.entries(jobRequirements).forEach(([key, value]) => {\r\n        formData.append(key, value);\r\n      });\r\n\r\n      // Add files\r\n      files.forEach(file => {\r\n        formData.append('files', file);\r\n      });\r\n\r\n      // Simulate processing steps with TalentSphere AI branding\r\n      const steps = [\r\n        'Initializing AI agents...',\r\n        'Extracting talent data...',\r\n        'Analyzing candidate profiles...',\r\n        'Matching against requirements...',\r\n        'Deep research across platforms...',\r\n        'Validating talent information...',\r\n        'Generating strategic insights...'\r\n      ];\r\n\r\n      // Simulate progress\r\n      for (let i = 0; i < steps.length; i++) {\r\n        setCurrentProcessingStep(steps[i]);\r\n        setProcessingProgress((i + 1) / steps.length * 90); // Stop at 90%\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n      }\r\n\r\n      // Make API call\r\n      const response = await fetch('http://localhost:8000/analyze', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      const result: AnalysisResult = await response.json();\r\n      \r\n      setProcessingProgress(100);\r\n      setCurrentProcessingStep('Analysis complete!');\r\n      \r\n      if (result.success) {\r\n        setAnalysisResult(result);\r\n        setStep('results');\r\n        toast.success('Talent analysis completed successfully!');\r\n      } else {\r\n        toast.error(result.message || 'Analysis failed');\r\n        setStep('form');\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('Analysis error:', error);\r\n      toast.error('Failed to analyze talent profiles. Please try again.');\r\n      setStep('form');\r\n    }\r\n  };\r\n\r\n  const handleBackToHome = () => {\r\n    navigate('/');\r\n  };\r\n\r\n  const handleStartNew = () => {\r\n    setStep('form');\r\n    setFiles([]);\r\n    setJobRequirements({\r\n      position_title: '',\r\n      job_description: '',\r\n      required_skills: '',\r\n      preferred_skills: '',\r\n      experience_level: '',\r\n      education_requirements: '',\r\n      location: '',\r\n      employment_type: '',\r\n      company_name: '',\r\n      keywords: ''\r\n    });\r\n    setAnalysisResult(null);\r\n    setProcessingProgress(0);\r\n    setCurrentProcessingStep('');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen relative overflow-hidden\">\r\n      <AnimatedBackground />\r\n      \r\n      {/* Floating particles effect */}\r\n      <div className=\"floating-particles\">\r\n        {[...Array(8)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className=\"particle\"\r\n            style={{\r\n              left: `${Math.random() * 100}%`,\r\n              animationDelay: `${Math.random() * 8}s`,\r\n              animationDuration: `${8 + Math.random() * 4}s`\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n      \r\n      {/* Header */}\r\n      <header className=\"relative z-10 p-6\">\r\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\r\n          <button\r\n            onClick={handleBackToHome}\r\n            className=\"btn-secondary px-6 py-3 flex items-center gap-3 hover-lift\"\r\n          >\r\n            <ArrowLeft className=\"w-5 h-5\" />\r\n            Back to Home\r\n          </button>\r\n          \r\n          <motion.h1 \r\n            className=\"text-2xl md:text-3xl font-bold gradient-text\"\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.6 }}\r\n          >\r\n            TalentSphere AI Analysis\r\n          </motion.h1>\r\n          \r\n          <div className=\"w-32\" /> {/* Spacer */}\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 py-8\">\r\n        <AnimatePresence mode=\"wait\">\r\n          {step === 'form' && (\r\n            <motion.div\r\n              key=\"form\"\r\n              initial={{ opacity: 0, y: 30 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -30 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"max-w-6xl mx-auto\"\r\n            >\r\n              <div className=\"text-center mb-12\">\r\n                <motion.div\r\n                  className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n                  initial={{ scale: 0 }}\r\n                  animate={{ scale: 1 }}\r\n                  transition={{ type: \"spring\", stiffness: 200, delay: 0.2 }}\r\n                >\r\n                  <Sparkles className=\"w-4 h-4 text-cyan-400\" />\r\n                  <span className=\"text-sm font-medium text-cyan-300\">AI-Powered Talent Discovery</span>\r\n                </motion.div>\r\n                <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\r\n                  Begin Your <span className=\"gradient-text\">AI Analysis</span>\r\n                </h2>\r\n                <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\r\n                  Upload talent profiles and define position requirements for comprehensive \r\n                  AI-powered analysis and strategic insights\r\n                </p>\r\n              </div>\r\n\r\n              <form onSubmit={handleSubmit} className=\"space-y-10\">\r\n                {/* Job Requirements Section */}\r\n                <motion.div \r\n                  className=\"glass-intense p-8 lg:p-12 rounded-3xl hover-glow\"\r\n                  initial={{ opacity: 0, y: 40 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.8, delay: 0.2 }}\r\n                >\r\n                  <h3 className=\"text-3xl font-bold mb-8 flex items-center gap-4\">\r\n                    <div className=\"icon-container\">\r\n                      <Target className=\"w-8 h-8 text-blue-400\" />\r\n                    </div>\r\n                    Position Requirements\r\n                  </h3>\r\n                  \r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Position Title *\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.position_title}\r\n                        onChange={(e) => handleInputChange('position_title', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"Senior Software Engineer\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    \r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Company Name\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.company_name}\r\n                        onChange={(e) => handleInputChange('company_name', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"Your Company Name\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"mt-8\">\r\n                    <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                      Position Description *\r\n                    </label>\r\n                    <textarea\r\n                      value={jobRequirements.job_description}\r\n                      onChange={(e) => handleInputChange('job_description', e.target.value)}\r\n                      className=\"form-textarea w-full\"\r\n                      rows={6}\r\n                      placeholder=\"Enter detailed position description including responsibilities, requirements, and qualifications...\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Required Skills (comma-separated)\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.required_skills}\r\n                        onChange={(e) => handleInputChange('required_skills', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"Python, React, AWS, Machine Learning\"\r\n                      />\r\n                    </div>\r\n                    \r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Preferred Skills (comma-separated)\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.preferred_skills}\r\n                        onChange={(e) => handleInputChange('preferred_skills', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"Docker, Kubernetes, GraphQL\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Experience Level\r\n                      </label>\r\n                      <select\r\n                        value={jobRequirements.experience_level}\r\n                        onChange={(e) => handleInputChange('experience_level', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                      >\r\n                        <option value=\"\">Select experience level</option>\r\n                        <option value=\"Entry Level\">Entry Level (0-2 years)</option>\r\n                        <option value=\"Mid Level\">Mid Level (3-5 years)</option>\r\n                        <option value=\"Senior Level\">Senior Level (6-10 years)</option>\r\n                        <option value=\"Executive Level\">Executive Level (10+ years)</option>\r\n                      </select>\r\n                    </div>\r\n                    \r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Location\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.location}\r\n                        onChange={(e) => handleInputChange('location', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"San Francisco, CA or Remote\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Education Requirements\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.education_requirements}\r\n                        onChange={(e) => handleInputChange('education_requirements', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"Bachelor's degree in Computer Science\"\r\n                      />\r\n                    </div>\r\n                    \r\n                    <div>\r\n                      <label className=\"block text-sm font-semibold mb-3 text-gray-300\">\r\n                        Additional Keywords (comma-separated)\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={jobRequirements.keywords}\r\n                        onChange={(e) => handleInputChange('keywords', e.target.value)}\r\n                        className=\"form-input w-full\"\r\n                        placeholder=\"Agile, Scrum, Leadership\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n\r\n                {/* File Upload Section */}\r\n                <motion.div \r\n                  className=\"glass-intense p-8 lg:p-12 rounded-3xl hover-glow\"\r\n                  initial={{ opacity: 0, y: 40 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.8, delay: 0.4 }}\r\n                >\r\n                  <h3 className=\"text-3xl font-bold mb-8 flex items-center gap-4\">\r\n                    <div className=\"icon-container\">\r\n                      <Users className=\"w-8 h-8 text-purple-400\" />\r\n                    </div>\r\n                    Talent Profiles\r\n                  </h3>\r\n                  \r\n                  <div\r\n                    {...getRootProps()}\r\n                    className={`border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-400 ${\r\n                      isDragActive\r\n                        ? 'border-blue-400 bg-blue-400/10 scale-105'\r\n                        : 'border-white/20 hover:border-white/40 hover:bg-white/5'\r\n                    }`}\r\n                  >\r\n                    <input {...getInputProps()} />\r\n                    <motion.div\r\n                      animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}\r\n                      transition={{ duration: 0.2 }}\r\n                    >\r\n                      <Upload className=\"w-16 h-16 mx-auto mb-6 text-gray-400\" />\r\n                      <p className=\"text-xl mb-3\">\r\n                        {isDragActive\r\n                          ? 'Drop the talent profiles here...'\r\n                          : 'Drag & drop talent profiles here, or click to select'\r\n                        }\r\n                      </p>\r\n                      <p className=\"text-sm text-gray-500\">\r\n                        Supports PDF, DOC, DOCX, and TXT files\r\n                      </p>\r\n                    </motion.div>\r\n                  </div>\r\n\r\n                  {/* Selected Files */}\r\n                  {files.length > 0 && (\r\n                    <motion.div \r\n                      className=\"mt-8\"\r\n                      initial={{ opacity: 0, height: 0 }}\r\n                      animate={{ opacity: 1, height: 'auto' }}\r\n                      transition={{ duration: 0.4 }}\r\n                    >\r\n                      <h4 className=\"text-xl font-bold mb-6 flex items-center gap-3\">\r\n                        <CheckCircle className=\"w-6 h-6 text-green-400\" />\r\n                        Selected Files ({files.length})\r\n                      </h4>\r\n                      <div className=\"grid gap-3\">\r\n                        {files.map((file, index) => (\r\n                          <motion.div\r\n                            key={index}\r\n                            initial={{ opacity: 0, x: -20 }}\r\n                            animate={{ opacity: 1, x: 0 }}\r\n                            transition={{ delay: index * 0.1 }}\r\n                            className=\"flex items-center justify-between p-4 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl hover:bg-white/10 transition-all\"\r\n                          >\r\n                            <div className=\"flex items-center gap-4\">\r\n                              <div className=\"w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center\">\r\n                                <FileText className=\"w-6 h-6 text-blue-400\" />\r\n                              </div>\r\n                              <div>\r\n                                <span className=\"font-medium\">{file.name}</span>\r\n                                <div className=\"text-sm text-gray-500\">\r\n                                  {(file.size / 1024 / 1024).toFixed(2)} MB\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => removeFile(index)}\r\n                              className=\"w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded-lg flex items-center justify-center text-red-400 hover:text-red-300 transition-all\"\r\n                            >\r\n                              <X className=\"w-4 h-4\" />\r\n                            </button>\r\n                          </motion.div>\r\n                        ))}\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                </motion.div>\r\n\r\n                {/* Submit Button */}\r\n                <motion.div \r\n                  className=\"text-center\"\r\n                  initial={{ opacity: 0, y: 40 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.8, delay: 0.6 }}\r\n                >\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"btn-primary text-xl px-12 py-6 mx-auto hover-lift\"\r\n                    disabled={!jobRequirements.position_title || !jobRequirements.job_description || files.length === 0}\r\n                  >\r\n                    <Brain className=\"w-7 h-7\" />\r\n                    Begin AI Analysis\r\n                    <Sparkles className=\"w-7 h-7\" />\r\n                  </button>\r\n                  <p className=\"text-sm text-gray-400 mt-4\">\r\n                    Our AI agents will analyze {files.length} profile{files.length !== 1 ? 's' : ''} with advanced intelligence\r\n                  </p>\r\n                </motion.div>\r\n              </form>\r\n            </motion.div>\r\n          )}\r\n\r\n          {step === 'processing' && (\r\n            <ProcessingAnimation\r\n              progress={processingProgress}\r\n              currentStep={currentProcessingStep}\r\n              filesCount={files.length}\r\n            />\r\n          )}\r\n\r\n          {step === 'results' && analysisResult && (\r\n            <ResultsDisplay\r\n              result={analysisResult}\r\n              onStartNew={handleStartNew}\r\n            />\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnalysisPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SACEC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,CAAC,EAEDC,KAAK,EAILC,WAAW,EAKXC,QAAQ,EACRC,MAAM,EACNC,KAAK,QAEA,cAAc;AACrB,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B1D,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAoC,MAAM,CAAC;EAC3E,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAkB;IACtE8B,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE,EAAE;IACpBC,sBAAsB,EAAE,EAAE;IAC1BC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAwB,IAAI,CAAC;EACjF,MAAM,CAAC0C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC4C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM8C,MAAM,GAAG7C,WAAW,CAAE8C,aAAqB,IAAK;IACpDpB,QAAQ,CAACqB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,aAAa,CAAC,CAAC;IAC7CzC,KAAK,CAAC2C,OAAO,CAAC,GAAGF,aAAa,CAACG,MAAM,6BAA6B,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEC,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGhD,WAAW,CAAC;IAChEyC,MAAM;IACNQ,MAAM,EAAE;MACN,iBAAiB,EAAE,CAAC,MAAM,CAAC;MAC3B,oBAAoB,EAAE,CAAC,MAAM,CAAC;MAC9B,yEAAyE,EAAE,CAAC,OAAO,CAAC;MACpF,YAAY,EAAE,CAAC,MAAM;IACvB,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAIC,KAAa,IAAK;IACpC9B,QAAQ,CAACqB,IAAI,IAAIA,IAAI,CAACU,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK,CAAC,CAAC;IACpDnD,KAAK,CAAC2C,OAAO,CAAC,cAAc,CAAC;EAC/B,CAAC;EAED,MAAMY,iBAAiB,GAAGA,CAACC,KAA4B,EAAEC,KAAa,KAAK;IACzElC,kBAAkB,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACc,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtC,eAAe,CAACE,cAAc,IAAI,CAACF,eAAe,CAACG,eAAe,EAAE;MACvEzB,KAAK,CAAC6D,KAAK,CAAC,mDAAmD,CAAC;MAChE;IACF;IAEA,IAAIzC,KAAK,CAACwB,MAAM,KAAK,CAAC,EAAE;MACtB5C,KAAK,CAAC6D,KAAK,CAAC,wCAAwC,CAAC;MACrD;IACF;IAEA1C,OAAO,CAAC,YAAY,CAAC;IACrBkB,qBAAqB,CAAC,CAAC,CAAC;IACxBE,wBAAwB,CAAC,2BAA2B,CAAC;IAErD,IAAI;MACF,MAAMuB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAC,MAAM,CAACC,OAAO,CAAC3C,eAAe,CAAC,CAAC4C,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEV,KAAK,CAAC,KAAK;QACxDK,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEV,KAAK,CAAC;MAC7B,CAAC,CAAC;;MAEF;MACArC,KAAK,CAAC8C,OAAO,CAACG,IAAI,IAAI;QACpBP,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAEC,IAAI,CAAC;MAChC,CAAC,CAAC;;MAEF;MACA,MAAMC,KAAK,GAAG,CACZ,2BAA2B,EAC3B,2BAA2B,EAC3B,iCAAiC,EACjC,kCAAkC,EAClC,mCAAmC,EACnC,kCAAkC,EAClC,kCAAkC,CACnC;;MAED;MACA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,CAAC1B,MAAM,EAAEU,CAAC,EAAE,EAAE;QACrCf,wBAAwB,CAAC+B,KAAK,CAAChB,CAAC,CAAC,CAAC;QAClCjB,qBAAqB,CAAC,CAACiB,CAAC,GAAG,CAAC,IAAIgB,KAAK,CAAC1B,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;QACpD,MAAM,IAAI2B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACzD;;MAEA;MACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,EAAE;QAC5DC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEf;MACR,CAAC,CAAC;MAEF,MAAMgB,MAAsB,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAEpD1C,qBAAqB,CAAC,GAAG,CAAC;MAC1BE,wBAAwB,CAAC,oBAAoB,CAAC;MAE9C,IAAIuC,MAAM,CAACnC,OAAO,EAAE;QAClBR,iBAAiB,CAAC2C,MAAM,CAAC;QACzB3D,OAAO,CAAC,SAAS,CAAC;QAClBnB,KAAK,CAAC2C,OAAO,CAAC,yCAAyC,CAAC;MAC1D,CAAC,MAAM;QACL3C,KAAK,CAAC6D,KAAK,CAACiB,MAAM,CAACE,OAAO,IAAI,iBAAiB,CAAC;QAChD7D,OAAO,CAAC,MAAM,CAAC;MACjB;IAEF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC7D,KAAK,CAAC6D,KAAK,CAAC,sDAAsD,CAAC;MACnE1C,OAAO,CAAC,MAAM,CAAC;IACjB;EACF,CAAC;EAED,MAAM+D,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjE,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMkE,cAAc,GAAGA,CAAA,KAAM;IAC3BhE,OAAO,CAAC,MAAM,CAAC;IACfE,QAAQ,CAAC,EAAE,CAAC;IACZE,kBAAkB,CAAC;MACjBC,cAAc,EAAE,EAAE;MAClBC,eAAe,EAAE,EAAE;MACnBC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE,EAAE;MACpBC,sBAAsB,EAAE,EAAE;MAC1BC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,qBAAqB,CAAC,CAAC,CAAC;IACxBE,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,oBACEzB,OAAA;IAAKsE,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDvE,OAAA,CAACJ,kBAAkB;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtB3E,OAAA;MAAKsE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChC,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACtC,CAAC,EAAEC,CAAC,kBACtBxC,OAAA;QAEEsE,SAAS,EAAC,UAAU;QACpBQ,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,cAAc,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCE,iBAAiB,EAAE,GAAG,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GANGzC,CAAC;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3E,OAAA;MAAQsE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACnCvE,OAAA;QAAKsE,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClEvE,OAAA;UACEoF,OAAO,EAAEhB,gBAAiB;UAC1BE,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBAEtEvE,OAAA,CAACb,SAAS;YAACmF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3E,OAAA,CAAClB,MAAM,CAACuG,EAAE;UACRf,SAAS,EAAC,8CAA8C;UACxDgB,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAApB,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZ3E,OAAA;UAAKsE,SAAS,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET3E,OAAA;MAAKsE,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxDvE,OAAA,CAACjB,eAAe;QAAC6G,IAAI,EAAC,MAAM;QAAArB,QAAA,GACzBnE,IAAI,KAAK,MAAM,iBACdJ,OAAA,CAAClB,MAAM,CAAC+G,GAAG;UAETP,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEO,CAAC,EAAE;UAAG,CAAE;UAC/BL,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEO,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEO,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BJ,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BrB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BvE,OAAA;YAAKsE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCvE,OAAA,CAAClB,MAAM,CAAC+G,GAAG;cACTvB,SAAS,EAAC,+GAA+G;cACzHgB,OAAO,EAAE;gBAAEE,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAE;cAAE,CAAE;cACtBE,UAAU,EAAE;gBAAEM,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAA3B,QAAA,gBAE3DvE,OAAA,CAACP,QAAQ;gBAAC6E,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9C3E,OAAA;gBAAMsE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACb3E,OAAA;cAAIsE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,aACvC,eAAAvE,OAAA;gBAAMsE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACL3E,OAAA;cAAGsE,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EAAC;YAGvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN3E,OAAA;YAAMmG,QAAQ,EAAEvD,YAAa;YAAC0B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAElDvE,OAAA,CAAClB,MAAM,CAAC+G,GAAG;cACTvB,SAAS,EAAC,kDAAkD;cAC5DgB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,CAAC,EAAE;cAAG,CAAE;cAC/BL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEO,CAAC,EAAE;cAAE,CAAE;cAC9BJ,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEO,KAAK,EAAE;cAAI,CAAE;cAAA3B,QAAA,gBAE1CvE,OAAA;gBAAIsE,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7DvE,OAAA;kBAAKsE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,eAC7BvE,OAAA,CAACN,MAAM;oBAAC4E,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,yBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEL3E,OAAA;gBAAKsE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDvE,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACE,cAAe;oBACtC0F,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,gBAAgB,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBACrE2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC,0BAA0B;oBACtCC,QAAQ;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN3E,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACU,YAAa;oBACpCkF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,cAAc,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBACnE2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC;kBAAmB;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3E,OAAA;gBAAKsE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBvE,OAAA;kBAAOsE,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR3E,OAAA;kBACE2C,KAAK,EAAEnC,eAAe,CAACG,eAAgB;kBACvCyF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,iBAAiB,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;kBACtE2B,SAAS,EAAC,sBAAsB;kBAChCkC,IAAI,EAAE,CAAE;kBACRF,WAAW,EAAC,qGAAqG;kBACjHC,QAAQ;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN3E,OAAA;gBAAKsE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzDvE,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACI,eAAgB;oBACvCwF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,iBAAiB,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBACtE2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC;kBAAsC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN3E,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACK,gBAAiB;oBACxCuF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,kBAAkB,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBACvE2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC;kBAA6B;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3E,OAAA;gBAAKsE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzDvE,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACE2C,KAAK,EAAEnC,eAAe,CAACM,gBAAiB;oBACxCsF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,kBAAkB,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBACvE2B,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAE7BvE,OAAA;sBAAQ2C,KAAK,EAAC,EAAE;sBAAA4B,QAAA,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjD3E,OAAA;sBAAQ2C,KAAK,EAAC,aAAa;sBAAA4B,QAAA,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5D3E,OAAA;sBAAQ2C,KAAK,EAAC,WAAW;sBAAA4B,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxD3E,OAAA;sBAAQ2C,KAAK,EAAC,cAAc;sBAAA4B,QAAA,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/D3E,OAAA;sBAAQ2C,KAAK,EAAC,iBAAiB;sBAAA4B,QAAA,EAAC;oBAA2B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEN3E,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACQ,QAAS;oBAChCoF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,UAAU,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBAC/D2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC;kBAA6B;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3E,OAAA;gBAAKsE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzDvE,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACO,sBAAuB;oBAC9CqF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,wBAAwB,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBAC7E2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC;kBAAuC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN3E,OAAA;kBAAAuE,QAAA,gBACEvE,OAAA;oBAAOsE,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR3E,OAAA;oBACEgG,IAAI,EAAC,MAAM;oBACXrD,KAAK,EAAEnC,eAAe,CAACW,QAAS;oBAChCiF,QAAQ,EAAGvD,CAAC,IAAKJ,iBAAiB,CAAC,UAAU,EAAEI,CAAC,CAACwD,MAAM,CAAC1D,KAAK,CAAE;oBAC/D2B,SAAS,EAAC,mBAAmB;oBAC7BgC,WAAW,EAAC;kBAA0B;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGb3E,OAAA,CAAClB,MAAM,CAAC+G,GAAG;cACTvB,SAAS,EAAC,kDAAkD;cAC5DgB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,CAAC,EAAE;cAAG,CAAE;cAC/BL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEO,CAAC,EAAE;cAAE,CAAE;cAC9BJ,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEO,KAAK,EAAE;cAAI,CAAE;cAAA3B,QAAA,gBAE1CvE,OAAA;gBAAIsE,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7DvE,OAAA;kBAAKsE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,eAC7BvE,OAAA,CAACL,KAAK;oBAAC2E,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,mBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEL3E,OAAA;gBAAA,GACM+B,YAAY,CAAC,CAAC;gBAClBuC,SAAS,EAAE,kGACTrC,YAAY,GACR,0CAA0C,GAC1C,wDAAwD,EAC3D;gBAAAsC,QAAA,gBAEHvE,OAAA;kBAAA,GAAWgC,aAAa,CAAC;gBAAC;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9B3E,OAAA,CAAClB,MAAM,CAAC+G,GAAG;kBACTJ,OAAO,EAAExD,YAAY,GAAG;oBAAEuD,KAAK,EAAE;kBAAI,CAAC,GAAG;oBAAEA,KAAK,EAAE;kBAAE,CAAE;kBACtDE,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAApB,QAAA,gBAE9BvE,OAAA,CAACZ,MAAM;oBAACkF,SAAS,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3D3E,OAAA;oBAAGsE,SAAS,EAAC,cAAc;oBAAAC,QAAA,EACxBtC,YAAY,GACT,kCAAkC,GAClC;kBAAsD;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzD,CAAC,eACJ3E,OAAA;oBAAGsE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EAGLrE,KAAK,CAACwB,MAAM,GAAG,CAAC,iBACf9B,OAAA,CAAClB,MAAM,CAAC+G,GAAG;gBACTvB,SAAS,EAAC,MAAM;gBAChBgB,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEkB,MAAM,EAAE;gBAAE,CAAE;gBACnChB,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEkB,MAAM,EAAE;gBAAO,CAAE;gBACxCf,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAApB,QAAA,gBAE9BvE,OAAA;kBAAIsE,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,gBAC5DvE,OAAA,CAACR,WAAW;oBAAC8E,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAClC,EAACrE,KAAK,CAACwB,MAAM,EAAC,GAChC;gBAAA;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL3E,OAAA;kBAAKsE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EACxBjE,KAAK,CAACuE,GAAG,CAAC,CAACtB,IAAI,EAAElB,KAAK,kBACrBrC,OAAA,CAAClB,MAAM,CAAC+G,GAAG;oBAETP,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEmB,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCjB,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEmB,CAAC,EAAE;oBAAE,CAAE;oBAC9BhB,UAAU,EAAE;sBAAEQ,KAAK,EAAE7D,KAAK,GAAG;oBAAI,CAAE;oBACnCiC,SAAS,EAAC,sIAAsI;oBAAAC,QAAA,gBAEhJvE,OAAA;sBAAKsE,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACtCvE,OAAA;wBAAKsE,SAAS,EAAC,sEAAsE;wBAAAC,QAAA,eACnFvE,OAAA,CAACX,QAAQ;0BAACiF,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACN3E,OAAA;wBAAAuE,QAAA,gBACEvE,OAAA;0BAAMsE,SAAS,EAAC,aAAa;0BAAAC,QAAA,EAAEhB,IAAI,CAACoD;wBAAI;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAChD3E,OAAA;0BAAKsE,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GACnC,CAAChB,IAAI,CAACqD,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACxC;wBAAA;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3E,OAAA;sBACEgG,IAAI,EAAC,QAAQ;sBACbZ,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAACC,KAAK,CAAE;sBACjCiC,SAAS,EAAC,sIAAsI;sBAAAC,QAAA,eAEhJvE,OAAA,CAACV,CAAC;wBAACgF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA,GAvBJtC,KAAK;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwBA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC,eAGb3E,OAAA,CAAClB,MAAM,CAAC+G,GAAG;cACTvB,SAAS,EAAC,aAAa;cACvBgB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEO,CAAC,EAAE;cAAG,CAAE;cAC/BL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEO,CAAC,EAAE;cAAE,CAAE;cAC9BJ,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEO,KAAK,EAAE;cAAI,CAAE;cAAA3B,QAAA,gBAE1CvE,OAAA;gBACEgG,IAAI,EAAC,QAAQ;gBACb1B,SAAS,EAAC,mDAAmD;gBAC7DwC,QAAQ,EAAE,CAACtG,eAAe,CAACE,cAAc,IAAI,CAACF,eAAe,CAACG,eAAe,IAAIL,KAAK,CAACwB,MAAM,KAAK,CAAE;gBAAAyC,QAAA,gBAEpGvE,OAAA,CAACT,KAAK;kBAAC+E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE7B,eAAA3E,OAAA,CAACP,QAAQ;kBAAC6E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACT3E,OAAA;gBAAGsE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,6BACb,EAACjE,KAAK,CAACwB,MAAM,EAAC,UAAQ,EAACxB,KAAK,CAACwB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,6BAClF;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,GAvRH,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwRA,CACb,EAEAvE,IAAI,KAAK,YAAY,iBACpBJ,OAAA,CAACH,mBAAmB;UAClBkH,QAAQ,EAAEzF,kBAAmB;UAC7B0F,WAAW,EAAExF,qBAAsB;UACnCyF,UAAU,EAAE3G,KAAK,CAACwB;QAAO;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACF,EAEAvE,IAAI,KAAK,SAAS,IAAIgB,cAAc,iBACnCpB,OAAA,CAACF,cAAc;UACbkE,MAAM,EAAE5C,cAAe;UACvB8F,UAAU,EAAE7C;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CA5eID,YAAsB;EAAA,QACTjB,WAAW,EAwB0BC,WAAW;AAAA;AAAAkI,EAAA,GAzB7DlH,YAAsB;AA8e5B,eAAeA,YAAY;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}