// GoogleOAuthHandler.tsx - Handle Google OAuth callback in popup
import React, { useEffect } from 'react';

const GoogleOAuthHandler: React.FC = () => {
  useEffect(() => {
    // Extract parameters from URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');

    const handleOAuthCallback = async () => {
      if (error) {
        console.error('OAuth error:', error);
        window.close();
        return;
      }

      if (code && state) {
        try {
          // Exchange code for token
          const response = await fetch(`http://localhost:8000/auth/google/callback?code=${code}&state=${state}`);
          const data = await response.json();

          if (data.success && data.access_token) {
            // Store token in localStorage for parent window to access
            localStorage.setItem('google_access_token', data.access_token);
            
            // Close popup - parent window will detect this
            window.close();
          } else {
            console.error('Token exchange failed:', data);
            window.close();
          }
        } catch (error) {
          console.error('Error exchanging code for token:', error);
          window.close();
        }
      } else {
        console.error('Missing code or state parameter');
        window.close();
      }
    };

    handleOAuthCallback();
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
        <p>Completing Google Drive connection...</p>
      </div>
    </div>
  );
};

export default GoogleOAuthHandler;