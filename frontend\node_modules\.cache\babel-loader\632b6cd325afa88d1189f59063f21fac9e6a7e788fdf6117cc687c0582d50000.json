{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\ai-hr-agent\\\\frontend\\\\src\\\\pages\\\\HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { Brain, Search, CheckCircle, Zap, ArrowRight, Shield, Sparkles, Target, TrendingUp, Award, Database, Globe, Cpu } from 'lucide-react';\nimport AnimatedBackground from '../components/AnimatedBackground';\nimport FeatureSection from '../components/FeatureSection';\nimport WorkflowAnimation from '../components/WorkflowAnimation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [isLoaded, setIsLoaded] = useState(false);\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n  const handleGetStarted = () => {\n    navigate('/analysis');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(AnimatedBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"floating-particles\",\n      children: [...Array(12)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"particle\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 8}s`,\n          animationDuration: `${8 + Math.random() * 4}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      className: \"relative z-10 min-h-screen flex items-center justify-center px-4 section-glow\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: isLoaded ? 1 : 0\n      },\n      transition: {\n        duration: 1.2\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            delay: 0.2\n          },\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            animate: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200,\n              delay: 0.1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-cyan-300\",\n              children: \"Next-Generation AI Talent Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"TalentSphere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Revolutionary Multi-Agent AI Platform for Comprehensive Talent Discovery, Deep Analysis, and Strategic Hiring Intelligence with Unparalleled Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 40\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            delay: 0.6\n          },\n          className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGetStarted,\n            className: \"btn-primary text-lg px-10 py-5 group hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(Brain, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), \"Discover Talent\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n              className: \"w-6 h-6 group-hover:translate-x-1 transition-transform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 40\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            delay: 0.8\n          },\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass-card p-8 rounded-2xl text-center hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container mx-auto\",\n              children: /*#__PURE__*/_jsxDEV(Brain, {\n                className: \"w-8 h-8 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-3\",\n              children: \"AI-Powered Intelligence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm leading-relaxed\",\n              children: \"Advanced multi-agent system for comprehensive talent evaluation with unprecedented depth and accuracy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass-card p-8 rounded-2xl text-center hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container mx-auto\",\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"w-8 h-8 text-purple-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-3\",\n              children: \"Deep Research Engine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm leading-relaxed\",\n              children: \"Automated research across LinkedIn, GitHub, and professional platforms for complete talent profiles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glass-card p-8 rounded-2xl text-center hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container mx-auto\",\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                className: \"w-8 h-8 text-cyan-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-3\",\n              children: \"Strategic Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm leading-relaxed\",\n              children: \"Comprehensive analysis with rankings, insights, and strategic hiring recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeatureSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4 section-glow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            whileInView: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(Cpu, {\n              className: \"w-4 h-4 text-purple-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-purple-300\",\n              children: \"AI-Powered Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-6xl font-bold mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"How TalentSphere Works\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Experience our revolutionary multi-agent AI system as it processes talent through advanced intelligence pipelines, research networks, and validation protocols\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WorkflowAnimation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"glass-intense p-12 lg:p-16 rounded-3xl hover-glow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n              initial: {\n                scale: 0\n              },\n              whileInView: {\n                scale: 1\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 200\n              },\n              viewport: {\n                once: true\n              },\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                className: \"w-4 h-4 text-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-300\",\n                children: \"Performance Excellence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-4xl md:text-5xl font-bold mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"gradient-text\",\n                children: \"Unmatched Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-gray-300 max-width-3xl mx-auto\",\n              children: \"Built for speed, precision, and scalability with cutting-edge AI optimizations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n            children: [{\n              value: \"15x\",\n              label: \"Faster Analysis\",\n              icon: Zap,\n              color: \"text-yellow-400\"\n            }, {\n              value: \"98%\",\n              label: \"Accuracy Rate\",\n              icon: Target,\n              color: \"text-green-400\"\n            }, {\n              value: \"7\",\n              label: \"AI Agents\",\n              icon: Brain,\n              color: \"text-purple-400\"\n            }, {\n              value: \"100+\",\n              label: \"Data Points\",\n              icon: Database,\n              color: \"text-cyan-400\"\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.1,\n                duration: 0.8\n              },\n              viewport: {\n                once: true\n              },\n              className: \"stat-card hover-lift\",\n              children: [/*#__PURE__*/_jsxDEV(stat.icon, {\n                className: `w-8 h-8 ${stat.color} mx-auto mb-4`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-value\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-label\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            whileInView: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(Award, {\n              className: \"w-4 h-4 text-amber-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-amber-300\",\n              children: \"Advanced Capabilities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-6xl font-bold mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Revolutionary Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Comprehensive suite of AI-powered tools designed for the future of talent acquisition\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: enhancedFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 40\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"feature-card hover-lift\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"icon-container\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-8 h-8 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold mb-4\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 leading-relaxed mb-6\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: feature.benefits.map((benefit, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4 text-green-400 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-300\",\n                  children: benefit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 23\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative z-10 py-32 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 60\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center glass-intense p-12 lg:p-20 rounded-3xl hover-glow section-glow\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\",\n            initial: {\n              scale: 0\n            },\n            whileInView: {\n              scale: 1\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 200\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-cyan-300\",\n              children: \"Ready to Transform?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-6xl font-bold mb-8\",\n            children: [\"Revolutionize Your \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"gradient-text\",\n              children: \"Talent Strategy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 34\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed\",\n            children: \"Experience the future of talent discovery with our AI-powered platform. Unlock unprecedented insights and make data-driven hiring decisions with confidence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGetStarted,\n              className: \"btn-primary text-xl px-12 py-6 group hover-lift\",\n              children: [/*#__PURE__*/_jsxDEV(Brain, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), \"Start Discovery\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n                className: \"w-6 h-6 group-hover:translate-x-1 transition-transform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-secondary text-xl px-12 py-6 hover-glow\",\n              children: [/*#__PURE__*/_jsxDEV(Globe, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), \"Schedule Demo\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"relative z-10 py-16 border-t border-white/10 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold gradient-text mb-3\",\n              children: \"TalentSphere AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-lg\",\n              children: \"Revolutionary AI-Powered Talent Analysis Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center gap-8 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"15x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"98%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Accurate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"AI Agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold gradient-text\",\n                children: \"100+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Data Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-500 text-sm\",\n            children: \"\\xA9 2024 TalentSphere AI. Powered by Advanced Multi-Agent Intelligence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"Y2by1Op+c0i5mal9nc1sARD3+0o=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nconst enhancedFeatures = [{\n  icon: Brain,\n  title: \"Multi-Agent AI System\",\n  description: \"Seven specialized AI agents working in parallel for comprehensive talent analysis with unprecedented depth and accuracy.\",\n  benefits: [\"GPT-4 powered intelligence\", \"Parallel processing architecture\", \"98% accuracy rate\", \"Semantic understanding\"]\n}, {\n  icon: Search,\n  title: \"Deep Research Engine\",\n  description: \"Automated research across LinkedIn, GitHub, and professional platforms to build complete talent profiles with strategic insights.\",\n  benefits: [\"Multi-platform intelligence\", \"Real-time data collection\", \"Professional verification\", \"Social proof analysis\"]\n}, {\n  icon: Shield,\n  title: \"Identity Validation\",\n  description: \"Advanced validation algorithms ensure talent authenticity through cross-platform verification and consistency analysis.\",\n  benefits: [\"Multi-factor verification\", \"Fraud detection AI\", \"Consistency analysis\", \"Confidence scoring\"]\n}, {\n  icon: Target,\n  title: \"Precision Matching\",\n  description: \"Intelligent scoring and ranking against job requirements with semantic analysis and cultural fit assessment.\",\n  benefits: [\"Semantic matching\", \"Cultural fit analysis\", \"Skills gap identification\", \"Growth potential scoring\"]\n}, {\n  icon: TrendingUp,\n  title: \"Predictive Analytics\",\n  description: \"Advanced analytics to predict candidate success, retention probability, and career trajectory patterns.\",\n  benefits: [\"Success prediction\", \"Retention modeling\", \"Career trajectory analysis\", \"Performance forecasting\"]\n}, {\n  icon: Globe,\n  title: \"Global Talent Pool\",\n  description: \"Access worldwide talent with localized insights, cultural considerations, and regional expertise mapping.\",\n  benefits: [\"Global reach\", \"Cultural intelligence\", \"Regional expertise\", \"Diversity insights\"]\n}];\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "useNavigate", "Brain", "Search", "CheckCircle", "Zap", "ArrowRight", "Shield", "<PERSON><PERSON><PERSON>", "Target", "TrendingUp", "Award", "Database", "Globe", "Cpu", "AnimatedBackground", "FeatureSection", "WorkflowAnimation", "jsxDEV", "_jsxDEV", "HomePage", "_s", "navigate", "isLoaded", "setIsLoaded", "handleGetStarted", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "style", "left", "Math", "random", "animationDelay", "animationDuration", "section", "initial", "opacity", "animate", "transition", "duration", "div", "y", "delay", "scale", "type", "stiffness", "onClick", "whileInView", "viewport", "once", "value", "label", "icon", "color", "stat", "index", "enhancedFeatures", "feature", "title", "description", "benefits", "benefit", "_c", "$RefreshReg$"], "sources": ["D:/Projects/ai-hr-agent/frontend/src/pages/HomePage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { \r\n  Brain, \r\n  Search, \r\n  CheckCircle, \r\n  BarChart3, \r\n  Users, \r\n  Zap,\r\n  ArrowRight,\r\n  Star,\r\n  Shield,\r\n  Clock,\r\n  Sparkles,\r\n  Target,\r\n  TrendingUp,\r\n  Award,\r\n  Database,\r\n  Globe,\r\n  Cpu\r\n} from 'lucide-react';\r\nimport AnimatedBackground from '../components/AnimatedBackground';\r\nimport FeatureSection from '../components/FeatureSection';\r\nimport WorkflowAnimation from '../components/WorkflowAnimation';\r\n\r\nconst HomePage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n  }, []);\r\n\r\n  const handleGetStarted = () => {\r\n    navigate('/analysis');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen relative overflow-hidden\">\r\n      <AnimatedBackground />\r\n      \r\n      {/* Floating particles effect */}\r\n      <div className=\"floating-particles\">\r\n        {[...Array(12)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className=\"particle\"\r\n            style={{\r\n              left: `${Math.random() * 100}%`,\r\n              animationDelay: `${Math.random() * 8}s`,\r\n              animationDuration: `${8 + Math.random() * 4}s`\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n      \r\n      {/* Hero Section */}\r\n      <motion.section \r\n        className=\"relative z-10 min-h-screen flex items-center justify-center px-4 section-glow\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: isLoaded ? 1 : 0 }}\r\n        transition={{ duration: 1.2 }}\r\n      >\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1, delay: 0.2 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200, delay: 0.1 }}\r\n            >\r\n              <Sparkles className=\"w-4 h-4 text-cyan-400\" />\r\n              <span className=\"text-sm font-medium text-cyan-300\">Next-Generation AI Talent Analysis</span>\r\n            </motion.div>\r\n            \r\n            <h1 className=\"hero-title text-4xl md:text-6xl lg:text-8xl font-bold mb-6 leading-tight\">\r\n              <span className=\"gradient-text\">TalentSphere</span>\r\n              <br />\r\n              <span className=\"text-white\">AI</span>\r\n            </h1>\r\n            <p className=\"hero-subtitle text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed\">\r\n              Revolutionary Multi-Agent AI Platform for Comprehensive Talent Discovery, \r\n              Deep Analysis, and Strategic Hiring Intelligence with Unparalleled Insights\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 40 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1, delay: 0.6 }}\r\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\"\r\n          >\r\n            <button\r\n              onClick={handleGetStarted}\r\n              className=\"btn-primary text-lg px-10 py-5 group hover-lift\"\r\n            >\r\n              <Brain className=\"w-6 h-6\" />\r\n              Discover Talent\r\n              <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\r\n            </button>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 40 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1, delay: 0.8 }}\r\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto\"\r\n          >\r\n            <div className=\"glass-card p-8 rounded-2xl text-center hover-lift\">\r\n              <div className=\"icon-container mx-auto\">\r\n                <Brain className=\"w-8 h-8 text-blue-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold mb-3\">AI-Powered Intelligence</h3>\r\n              <p className=\"text-gray-400 text-sm leading-relaxed\">Advanced multi-agent system for comprehensive talent evaluation with unprecedented depth and accuracy</p>\r\n            </div>\r\n            <div className=\"glass-card p-8 rounded-2xl text-center hover-lift\">\r\n              <div className=\"icon-container mx-auto\">\r\n                <Search className=\"w-8 h-8 text-purple-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold mb-3\">Deep Research Engine</h3>\r\n              <p className=\"text-gray-400 text-sm leading-relaxed\">Automated research across LinkedIn, GitHub, and professional platforms for complete talent profiles</p>\r\n            </div>\r\n            <div className=\"glass-card p-8 rounded-2xl text-center hover-lift\">\r\n              <div className=\"icon-container mx-auto\">\r\n                <Target className=\"w-8 h-8 text-cyan-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold mb-3\">Strategic Insights</h3>\r\n              <p className=\"text-gray-400 text-sm leading-relaxed\">Comprehensive analysis with rankings, insights, and strategic hiring recommendations</p>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </motion.section>\r\n\r\n      {/* Features Section */}\r\n      <FeatureSection />\r\n\r\n      {/* Workflow Animation Section */}\r\n      <section className=\"relative z-10 py-32 px-4 section-glow\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-20\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              whileInView={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <Cpu className=\"w-4 h-4 text-purple-400\" />\r\n              <span className=\"text-sm font-medium text-purple-300\">AI-Powered Workflow</span>\r\n            </motion.div>\r\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n              <span className=\"gradient-text\">How TalentSphere Works</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\r\n              Experience our revolutionary multi-agent AI system as it processes talent through \r\n              advanced intelligence pipelines, research networks, and validation protocols\r\n            </p>\r\n          </motion.div>\r\n          \r\n          <WorkflowAnimation />\r\n        </div>\r\n      </section>\r\n\r\n      {/* Performance Stats */}\r\n      <section className=\"relative z-10 py-32 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"glass-intense p-12 lg:p-16 rounded-3xl hover-glow\"\r\n          >\r\n            <div className=\"text-center mb-16\">\r\n              <motion.div\r\n                className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n                initial={{ scale: 0 }}\r\n                whileInView={{ scale: 1 }}\r\n                transition={{ type: \"spring\", stiffness: 200 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                <TrendingUp className=\"w-4 h-4 text-green-400\" />\r\n                <span className=\"text-sm font-medium text-green-300\">Performance Excellence</span>\r\n              </motion.div>\r\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\r\n                <span className=\"gradient-text\">Unmatched Performance</span>\r\n              </h2>\r\n              <p className=\"text-xl text-gray-300 max-width-3xl mx-auto\">\r\n                Built for speed, precision, and scalability with cutting-edge AI optimizations\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\r\n              {[\r\n                { value: \"15x\", label: \"Faster Analysis\", icon: Zap, color: \"text-yellow-400\" },\r\n                { value: \"98%\", label: \"Accuracy Rate\", icon: Target, color: \"text-green-400\" },\r\n                { value: \"7\", label: \"AI Agents\", icon: Brain, color: \"text-purple-400\" },\r\n                { value: \"100+\", label: \"Data Points\", icon: Database, color: \"text-cyan-400\" }\r\n              ].map((stat, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 30 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1, duration: 0.8 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"stat-card hover-lift\"\r\n                >\r\n                  <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-4`} />\r\n                  <div className=\"stat-value\">{stat.value}</div>\r\n                  <div className=\"stat-label\">{stat.label}</div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Enhanced Features Grid */}\r\n      <section className=\"relative z-10 py-32 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-20\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              whileInView={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <Award className=\"w-4 h-4 text-amber-400\" />\r\n              <span className=\"text-sm font-medium text-amber-300\">Advanced Capabilities</span>\r\n            </motion.div>\r\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n              <span className=\"gradient-text\">Revolutionary Features</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\r\n              Comprehensive suite of AI-powered tools designed for the future of talent acquisition\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {enhancedFeatures.map((feature, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 40 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"feature-card hover-lift\"\r\n              >\r\n                <div className=\"icon-container\">\r\n                  <feature.icon className=\"w-8 h-8 text-blue-400\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold mb-4\">{feature.title}</h3>\r\n                <p className=\"text-gray-400 leading-relaxed mb-6\">{feature.description}</p>\r\n                <div className=\"space-y-3\">\r\n                  {feature.benefits.map((benefit, i) => (\r\n                    <div key={i} className=\"flex items-center gap-3\">\r\n                      <CheckCircle className=\"w-4 h-4 text-green-400 flex-shrink-0\" />\r\n                      <span className=\"text-sm text-gray-300\">{benefit}</span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"relative z-10 py-32 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 1 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center glass-intense p-12 lg:p-20 rounded-3xl hover-glow section-glow\"\r\n          >\r\n            <motion.div\r\n              className=\"inline-flex items-center gap-2 px-6 py-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full mb-8\"\r\n              initial={{ scale: 0 }}\r\n              whileInView={{ scale: 1 }}\r\n              transition={{ type: \"spring\", stiffness: 200 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <Sparkles className=\"w-4 h-4 text-cyan-400\" />\r\n              <span className=\"text-sm font-medium text-cyan-300\">Ready to Transform?</span>\r\n            </motion.div>\r\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-8\">\r\n              Revolutionize Your <span className=\"gradient-text\">Talent Strategy</span>\r\n            </h2>\r\n            <p className=\"text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed\">\r\n              Experience the future of talent discovery with our AI-powered platform. \r\n              Unlock unprecedented insights and make data-driven hiring decisions with confidence.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\r\n              <button\r\n                onClick={handleGetStarted}\r\n                className=\"btn-primary text-xl px-12 py-6 group hover-lift\"\r\n              >\r\n                <Brain className=\"w-6 h-6\" />\r\n                Start Discovery\r\n                <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\r\n              </button>\r\n              <button className=\"btn-secondary text-xl px-12 py-6 hover-glow\">\r\n                <Globe className=\"w-6 h-6\" />\r\n                Schedule Demo\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"relative z-10 py-16 border-t border-white/10 px-4\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center\">\r\n            <div className=\"mb-8\">\r\n              <h3 className=\"text-3xl font-bold gradient-text mb-3\">TalentSphere AI</h3>\r\n              <p className=\"text-gray-400 text-lg\">Revolutionary AI-Powered Talent Analysis Platform</p>\r\n            </div>\r\n            <div className=\"flex justify-center gap-8 mb-8\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">15x</div>\r\n                <div className=\"text-sm text-gray-500\">Faster</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">98%</div>\r\n                <div className=\"text-sm text-gray-500\">Accurate</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">7</div>\r\n                <div className=\"text-sm text-gray-500\">AI Agents</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"text-2xl font-bold gradient-text\">100+</div>\r\n                <div className=\"text-sm text-gray-500\">Data Points</div>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-gray-500 text-sm\">\r\n              © 2024 TalentSphere AI. Powered by Advanced Multi-Agent Intelligence.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst enhancedFeatures = [\r\n  {\r\n    icon: Brain,\r\n    title: \"Multi-Agent AI System\",\r\n    description: \"Seven specialized AI agents working in parallel for comprehensive talent analysis with unprecedented depth and accuracy.\",\r\n    benefits: [\r\n      \"GPT-4 powered intelligence\",\r\n      \"Parallel processing architecture\",\r\n      \"98% accuracy rate\",\r\n      \"Semantic understanding\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Search,\r\n    title: \"Deep Research Engine\",\r\n    description: \"Automated research across LinkedIn, GitHub, and professional platforms to build complete talent profiles with strategic insights.\",\r\n    benefits: [\r\n      \"Multi-platform intelligence\",\r\n      \"Real-time data collection\",\r\n      \"Professional verification\",\r\n      \"Social proof analysis\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Shield,\r\n    title: \"Identity Validation\",\r\n    description: \"Advanced validation algorithms ensure talent authenticity through cross-platform verification and consistency analysis.\",\r\n    benefits: [\r\n      \"Multi-factor verification\",\r\n      \"Fraud detection AI\",\r\n      \"Consistency analysis\",\r\n      \"Confidence scoring\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Target,\r\n    title: \"Precision Matching\",\r\n    description: \"Intelligent scoring and ranking against job requirements with semantic analysis and cultural fit assessment.\",\r\n    benefits: [\r\n      \"Semantic matching\",\r\n      \"Cultural fit analysis\",\r\n      \"Skills gap identification\",\r\n      \"Growth potential scoring\"\r\n    ]\r\n  },\r\n  {\r\n    icon: TrendingUp,\r\n    title: \"Predictive Analytics\",\r\n    description: \"Advanced analytics to predict candidate success, retention probability, and career trajectory patterns.\",\r\n    benefits: [\r\n      \"Success prediction\",\r\n      \"Retention modeling\",\r\n      \"Career trajectory analysis\",\r\n      \"Performance forecasting\"\r\n    ]\r\n  },\r\n  {\r\n    icon: Globe,\r\n    title: \"Global Talent Pool\",\r\n    description: \"Access worldwide talent with localized insights, cultural considerations, and regional expertise mapping.\",\r\n    benefits: [\r\n      \"Global reach\",\r\n      \"Cultural intelligence\",\r\n      \"Regional expertise\",\r\n      \"Diversity insights\"\r\n    ]\r\n  }\r\n];\r\n\r\nexport default HomePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,KAAK,EACLC,MAAM,EACNC,WAAW,EAGXC,GAAG,EACHC,UAAU,EAEVC,MAAM,EAENC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,GAAG,QACE,cAAc;AACrB,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACd0B,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BH,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED,oBACEH,OAAA;IAAKO,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBACpDR,OAAA,CAACJ,kBAAkB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtBZ,OAAA;MAAKO,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChC,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBhB,OAAA;QAEEO,SAAS,EAAC,UAAU;QACpBU,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,cAAc,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCE,iBAAiB,EAAE,GAAG,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GANGJ,CAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNZ,OAAA,CAACnB,MAAM,CAAC0C,OAAO;MACbhB,SAAS,EAAC,+EAA+E;MACzFiB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAErB,QAAQ,GAAG,CAAC,GAAG;MAAE,CAAE;MACvCuB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAApB,QAAA,eAE9BR,OAAA;QAAKO,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CR,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAI,CAAE;UACxCxB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAEhBR,OAAA,CAACnB,MAAM,CAACgD,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBN,OAAO,EAAE;cAAEM,KAAK,EAAE;YAAE,CAAE;YACtBL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE,GAAG;cAAEH,KAAK,EAAE;YAAI,CAAE;YAAAvB,QAAA,gBAE3DR,OAAA,CAACX,QAAQ;cAACkB,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CZ,OAAA;cAAMO,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAEbZ,OAAA;YAAIO,SAAS,EAAC,0EAA0E;YAAAC,QAAA,gBACtFR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDZ,OAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNZ,OAAA;cAAMO,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,mGAAmG;YAAAC,QAAA,EAAC;UAGjH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbZ,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAI,CAAE;UACxCxB,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAE7ER,OAAA;YACEmC,OAAO,EAAE7B,gBAAiB;YAC1BC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAE3DR,OAAA,CAACjB,KAAK;cAACwB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAE7B,eAAAZ,OAAA,CAACb,UAAU;cAACoB,SAAS,EAAC;YAAwD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEbZ,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAI,CAAE;UACxCxB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBAEnER,OAAA;YAAKO,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChER,OAAA;cAAKO,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCR,OAAA,CAACjB,KAAK;gBAACwB,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNZ,OAAA;cAAIO,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEZ,OAAA;cAAGO,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAqG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3J,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChER,OAAA;cAAKO,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCR,OAAA,CAAChB,MAAM;gBAACuB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNZ,OAAA;cAAIO,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEZ,OAAA;cAAGO,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAmG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzJ,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChER,OAAA;cAAKO,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCR,OAAA,CAACV,MAAM;gBAACiB,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNZ,OAAA;cAAIO,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DZ,OAAA;cAAGO,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAoF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1I,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBZ,OAAA,CAACH,cAAc;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGlBZ,OAAA;MAASO,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACxDR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BM,WAAW,EAAE;YAAEX,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB/B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BR,OAAA,CAACnB,MAAM,CAACgD,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBI,WAAW,EAAE;cAAEJ,KAAK,EAAE;YAAE,CAAE;YAC1BL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC/CG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBR,OAAA,CAACL,GAAG;cAACY,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CZ,OAAA;cAAMO,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACbZ,OAAA;YAAIO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACjDR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAGvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbZ,OAAA,CAACF,iBAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BM,WAAW,EAAE;YAAEX,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB/B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7DR,OAAA;YAAKO,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCR,OAAA,CAACnB,MAAM,CAACgD,GAAG;cACTtB,SAAS,EAAC,+GAA+G;cACzHiB,OAAO,EAAE;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cACtBI,WAAW,EAAE;gBAAEJ,KAAK,EAAE;cAAE,CAAE;cAC1BL,UAAU,EAAE;gBAAEM,IAAI,EAAE,QAAQ;gBAAEC,SAAS,EAAE;cAAI,CAAE;cAC/CG,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cAAA9B,QAAA,gBAEzBR,OAAA,CAACT,UAAU;gBAACgB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDZ,OAAA;gBAAMO,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACbZ,OAAA;cAAIO,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eACjDR,OAAA;gBAAMO,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACLZ,OAAA;cAAGO,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENZ,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnD,CACC;cAAE+B,KAAK,EAAE,KAAK;cAAEC,KAAK,EAAE,iBAAiB;cAAEC,IAAI,EAAEvD,GAAG;cAAEwD,KAAK,EAAE;YAAkB,CAAC,EAC/E;cAAEH,KAAK,EAAE,KAAK;cAAEC,KAAK,EAAE,eAAe;cAAEC,IAAI,EAAEnD,MAAM;cAAEoD,KAAK,EAAE;YAAiB,CAAC,EAC/E;cAAEH,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,WAAW;cAAEC,IAAI,EAAE1D,KAAK;cAAE2D,KAAK,EAAE;YAAkB,CAAC,EACzE;cAAEH,KAAK,EAAE,MAAM;cAAEC,KAAK,EAAE,aAAa;cAAEC,IAAI,EAAEhD,QAAQ;cAAEiD,KAAK,EAAE;YAAgB,CAAC,CAChF,CAAC5B,GAAG,CAAC,CAAC6B,IAAI,EAAEC,KAAK,kBAChB5C,OAAA,CAACnB,MAAM,CAACgD,GAAG;cAETL,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cAC/BM,WAAW,EAAE;gBAAEX,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAE,CAAE;cAClCH,UAAU,EAAE;gBAAEI,KAAK,EAAEa,KAAK,GAAG,GAAG;gBAAEhB,QAAQ,EAAE;cAAI,CAAE;cAClDS,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB/B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBAEhCR,OAAA,CAAC2C,IAAI,CAACF,IAAI;gBAAClC,SAAS,EAAE,WAAWoC,IAAI,CAACD,KAAK;cAAgB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DZ,OAAA;gBAAKO,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEmC,IAAI,CAACJ;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CZ,OAAA;gBAAKO,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEmC,IAAI,CAACH;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GATzCgC,KAAK;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BM,WAAW,EAAE;YAAEX,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB/B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BR,OAAA,CAACnB,MAAM,CAACgD,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBI,WAAW,EAAE;cAAEJ,KAAK,EAAE;YAAE,CAAE;YAC1BL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC/CG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBR,OAAA,CAACR,KAAK;cAACe,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5CZ,OAAA;cAAMO,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACbZ,OAAA;YAAIO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACjDR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbZ,OAAA;UAAKO,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEqC,gBAAgB,CAAC/B,GAAG,CAAC,CAACgC,OAAO,EAAEF,KAAK,kBACnC5C,OAAA,CAACnB,MAAM,CAACgD,GAAG;YAETL,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAG,CAAE;YAC/BM,WAAW,EAAE;cAAEX,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEa,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB/B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAEnCR,OAAA;cAAKO,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BR,OAAA,CAAC8C,OAAO,CAACL,IAAI;gBAAClC,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNZ,OAAA;cAAIO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEsC,OAAO,CAACC;YAAK;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5DZ,OAAA;cAAGO,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEsC,OAAO,CAACE;YAAW;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EZ,OAAA;cAAKO,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBsC,OAAO,CAACG,QAAQ,CAACnC,GAAG,CAAC,CAACoC,OAAO,EAAElC,CAAC,kBAC/BhB,OAAA;gBAAaO,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBAC9CR,OAAA,CAACf,WAAW;kBAACsB,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEZ,OAAA;kBAAMO,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE0C;gBAAO;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFhDI,CAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAnBDgC,KAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAASO,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAC3CR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA,CAACnB,MAAM,CAACgD,GAAG;UACTL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BM,WAAW,EAAE;YAAEX,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCH,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzB/B,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBAEtFR,OAAA,CAACnB,MAAM,CAACgD,GAAG;YACTtB,SAAS,EAAC,+GAA+G;YACzHiB,OAAO,EAAE;cAAEQ,KAAK,EAAE;YAAE,CAAE;YACtBI,WAAW,EAAE;cAAEJ,KAAK,EAAE;YAAE,CAAE;YAC1BL,UAAU,EAAE;cAAEM,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC/CG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAA9B,QAAA,gBAEzBR,OAAA,CAACX,QAAQ;cAACkB,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CZ,OAAA;cAAMO,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACbZ,OAAA;YAAIO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,qBAC/B,eAAAR,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAG7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YAAKO,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1ER,OAAA;cACEmC,OAAO,EAAE7B,gBAAiB;cAC1BC,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAE3DR,OAAA,CAACjB,KAAK;gBAACwB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE7B,eAAAZ,OAAA,CAACb,UAAU;gBAACoB,SAAS,EAAC;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACTZ,OAAA;cAAQO,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC7DR,OAAA,CAACN,KAAK;gBAACa,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVZ,OAAA;MAAQO,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eACnER,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BR,OAAA;YAAKO,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBR,OAAA;cAAIO,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EZ,OAAA;cAAGO,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CR,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DZ,OAAA;gBAAKO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DZ,OAAA;gBAAKO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzDZ,OAAA;gBAAKO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNZ,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BR,OAAA;gBAAKO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5DZ,OAAA;gBAAKO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACV,EAAA,CAnVID,QAAkB;EAAA,QACLnB,WAAW;AAAA;AAAAqE,EAAA,GADxBlD,QAAkB;AAqVxB,MAAM4C,gBAAgB,GAAG,CACvB;EACEJ,IAAI,EAAE1D,KAAK;EACXgE,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,0HAA0H;EACvIC,QAAQ,EAAE,CACR,4BAA4B,EAC5B,kCAAkC,EAClC,mBAAmB,EACnB,wBAAwB;AAE5B,CAAC,EACD;EACER,IAAI,EAAEzD,MAAM;EACZ+D,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,mIAAmI;EAChJC,QAAQ,EAAE,CACR,6BAA6B,EAC7B,2BAA2B,EAC3B,2BAA2B,EAC3B,uBAAuB;AAE3B,CAAC,EACD;EACER,IAAI,EAAErD,MAAM;EACZ2D,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE,yHAAyH;EACtIC,QAAQ,EAAE,CACR,2BAA2B,EAC3B,oBAAoB,EACpB,sBAAsB,EACtB,oBAAoB;AAExB,CAAC,EACD;EACER,IAAI,EAAEnD,MAAM;EACZyD,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,8GAA8G;EAC3HC,QAAQ,EAAE,CACR,mBAAmB,EACnB,uBAAuB,EACvB,2BAA2B,EAC3B,0BAA0B;AAE9B,CAAC,EACD;EACER,IAAI,EAAElD,UAAU;EAChBwD,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,yGAAyG;EACtHC,QAAQ,EAAE,CACR,oBAAoB,EACpB,oBAAoB,EACpB,4BAA4B,EAC5B,yBAAyB;AAE7B,CAAC,EACD;EACER,IAAI,EAAE/C,KAAK;EACXqD,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,2GAA2G;EACxHC,QAAQ,EAAE,CACR,cAAc,EACd,uBAAuB,EACvB,oBAAoB,EACpB,oBAAoB;AAExB,CAAC,CACF;AAED,eAAehD,QAAQ;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}